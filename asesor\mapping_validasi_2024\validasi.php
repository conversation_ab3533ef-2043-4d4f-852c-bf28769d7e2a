<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level As<PERSON>or
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');
?>

<?php include '../header.php'; ?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Mapping Validasi Dasmen IASP 2024</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../dashboard.php">Home</a></li>
                        <li class="breadcrumb-item active">Mapping Validasi</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-clipboard-check mr-2"></i>
                                Data Mapping Validasi Dasmen IASP 2024
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- Alert Container -->
                            <div id="alert-container"></div>
                            
                            <?php
                            // Get session variables
                            $kd_user = $_SESSION['kd_user'];
                            $provinsi_id = $_SESSION['provinsi_id'];
                            $nomor = 0;
                            
                            // Get tahun from mapping_validasi_tahun_2024
                            $tahun = "SELECT mapping_validasi_tahun_2024.nama_tahun FROM mapping_validasi_tahun_2024 WHERE mapping_validasi_tahun_2024.provinsi_id='$provinsi_id'";
                            $result_tahun = $conn->query($tahun);
                            
                            if($result_tahun->num_rows > 0) {
                                while($row_tahun = $result_tahun->fetch_assoc()) {
                                    $nama_tahun = $row_tahun['nama_tahun'];
                                    ?>
                                    
                                    <div class="mb-3">
                                        <h5 class="text-primary">
                                            <i class="fas fa-calendar-alt mr-2"></i>
                                            Tahun Akreditasi: <?php echo $nama_tahun; ?>
                                        </h5>
                                    </div>
                                    
                                    <?php
                                    // Optimized query with proper JOINs
                                    $sql = "SELECT 
                                                mv.id_mapping, 
                                                mv.sekolah_id,
                                                mv.kd_asesor1, 
                                                mv.kd_asesor2,
                                                mv.tahap,
                                                mv.tgl_mulai_validasi,
                                                mv.tgl_akhir_validasi,
                                                mv.file_format_5_1_berita_acara_hasil_validasi_1,
                                                mv.file_format_5_1_berita_acara_hasil_validasi_2,
                                                mv.tahun_akreditasi,
                                                s.npsn, 
                                                s.nama_sekolah,
                                                s.nama_kepsek, 
                                                s.no_hp_kepsek,
                                                j.nm_jenjang,
                                                kk.nm_kota as sekolah_kota,
                                                a1.nia1, 
                                                a1.nm_asesor1, 
                                                a1.no_hp as hp1,
                                                kk1.nm_kota as kota1,
                                                a2.nia2, 
                                                a2.nm_asesor2, 
                                                a2.no_hp as hp2,
                                                kk2.nm_kota as kota2
                                            FROM mapping_validasi_2024 mv
                                            LEFT JOIN sekolah s ON mv.sekolah_id = s.sekolah_id
                                            LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                                            LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
                                            LEFT JOIN asesor_1 a1 ON mv.kd_asesor1 = a1.kd_asesor1 AND a1.soft_delete = '1'
                                            LEFT JOIN kab_kota kk1 ON a1.kota_id1 = kk1.kota_id
                                            LEFT JOIN asesor_2 a2 ON mv.kd_asesor2 = a2.kd_asesor2 AND a2.soft_delete = '1'
                                            LEFT JOIN kab_kota kk2 ON a2.kota_id2 = kk2.kota_id
                                            WHERE (mv.kd_asesor1 = '$kd_user' OR mv.kd_asesor2 = '$kd_user')
                                                AND mv.tahun_akreditasi = '$nama_tahun'
                                                AND mv.provinsi_id = '$provinsi_id'
                                                AND s.soft_delete = '1'
                                            ORDER BY s.nama_sekolah ASC";
                                    
                                    $result = $conn->query($sql);
                                    
                                    if($result->num_rows > 0) {
                                        ?>
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped table-hover" style="font-size: 13px;">
                                                <thead style="background-color: #007bff; color: white;">
                                                    <tr>
                                                        <th class="text-center" style="width: 50px;">NO</th>
                                                        <th class="text-center" style="width: 200px;">SEKOLAH</th>
                                                        <th class="text-center" style="width: 250px;">VALIDATOR</th>
                                                        <th class="text-center" style="width: 200px;">FORM UPLOAD DOKUMEN</th>
                                                        <th class="text-center" style="width: 200px;">DOKUMEN UNGGAHAN</th>
                                                        <th class="text-center" style="width: 200px;">JADWAL DAN AKSI</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php
                                                    while($row = $result->fetch_assoc()) {
                                                        $nomor++;
                                                        $id_mapping = $row['id_mapping'];
                                                        $asesor1 = $row['kd_asesor1'];
                                                        $asesor2 = $row['kd_asesor2'];
                                                        $file_format_5_1_berita_acara_hasil_validasi_1 = $row['file_format_5_1_berita_acara_hasil_validasi_1'];
                                                        $file_format_5_1_berita_acara_hasil_validasi_2 = $row['file_format_5_1_berita_acara_hasil_validasi_2'];
                                                        ?>
                                                        <tr>
                                                            <!-- NO -->
                                                            <td class="text-center align-top" style="vertical-align: top; padding-top: 8px;"><?php echo $nomor; ?></td>
                                                            
                                                            <!-- SEKOLAH -->
                                                            <td class="align-top text-left">
                                                                <div style="font-size: 13px; line-height: 1.4;">
                                                                    <strong>NPSN:</strong> <?php echo $row['npsn']; ?><br>
                                                                    <strong>NAMA:</strong> <?php echo $row['nama_sekolah']; ?><br>
                                                                    <strong>JENJANG:</strong> <?php echo $row['nm_jenjang']; ?><br>
                                                                    <strong>KAB/KOTA:</strong> <?php echo $row['sekolah_kota']; ?><br>
                                                                    <strong>NAMA KEPSEK:</strong> <?php echo $row['nama_kepsek']; ?><br>
                                                                    <strong>NO HP KEPSEK:</strong> <?php echo $row['no_hp_kepsek']; ?><br>
                                                                    <strong>TAHAP VALIDASI:</strong> <?php echo $row['tahap']; ?>
                                                                </div>
                                                            </td>
                                                            
                                                            <!-- VALIDATOR -->
                                                            <td class="align-top text-left">
                                                                <div style="font-size: 13px; line-height: 1.4;">
                                                                    <div class="mb-2">
                                                                        <span class="badge badge-primary" style="font-size: 11px; margin-bottom: 5px;">VALIDATOR 1</span><br>
                                                                        <strong>NIA:</strong> <?php echo $row['nia1']; ?><br>
                                                                        <strong>NAMA:</strong> <?php echo $row['nm_asesor1']; ?><br>
                                                                        <strong>KAB/KOTA:</strong> <?php echo $row['kota1']; ?><br>
                                                                        <strong>NO HP:</strong> <?php echo $row['hp1']; ?>
                                                                    </div>
                                                                    <div>
                                                                        <span class="badge badge-success" style="font-size: 11px; margin-bottom: 5px;">VALIDATOR 2</span><br>
                                                                        <strong>NIA:</strong> <?php echo $row['nia2']; ?><br>
                                                                        <strong>NAMA:</strong> <?php echo $row['nm_asesor2']; ?><br>
                                                                        <strong>KAB/KOTA:</strong> <?php echo $row['kota2']; ?><br>
                                                                        <strong>NO HP:</strong> <?php echo $row['hp2']; ?>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            
                                                            <!-- FORM UPLOAD DOKUMEN -->
                                                            <td class="align-top text-left">
                                                                <div style="font-size: 11px; line-height: 1.3;">
                                                                    <?php if ($kd_user == $asesor1): ?>
                                                                        <!-- Validator 1 Upload Button -->
                                                                        <button type="button" class="btn btn-primary btn-xs mb-1" style="font-size: 10px; padding: 3px 6px;" 
                                                                                onclick="uploadFile(<?php echo $id_mapping; ?>, 'format_5_1_berita_acara_hasil_validasi_1')">
                                                                            Upload File Format 5.1 Berita Acara Hasil Validasi 1
                                                                        </button><br>
                                                                    <?php endif; ?>
                                                                    
                                                                    <?php if ($kd_user == $asesor2): ?>
                                                                        <!-- Validator 2 Upload Button -->
                                                                        <button type="button" class="btn btn-success btn-xs mb-1" style="font-size: 10px; padding: 3px 6px;" 
                                                                                onclick="uploadFile(<?php echo $id_mapping; ?>, 'format_5_1_berita_acara_hasil_validasi_2')">
                                                                            Upload File Format 5.1 Berita Acara Hasil Validasi 2
                                                                        </button><br>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </td>
                                                            
                                                            <!-- DOKUMEN UNGGAHAN -->
                                                            <td class="align-top text-left">
                                                                <div style="font-size: 12px; line-height: 1.3;">
                                                                    <?php if ($kd_user == $asesor1): ?>
                                                                        <!-- File Format 5.1 Berita Acara Hasil Validasi 1 -->
                                                                        <div class="mb-1">
                                                                            <span style="font-weight: bold;">File Format 5.1 Berita Acara Hasil Validasi 1:</span><br>
                                                                            <?php
                                                                            // Status upload mengikuti pola KPA PAUD
                                                                            $upload_status_1 = '';
                                                                            $upload_class_1 = '';
                                                                            $onclick_attr_1 = '';
                                                                            $title_attr_1 = '';
                                                                            $cursor_style_1 = '';

                                                                            if (!empty($file_format_5_1_berita_acara_hasil_validasi_1)) {
                                                                                $upload_status_1 = '<i class="fas fa-eye mr-1"></i>Sudah Upload';
                                                                                $upload_class_1 = 'badge-success';
                                                                                $onclick_attr_1 = 'onclick="previewFile(\'' . htmlspecialchars($file_format_5_1_berita_acara_hasil_validasi_1) . '\')"';
                                                                                $title_attr_1 = 'title="Klik untuk melihat file"';
                                                                                $cursor_style_1 = 'cursor: pointer;';
                                                                            } else {
                                                                                $upload_status_1 = 'Belum Upload';
                                                                                $upload_class_1 = 'badge-danger';
                                                                            }
                                                                            ?>
                                                                            <span class="badge <?php echo $upload_class_1; ?> p-2"
                                                                                  id="badge-<?php echo $id_mapping; ?>-format_5_1_berita_acara_hasil_validasi_1"
                                                                                  style="<?php echo $cursor_style_1; ?>"
                                                                                  <?php echo $onclick_attr_1; ?>
                                                                                  <?php echo $title_attr_1; ?>>
                                                                                <?php echo $upload_status_1; ?>
                                                                            </span>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                    
                                                                    <?php if ($kd_user == $asesor2): ?>
                                                                        <!-- File Format 5.1 Berita Acara Hasil Validasi 2 -->
                                                                        <div class="mb-1">
                                                                            <span style="font-weight: bold;">File Format 5.1 Berita Acara Hasil Validasi 2:</span><br>
                                                                            <?php
                                                                            // Status upload mengikuti pola KPA PAUD
                                                                            $upload_status_2 = '';
                                                                            $upload_class_2 = '';
                                                                            $onclick_attr_2 = '';
                                                                            $title_attr_2 = '';
                                                                            $cursor_style_2 = '';

                                                                            if (!empty($file_format_5_1_berita_acara_hasil_validasi_2)) {
                                                                                $upload_status_2 = '<i class="fas fa-eye mr-1"></i>Sudah Upload';
                                                                                $upload_class_2 = 'badge-success';
                                                                                $onclick_attr_2 = 'onclick="previewFile(\'' . htmlspecialchars($file_format_5_1_berita_acara_hasil_validasi_2) . '\')"';
                                                                                $title_attr_2 = 'title="Klik untuk melihat file"';
                                                                                $cursor_style_2 = 'cursor: pointer;';
                                                                            } else {
                                                                                $upload_status_2 = 'Belum Upload';
                                                                                $upload_class_2 = 'badge-danger';
                                                                            }
                                                                            ?>
                                                                            <span class="badge <?php echo $upload_class_2; ?> p-2"
                                                                                  id="badge-<?php echo $id_mapping; ?>-format_5_1_berita_acara_hasil_validasi_2"
                                                                                  style="<?php echo $cursor_style_2; ?>"
                                                                                  <?php echo $onclick_attr_2; ?>
                                                                                  <?php echo $title_attr_2; ?>>
                                                                                <?php echo $upload_status_2; ?>
                                                                            </span>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </td>
                                                            
                                                            <!-- JADWAL DAN AKSI -->
                                                            <td class="align-top text-left">
                                                                <div style="font-size: 12px; line-height: 1.3;">
                                                                    <div class="mb-2">
                                                                        <strong>Tanggal Mulai Validasi:</strong><br>
                                                                        <?php 
                                                                        if (!empty($row['tgl_mulai_validasi']) && $row['tgl_mulai_validasi'] != '0000-00-00') {
                                                                            echo date('d-m-Y', strtotime($row['tgl_mulai_validasi']));
                                                                        } else {
                                                                            echo '-';
                                                                        }
                                                                        ?>
                                                                    </div>
                                                                    
                                                                    <div class="mb-2">
                                                                        <strong>Tanggal Akhir Validasi:</strong><br>
                                                                        <?php 
                                                                        if (!empty($row['tgl_akhir_validasi']) && $row['tgl_akhir_validasi'] != '0000-00-00') {
                                                                            echo date('d-m-Y', strtotime($row['tgl_akhir_validasi']));
                                                                        } else {
                                                                            echo '-';
                                                                        }
                                                                        ?>
                                                                    </div>
                                                                    
                                                                    <div>
                                                                        <button type="button" class="btn btn-info btn-xs" style="font-size: 10px; padding: 3px 6px;" 
                                                                                onclick="downloadSuratTugasValidasi(<?php echo $id_mapping; ?>)">
                                                                            <i class="fas fa-download"></i> Download Surat Tugas Validasi
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        <?php
                                                    }
                                                    ?>
                                                </tbody>
                                            </table>
                                        </div>
                                        <?php
                                    } else {
                                        ?>
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle mr-2"></i>
                                            Tidak ada data mapping validasi untuk tahun <?php echo $nama_tahun; ?>.
                                        </div>
                                        <?php
                                    }
                                }
                            } else {
                                ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                    Tidak ada data tahun akreditasi untuk provinsi ini.
                                </div>
                                <?php
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Modal Upload File -->
<div class="modal fade" id="modal-upload" tabindex="-1" role="dialog" aria-labelledby="modal-upload-label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-upload-label">
                    <i class="fas fa-upload mr-2"></i>Upload File Validasi
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-upload" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="upload-id-mapping" name="id_mapping">
                    <input type="hidden" id="upload-file-type" name="file_type">

                    <div class="form-group">
                        <label for="file-upload">Pilih File (PDF Only):</label>
                        <input type="file" class="form-control-file" id="file-upload" name="file_validasi" accept=".pdf" required>
                        <small class="form-text text-muted">Format file yang diizinkan: PDF (maksimal 10MB)</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times mr-1"></i>Batal
                    </button>
                    <button type="button" class="btn btn-primary" id="btn-upload" onclick="uploadFileToServer()">
                        <i class="fas fa-upload mr-1"></i>Upload
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Preview File -->
<div class="modal fade" id="modal-preview" tabindex="-1" role="dialog" aria-labelledby="modal-preview-label" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-preview-label">
                    <i class="fas fa-eye mr-2"></i>Preview File
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <iframe id="preview-frame" src="" style="width: 100%; height: 600px; border: none;"></iframe>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times mr-1"></i>Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Include JavaScript -->
<script src="js/mapping_validasi.js"></script>

<!-- Include footer -->
<?php include '../footer.php'; ?>
