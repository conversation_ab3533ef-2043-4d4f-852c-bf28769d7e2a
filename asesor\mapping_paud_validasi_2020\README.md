# Modul Mapping Validasi PAUD 2020 - Asesor

## 📋 **OVERVIEW**

Modul ini menampilkan data mapping validasi PAUD untuk asesor yang sedang login. Asesor dapat melihat sekolah-sekolah PAUD yang telah di-assign untuk proses validasi akreditasi dan mengelola catatan penilaian serta upload file PHA (Penjelasan Hasil Akred<PERSON>i).

## 📁 **STRUKTUR FILE**

```
asesor/mapping_paud_validasi_2020/
├── validasi_paud_2020.php          # Main module file
├── ajax/
│   ├── upload_file_pha.php         # Upload file PHA handler
│   ├── simpan_catatan_kpa.php      # Save catatan asesor KPA
│   ├── simpan_catatan_visitasi_a.php # Save catatan asesor visitasi A
│   ├── simpan_catatan_visitasi_b.php # Save catatan asesor visitasi B
│   └── simpan_catatan_validator.php # Save catatan validator
├── js/
│   └── validasi.js                 # JavaScript functionality
└── README.md                       # This documentation
```

## 🎯 **FITUR UTAMA**

### **1. Tabel Data Mapping (5 Kolom)**
- **NO**: Nomor urut auto increment
- **SEKOLAH**: Informasi lengkap sekolah (NPSN, Nama, Jenjang, Kab/Kota, Kepsek, HP, Tahap)
- **ASESOR**: Informasi Validator & Verifikator (NIA, Nama, Kab/Kota)
- **FORM UPLOAD DOKUMEN**: Tombol upload dan form catatan conditional berdasarkan asesor
- **DOKUMEN PHA**: Status file Penjelasan Hasil Akreditasi dengan badge hijau/merah

### **2. Role-Based Access Control**

#### **Asesor 1 (Validator) - 4 Fitur:**
- **Upload File PHA** - Upload file PDF ke direktori validasi
- **Form Catatan Asesor KPA** - Input nama dan catatan penilaian asesor KPA
- **Form Catatan Asesor Visitasi A** - Input nama dan catatan penilaian asesor visitasi A
- **Form Catatan Asesor Visitasi B** - Input nama dan catatan penilaian asesor visitasi B

#### **Asesor 2 (Verifikator) - 1 Fitur:**
- **Form Catatan Validator** - Input nama validator, nilai validasi, dan catatan penilaian

### **3. Upload System File PHA**
- **File Type**: PDF only dengan validasi MIME type
- **File Size**: Maksimal 10MB
- **Progress Bar**: Real-time upload progress tracking
- **Replace File**: Otomatis replace file lama jika ada
- **Upload Directory**: `../../../../simak/files/upload_file_hasil_validasi_paud/`

### **4. Form Management**
- **Auto-Fill Data**: Form menampilkan data existing jika sudah ada (update mode)
- **Manual Input**: Field nama asesor bisa diketik manual
- **Textarea**: Catatan penilaian menggunakan textarea biasa
- **No Validation**: Tidak ada validasi required untuk field-field

## 🔍 **DATABASE QUERY**

### **Main Query dengan JOIN Optimized:**
```sql
SELECT mapping_paud_validasi.id_mapping, sekolah.sekolah_id, sekolah.npsn, sekolah.nama_sekolah,
       sekolah.nama_kepsek, sekolah.no_hp_kepsek, sekolah.no_wa_kepsek, sekolah.jenjang_id, jenjang.nm_jenjang,
       sekolah.kota_id, kab_kota.nm_kota, mapping_paud_validasi.kd_asesor1, mapping_paud_validasi.kd_asesor2,
       mapping_paud_validasi.tahap, mapping_paud_validasi.file_penjelasan_hasil_akreditasi,
       mapping_paud_validasi.nama_asesor_kpa, mapping_paud_validasi.catatan_penilaian_asesor_kpa,
       mapping_paud_validasi.nama_asesor_visitasi_a, mapping_paud_validasi.catatan_penilaian_asesor_visitasi_a,
       mapping_paud_validasi.nama_asesor_visitasi_b, mapping_paud_validasi.catatan_penilaian_asesor_visitasi_b,
       mapping_paud_validasi.nama_validator, mapping_paud_validasi.nilai_validasi, 
       mapping_paud_validasi.catatan_penilaian_validator,
       asesor_1.nia1, asesor_1.nm_asesor1, kk1.nm_kota as kota1,
       asesor_2.nia2, asesor_2.nm_asesor2, kk2.nm_kota as kota2,
       mapping_paud_validasi.tahun_akreditasi
FROM mapping_paud_validasi
LEFT JOIN sekolah ON mapping_paud_validasi.sekolah_id=sekolah.sekolah_id
LEFT JOIN jenjang ON sekolah.jenjang_id=jenjang.jenjang_id
LEFT JOIN kab_kota ON sekolah.kota_id=kab_kota.kota_id
LEFT JOIN asesor_1 ON mapping_paud_validasi.kd_asesor1=asesor_1.kd_asesor1
LEFT JOIN kab_kota kk1 ON asesor_1.kota_id1=kk1.kota_id
LEFT JOIN asesor_2 ON mapping_paud_validasi.kd_asesor2=asesor_2.kd_asesor2
LEFT JOIN kab_kota kk2 ON asesor_2.kota_id2=kk2.kota_id
WHERE (kd_asesor1='$kd_user' OR kd_asesor2='$kd_user')
    AND mapping_paud_validasi.tahun_akreditasi='$nama_tahun'
    AND mapping_paud_validasi.provinsi_id='$provinsi_id'
```

## 🔐 **KONTROL AKSES & KEAMANAN**

### **Session Requirements:**
```php
$_SESSION['kd_user']     // Kode asesor yang login
$_SESSION['provinsi_id'] // ID provinsi asesor
$_SESSION['level']       // Harus 'Asesor'
```

### **Access Control:**
- **Data Filtering**: Hanya data mapping yang melibatkan asesor login
- **File Upload**: Hanya asesor1 yang bisa upload file PHA
- **Form Access**: Role-based access untuk setiap form catatan
- **File Ownership**: Validasi kepemilikan sebelum upload/update

## 🎨 **UI COMPONENTS**

### **Responsive Table:**
- **5 Kolom Layout** dengan width yang proporsional
- **Horizontal Scroll** untuk mobile devices
- **Font Size 13px** untuk muat banyak informasi
- **Color-coded Buttons** untuk berbagai jenis form

### **Modal System:**
- **5 Modal Berbeda** untuk setiap jenis form
- **Progress Bar** untuk upload file
- **Auto-fill Data** untuk mode update
- **Responsive Design** dengan Bootstrap

### **Status Badges:**
- **Sudah Upload**: Green badge (clickable untuk preview)
- **Belum Upload**: Red badge (tidak clickable)
- **File Preview**: Buka PDF di tab baru

## 📱 **JAVASCRIPT FUNCTIONALITY**

### **Upload Functions:**
- `uploadFilePHA(idMapping)` - Show upload modal
- `handleFileUpload()` - Process AJAX upload dengan progress bar
- `previewFile(filename)` - Preview file di tab baru

### **Form Functions:**
- `formCatatanKPA()` - Show form catatan KPA dengan auto-fill
- `formCatatanVisitasiA()` - Show form catatan visitasi A
- `formCatatanVisitasiB()` - Show form catatan visitasi B
- `formCatatanValidator()` - Show form catatan validator

### **Save Functions:**
- `simpanCatatanKPA()` - Save catatan KPA via AJAX
- `simpanCatatanVisitasiA()` - Save catatan visitasi A
- `simpanCatatanVisitasiB()` - Save catatan visitasi B
- `simpanCatatanValidator()` - Save catatan validator

### **Utility Functions:**
- `showAlert(type, message)` - Alert notification system
- `resetModalState()` - Reset modal saat ditutup
- `updateFileLabel()` - Update file input label

## 🚀 **FEATURES SUMMARY**

### **File Upload System:**
- ✅ **PDF Upload** dengan validasi MIME type dan size
- ✅ **Progress Bar** real-time tracking
- ✅ **File Replace** otomatis untuk file lama
- ✅ **Security Validation** berdasarkan asesor role

### **Form Management:**
- ✅ **Role-based Access** (Asesor 1 vs Asesor 2)
- ✅ **Auto-fill Data** untuk mode update
- ✅ **Manual Input** untuk nama asesor
- ✅ **AJAX Submission** tanpa reload halaman

### **User Experience:**
- ✅ **Responsive Design** untuk semua device
- ✅ **Visual Indicators** dengan color-coded buttons
- ✅ **File Preview** dengan secure handler
- ✅ **Real-time Updates** setelah save

## 🔍 **TESTING**

### **Test Cases:**
1. **Asesor 1 Login**: Verify 4 tombol form + upload PHA
2. **Asesor 2 Login**: Verify 1 tombol form catatan validator
3. **File Upload**: Test PDF upload dengan progress bar
4. **File Preview**: Test preview untuk uploaded files
5. **Form Save**: Test setiap jenis form catatan
6. **Auto-fill**: Test form dengan data existing

### **Security Tests:**
- File ownership validation
- Role-based access control
- Session-based filtering
- File type dan size validation

---
**Version**: 1.0.1
**Status**: Production Ready ✅
**Features**: Complete Upload & Form System
**Database**: Menggunakan asesor_1 dan asesor_2 tables
**Files**: Role-based access dengan auto-fill data
**Debug**: Cleaned - No debug code in production
**Last Updated**: 2024-12-19
