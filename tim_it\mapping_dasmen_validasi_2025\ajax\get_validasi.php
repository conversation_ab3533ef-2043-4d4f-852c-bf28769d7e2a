<?php
/**
 * AJAX handler untuk mengambil data mapping validasi SM
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Parameter DataTables
    $draw = isset($_POST['draw']) ? intval($_POST['draw']) : 1;
    $start = isset($_POST['start']) ? intval($_POST['start']) : 0;
    $length = isset($_POST['length']) ? intval($_POST['length']) : 10;
    $search_value = isset($_POST['search']['value']) ? $_POST['search']['value'] : '';
    
    // Kolom untuk ordering
    $columns = [
        0 => 'mv.id_mapping',
        1 => 's.npsn',
        2 => 's.nama_sekolah',
        3 => 'j.nm_jenjang',
        4 => 's.rumpun',
        5 => 'k.nm_kota',
        6 => 'a1.nia1',
        7 => 'a1.nm_asesor1',
        8 => 'a2.nia2',
        9 => 'a2.nm_asesor2',
        10 => 'mv.tahun_akreditasi',
        11 => 'mv.tahap'
    ];
    
    $order_column = isset($_POST['order'][0]['column']) ? intval($_POST['order'][0]['column']) : 0;
    $order_dir = isset($_POST['order'][0]['dir']) ? $_POST['order'][0]['dir'] : 'asc';
    $order_by = isset($columns[$order_column]) ? $columns[$order_column] : 'mv.id_mapping';
    
    // Base WHERE clause sederhana - KISS principle
    $base_where = "WHERE mv.tahun_akreditasi = mvt.nama_tahun
                   AND s.rumpun = 'dasmen'
                   AND mvt.provinsi_id = '$provinsi_id_session'
                   AND mv.provinsi_id = '$provinsi_id_session'";

    // Tambahkan search jika ada
    $search_where = "";
    if (!empty($search_value)) {
        $search = $conn->real_escape_string($search_value);
        $search_where = " AND (
            s.npsn LIKE '%$search%' OR
            s.nama_sekolah LIKE '%$search%' OR
            j.nm_jenjang LIKE '%$search%' OR
            k.nm_kota LIKE '%$search%' OR
            s.rumpun LIKE '%$search%' OR
            mv.tahun_akreditasi LIKE '%$search%' OR
            a1.nia1 LIKE '%$search%' OR
            a1.nm_asesor1 LIKE '%$search%' OR
            a2.nia2 LIKE '%$search%' OR
            a2.nm_asesor2 LIKE '%$search%' OR
            mv.tahap LIKE '%$search%'
        )";
    }
    
    // Count total records
    $count_sql = "SELECT COUNT(*) as total
                  FROM mapping_validasi_2025 mv
                  LEFT JOIN sekolah s ON mv.sekolah_id = s.sekolah_id
                  LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                  LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
                  LEFT JOIN asesor_1 a1 ON mv.kd_asesor1 = a1.kd_asesor1
                  LEFT JOIN asesor_2 a2 ON mv.kd_asesor2 = a2.kd_asesor2
                  LEFT JOIN mapping_validasi_tahun_2025 mvt ON mv.tahun_akreditasi = mvt.nama_tahun
                  $base_where";

    $count_result = $conn->query($count_sql);
    $total_records = $count_result ? $count_result->fetch_assoc()['total'] : 0;

    // Count filtered records
    $filtered_sql = "SELECT COUNT(*) as total
                     FROM mapping_validasi_2025 mv
                     LEFT JOIN sekolah s ON mv.sekolah_id = s.sekolah_id
                     LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                     LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
                     LEFT JOIN asesor_1 a1 ON mv.kd_asesor1 = a1.kd_asesor1
                     LEFT JOIN asesor_2 a2 ON mv.kd_asesor2 = a2.kd_asesor2
                     LEFT JOIN mapping_validasi_tahun_2025 mvt ON mv.tahun_akreditasi = mvt.nama_tahun
                     $base_where $search_where";

    $filtered_result = $conn->query($filtered_sql);
    $filtered_records = $filtered_result ? $filtered_result->fetch_assoc()['total'] : 0;

    // Main query
    $sql = "SELECT mv.id_mapping, mv.sekolah_id, s.npsn, s.nama_sekolah, j.nm_jenjang,
                   k.nm_kota, s.rumpun, mvt.nama_tahun, mv.tahun_akreditasi, a1.nia1,
                   a1.nm_asesor1, a2.nia2, a2.nm_asesor2, mv.tahap
            FROM mapping_validasi_2025 mv
            LEFT JOIN sekolah s ON mv.sekolah_id = s.sekolah_id
            LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
            LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
            LEFT JOIN asesor_1 a1 ON mv.kd_asesor1 = a1.kd_asesor1
            LEFT JOIN asesor_2 a2 ON mv.kd_asesor2 = a2.kd_asesor2
            LEFT JOIN mapping_validasi_tahun_2025 mvt ON mv.tahun_akreditasi = mvt.nama_tahun
            $base_where $search_where
            ORDER BY s.nama_sekolah ASC
            LIMIT $start, $length";

    $result = $conn->query($sql);
    
    $data = [];
    $no = $start + 1;

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            // Format tahap - tampilkan nilai asli dari database
            $tahap_text = $row['tahap'] ?? '-';

            // Tombol aksi
            $aksi = '<div class="btn-group" role="group">
                        <button type="button" class="btn btn-info btn-sm" onclick="detailValidasi(' . $row['id_mapping'] . ')" title="Detail">
                            <i class="fas fa-eye"></i>
                        </button>
                     </div>';

            $data[] = [
                $no++,
                $row['npsn'] ?? '-',
                $row['nama_sekolah'] ?? '-',
                $row['nm_jenjang'] ?? '-',
                $row['rumpun'] ?? '-',
                $row['nm_kota'] ?? '-',
                $row['nia1'] ?? '-',
                $row['nm_asesor1'] ?? '-',
                $row['nia2'] ?? '-',
                $row['nm_asesor2'] ?? '-',
                $row['tahun_akreditasi'] ?? '-',
                $tahap_text,
                $aksi
            ];
        }
    }
    
    // Response untuk DataTables
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $filtered_records,
        'data' => $data
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Error response
    $response = [
        'draw' => isset($draw) ? $draw : 1,
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => 'Terjadi kesalahan: ' . $e->getMessage()
    ];
    
    echo json_encode($response);
    error_log("Error in get_validasi.php: " . $e->getMessage());
}
?>
