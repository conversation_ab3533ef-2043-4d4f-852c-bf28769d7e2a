<?php
/**
 * AJAX handler untuk upload file <PERSON><PERSON><PERSON><PERSON> - Mapping Validasi PAUD
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Asesor', '../../../login.php');

header('Content-Type: application/json');

try {
    // Validasi request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }
    
    // Validasi input
    if (!isset($_POST['id_mapping']) || empty($_POST['id_mapping'])) {
        throw new Exception('ID Mapping tidak valid');
    }
    
    if (!isset($_FILES['file_penjelasan_hasil_akreditasi']) || $_FILES['file_penjelasan_hasil_akreditasi']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File tidak valid atau gagal diupload');
    }

    // Get data from POST - KISS: hanya yang diperlukan
    $id_mapping = mysqli_real_escape_string($conn, $_POST['id_mapping']);

    // KISS: Validasi file sederhana
    $file_extension = strtolower(pathinfo($_FILES['file_penjelasan_hasil_akreditasi']['name'], PATHINFO_EXTENSION));
    if ($file_extension !== 'pdf') {
        throw new Exception('Format file harus PDF');
    }

    // KISS: Cek mapping sederhana
    $kd_user = $_SESSION['kd_user'];
    $provinsi_id = $_SESSION['provinsi_id'];

    $check_query = "SELECT * FROM mapping_paud_validasi WHERE id_mapping = '$id_mapping' AND provinsi_id = '$provinsi_id'";
    $check_result = mysqli_query($conn, $check_query);

    if (!$check_result || mysqli_num_rows($check_result) === 0) {
        throw new Exception('Data mapping tidak ditemukan');
    }

    $mapping_data = mysqli_fetch_assoc($check_result);

    // KISS: Setup direktori upload sederhana
    $upload_dir = "../../../../simak/files/upload_file_hasil_validasi_paud/";
    
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    // KISS: Upload file sederhana
    $nama_baru = time() . '.pdf';
    $upload_path = $upload_dir . $nama_baru;

    if (!move_uploaded_file($_FILES['file_penjelasan_hasil_akreditasi']['tmp_name'], $upload_path)) {
        throw new Exception('Gagal mengupload file ke server');
    }
    
    // KISS: Update database sederhana
    $update_query = "UPDATE mapping_paud_validasi SET file_penjelasan_hasil_akreditasi = '$nama_baru' WHERE id_mapping = '$id_mapping'";
    $update_result = mysqli_query($conn, $update_query);

    if (!$update_result) {
        throw new Exception('Gagal menyimpan data ke database');
    }

    // KISS: Response sederhana
    echo json_encode([
        'success' => true,
        'message' => 'File berhasil di-upload',
        'data' => [
            'id_mapping' => $id_mapping,
            'filename' => $nama_baru
        ]
    ]);
    
} catch (Exception $e) {
    // KISS: Response error sederhana
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
