$(document).ready(function() {
    // Initialize DataTable
    initDataTable();
    
    // Event handlers untuk action buttons
    $('#btn-input-mapping').on('click', function() {
        $('#modalInputMapping').modal('show');
        resetFormInputMapping();
    });
    
    $('#btn-export-excel').on('click', function() {
        showAlert('info', 'Fitur export Excel akan segera tersedia');
    });
    
    $('#btn-import-excel').on('click', function() {
        showImportModalVisitasiSM();
    });

    // Import modal event handlers
    $('#btn-download-template-visitasi-sm').on('click', function() {
        downloadTemplateVisitasiSM();
    });

    $('#btn-start-import-visitasi-sm').on('click', function() {
        startImportVisitasiSM();
    });

    // Reset modal when closed
    $('#modal-import-excel-visitasi-sm').on('hidden.bs.modal', function() {
        resetImportModalVisitasiSM();
    });
    
    $('#btn-tahun-akreditasi').on('click', function() {
        openModalTahunAkreditasi();
    });
    
    // Event handler untuk tombol detail
    $(document).on('click', '.btn-detail-visitasi', function(e) {
        e.preventDefault();
        var idMapping = $(this).data('id');
        openDetailMapping(idMapping);
    });

    // Event handlers untuk form input mapping
    $('#btnSimpanMapping').on('click', function() {
        simpanDataMapping();
    });

    // Real-time validation untuk NPSN
    $('#npsn_sekolah').on('blur', function() {
        var npsn = $(this).val().trim();
        if (npsn) {
            validateNPSN(npsn);
        }
    });

    // Real-time validation untuk NIA Asesor 1
    $('#nia_asesor1').on('blur', function() {
        var nia = $(this).val().trim();
        if (nia) {
            validateAsesor(nia, 1);
        }
    });

    // Real-time validation untuk NIA Asesor 2
    $('#nia_asesor2').on('blur', function() {
        var nia = $(this).val().trim();
        if (nia) {
            validateAsesor(nia, 2);
        }
    });
});

function initDataTable() {
    $('#table-mapping-visitasi').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'ajax/get_mapping_visitasi.php',
            type: 'POST',
            error: function(xhr, error, code) {
                console.error('DataTable AJAX Error:', error);
                showAlert('error', 'Gagal memuat data mapping visitasi');
            }
        },
        columns: [
            {
                data: null,
                name: 'no',
                orderable: false,
                searchable: false,
                render: function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { data: 'npsn', name: 'npsn' },
            { data: 'nama_sekolah', name: 'nama_sekolah' },
            {
                data: null,
                name: 'jenjang_rumpun',
                render: function(data, type, row) {
                    var jenjang = row.nm_jenjang || '-';
                    var rumpun = row.rumpun ? row.rumpun.toUpperCase() : '-';
                    return jenjang + '<br><small class="text-muted">' + rumpun + '</small>';
                }
            },
            { data: 'nm_kota', name: 'nm_kota' },
            {
                data: 'nia1',
                name: 'nia1',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            {
                data: 'nm_asesor1',
                name: 'nm_asesor1',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            {
                data: 'nia2',
                name: 'nia2',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            {
                data: 'nm_asesor2',
                name: 'nm_asesor2',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { data: 'tahun_akreditasi', name: 'tahun_akreditasi' },
            { data: 'tahap', name: 'tahap' },
            {
                data: null,
                name: 'aksi',
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    return '<button type="button" class="btn btn-info btn-sm btn-detail-visitasi" data-id="' + row.id_mapping + '" title="Detail Visitasi">' +
                           '<i class="fas fa-eye"></i>' +
                           '</button>';
                }
            }
        ],
        order: [[1, 'asc']], // Order by NPSN
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        language: {
            processing: "Memproses...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            loadingRecords: "Memuat...",
            zeroRecords: "Tidak ada data yang ditemukan",
            emptyTable: "Tidak ada data mapping visitasi",
            paginate: {
                first: "Pertama",
                previous: "Sebelumnya",
                next: "Selanjutnya",
                last: "Terakhir"
            }
        },
        responsive: true,
        scrollX: true,
        dom: '<"row"<"col-sm-6"l><"col-sm-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-5"i><"col-sm-7"p>>',
        drawCallback: function(settings) {
            // DataTable draw completed
        }
    });
}

// Fungsi untuk reset form input mapping
function resetFormInputMapping() {
    $('#formInputMapping')[0].reset();

    // Reset validation states
    $('#npsn_sekolah, #nia_asesor1, #nia_asesor2').removeClass('is-valid is-invalid');

    // Clear hidden fields
    $('#sekolah_id, #kd_asesor1, #kd_asesor2').val('');

    // Clear feedback messages
    $('#npsn_feedback, #asesor1_feedback, #asesor2_feedback').text('').hide();
    $('#sekolah_info, #asesor1_info, #asesor2_info').text('');
}

// Fungsi untuk validasi NPSN
function validateNPSN(npsn) {
    $.ajax({
        url: 'ajax/validate_npsn.php',
        type: 'POST',
        data: { npsn: npsn },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#npsn_sekolah').removeClass('is-invalid').addClass('is-valid');
                $('#sekolah_id').val(response.data.sekolah_id);
                $('#sekolah_info').text(response.data.nama_sekolah + ' - ' + response.data.jenjang + ' - ' + response.data.kota);
                $('#npsn_feedback').hide();
            } else {
                $('#npsn_sekolah').removeClass('is-valid').addClass('is-invalid');
                $('#npsn_feedback').text(response.message).show();
                $('#sekolah_id').val('');
                $('#sekolah_info').text('');
            }
        },
        error: function() {
            $('#npsn_sekolah').removeClass('is-valid').addClass('is-invalid');
            $('#npsn_feedback').text('Terjadi kesalahan saat validasi NPSN').show();
            $('#sekolah_id').val('');
            $('#sekolah_info').text('');
        }
    });
}

// Fungsi untuk validasi asesor
function validateAsesor(nia, asesorType) {
    var table = (asesorType === 1) ? 'asesor_1' : 'asesor_2';

    $.ajax({
        url: 'ajax/validate_asesor.php',
        type: 'POST',
        data: {
            nia: nia,
            asesor_type: asesorType
        },
        dataType: 'json',
        success: function(response) {
            var inputField = '#nia_asesor' + asesorType;
            var hiddenField = '#kd_asesor' + asesorType;
            var feedbackField = '#asesor' + asesorType + '_feedback';
            var infoField = '#asesor' + asesorType + '_info';

            if (response.success) {
                $(inputField).removeClass('is-invalid').addClass('is-valid');
                $(hiddenField).val(response.data.kd_asesor);
                $(infoField).text(response.data.nama + ' - ' + response.data.kota);
                $(feedbackField).hide();
            } else {
                $(inputField).removeClass('is-valid').addClass('is-invalid');
                $(feedbackField).text(response.message).show();
                $(hiddenField).val('');
                $(infoField).text('');
            }
        },
        error: function() {
            var inputField = '#nia_asesor' + asesorType;
            var hiddenField = '#kd_asesor' + asesorType;
            var feedbackField = '#asesor' + asesorType + '_feedback';
            var infoField = '#asesor' + asesorType + '_info';

            $(inputField).removeClass('is-valid').addClass('is-invalid');
            $(feedbackField).text('Terjadi kesalahan saat validasi NIA').show();
            $(hiddenField).val('');
            $(infoField).text('');
        }
    });
}

// Fungsi untuk simpan data mapping
function simpanDataMapping() {
    // Validasi form
    if (!$('#formInputMapping')[0].checkValidity()) {
        $('#formInputMapping')[0].reportValidity();
        return;
    }

    // Validasi bahwa lookup sudah berhasil
    if (!$('#sekolah_id').val()) {
        showAlert('error', 'NPSN belum valid atau belum divalidasi');
        return;
    }

    if (!$('#kd_asesor1').val()) {
        showAlert('error', 'NIA Asesor 1 belum valid atau belum divalidasi');
        return;
    }

    if (!$('#kd_asesor2').val()) {
        showAlert('error', 'NIA Asesor 2 belum valid atau belum divalidasi');
        return;
    }

    // Validasi asesor tidak boleh sama
    if ($('#kd_asesor1').val() === $('#kd_asesor2').val()) {
        showAlert('error', 'Asesor 1 dan Asesor 2 tidak boleh sama');
        return;
    }

    // Disable button saat proses simpan
    $('#btnSimpanMapping').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    // Ambil data form
    var formData = $('#formInputMapping').serialize();

    $.ajax({
        url: 'ajax/simpan_mapping.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showAlert('success', 'Data mapping berhasil disimpan');
                $('#modalInputMapping').modal('hide');

                // Refresh DataTable
                if ($.fn.DataTable.isDataTable('#table-mapping-visitasi')) {
                    $('#table-mapping-visitasi').DataTable().ajax.reload(null, false);
                }
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat menyimpan data');
        },
        complete: function() {
            // Re-enable button
            $('#btnSimpanMapping').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Data');
        }
    });
}

function showAlert(type, message) {
    let icon = 'info';
    let title = 'Informasi';

    switch(type) {
        case 'success':
            icon = 'success';
            title = 'Berhasil';
            break;
        case 'error':
            icon = 'error';
            title = 'Error';
            break;
        case 'warning':
            icon = 'warning';
            title = 'Peringatan';
            break;
    }

    Swal.fire({
        icon: icon,
        title: title,
        text: message,
        confirmButtonText: 'OK'
    });
}

// ===== TAHUN AKREDITASI FUNCTIONS =====

function openModalTahunAkreditasi() {
    // Ambil tahun akreditasi saat ini
    $.ajax({
        url: 'ajax/tahun_akreditasi.php',
        type: 'GET',
        data: { action: 'get' },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Set nilai tahun saat ini ke form
                $('#tahunAkreditasi').val(response.current_tahun);
                $('#modalTahunAkreditasi').modal('show');
            } else {
                showAlert('error', 'Gagal memuat data tahun akreditasi');
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat memuat data tahun');
        }
    });
}

// Event handler untuk tombol simpan tahun
$(document).on('click', '#btnSimpanTahun', function() {
    var tahun = $('#tahunAkreditasi').val();

    if (!tahun || tahun < 2020 || tahun > 2030) {
        showAlert('warning', 'Tahun harus antara 2020-2030');
        return;
    }

    // Simpan tahun akreditasi
    $.ajax({
        url: 'ajax/tahun_akreditasi.php',
        type: 'POST',
        data: {
            action: 'update',
            tahun_akreditasi: tahun
        },
        dataType: 'json',
        beforeSend: function() {
            $('#btnSimpanTahun').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
        },
        success: function(response) {
            if (response.success) {
                $('#modalTahunAkreditasi').modal('hide');

                // Refresh DataTable tanpa reload halaman
                $('#table-mapping-visitasi').DataTable().ajax.reload(null, false);
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat menyimpan tahun');
        },
        complete: function() {
            $('#btnSimpanTahun').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan');
        }
    });
});

// ===== DETAIL MAPPING FUNCTIONS =====

function openDetailMapping(idMapping) {
    // Load detail data
    $.ajax({
        url: 'ajax/detail_mapping.php',
        type: 'GET',
        data: { id_mapping: idMapping },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                populateDetailModal(response.data);
                $('#modalDetailMapping').modal('show');
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat memuat detail data');
        }
    });
}

function populateDetailModal(data) {
        // Data Sekolah
        $('#detail-npsn').text(data.sekolah.npsn);
        $('#detail-nama-sekolah').text(data.sekolah.nama_sekolah);
        $('#detail-jenjang').text(data.sekolah.jenjang);
        $('#detail-kota-sekolah').text(data.sekolah.kota_sekolah);
        $('#detail-nama-kepsek').text(data.sekolah.nama_kepsek);
        $('#detail-hp-kepsek').text(data.sekolah.hp_kepsek);
        $('#detail-wa-kepsek').text(data.sekolah.wa_kepsek);

        // Data Asesor
        $('#detail-nia1').text(data.asesor.asesor1.nia);
        $('#detail-nama-asesor1').text(data.asesor.asesor1.nama);
        $('#detail-hp-asesor1').text(data.asesor.asesor1.hp);
        $('#detail-kota-asesor1').text(data.asesor.asesor1.kota);

        $('#detail-nia2').text(data.asesor.asesor2.nia);
        $('#detail-nama-asesor2').text(data.asesor.asesor2.nama);
        $('#detail-hp-asesor2').text(data.asesor.asesor2.hp);
        $('#detail-kota-asesor2').text(data.asesor.asesor2.kota);

        // Status File Upload dengan clickable links
        setFileStatus('status-file-3-1-1', data.files.file_format_3_1_hasil_penilaian_pra_visitasi_1);
        setFileStatus('status-file-3-1-2', data.files.file_format_3_1_hasil_penilaian_pra_visitasi_2);
        setFileStatus('status-file-3-2-1', data.files.file_format_3_2_lk_penggalian_data_pra_visitasi_1);
        setFileStatus('status-file-3-2-2', data.files.file_format_3_2_lk_penggalian_data_pra_visitasi_2);
        setFileStatus('status-file-4-1', data.files.file_format_4_1_surat_tugas_visitasi);
        setFileStatus('status-file-4-2-1', data.files.file_format_4_2_pakta_integritas_1);
        setFileStatus('status-file-4-2-2', data.files.file_format_4_2_pakta_integritas_2);
        setFileStatus('status-file-4-3-1', data.files.file_lk_penilaian_1);
        setFileStatus('status-file-4-3-2', data.files.file_lk_penilaian_2);
        setFileStatus('status-file-4-4', data.files.file_format_4_4_berita_acara_visitasi);
        setFileStatus('status-file-4-5-1', data.files.file_format_4_5_laporan_individu_1);
        setFileStatus('status-file-4-5-2', data.files.file_format_4_5_laporan_individu_2);
        setFileStatus('status-file-4-5-kelompok', data.files.file_format_4_5_laporan_kelompok);
        setFileStatus('status-file-4-5-catatan', data.files.file_format_4_5_catatan_dan_saran);
        setFileStatus('status-file-foto', data.files.file_foto_visitasi_2025);

        // Pelaksanaan Kegiatan
        $('#detail-tgl-pra-visitasi').text(data.kegiatan.tgl_pra_visitasi);
        $('#detail-no-st-pra').text(data.kegiatan.no_st_pra_visitasi);
        $('#detail-tgl-st-pra').text(data.kegiatan.tgl_st_pra_visitasi);
        $('#detail-tgl-mulai-visitasi').text(data.kegiatan.tgl_mulai_visitasi);
        $('#detail-tgl-akhir-visitasi').text(data.kegiatan.tgl_akhir_visitasi);
        $('#detail-no-st-visitasi').text(data.kegiatan.no_st_visitasi);
        $('#detail-tgl-st-visitasi').text(data.kegiatan.tgl_st_visitasi);
        $('#detail-tahap').text(data.kegiatan.tahap);

    // Set ID mapping untuk aksi
    $('#modalDetailMapping').data('id-mapping', data.id_mapping);
}

function setFileStatus(elementId, fileData) {
    var element = $('#' + elementId);

    if (fileData.uploaded && fileData.filename) {
        // Buat URL file
        var fileUrl = '../../../simak/files/' + fileData.directory + '/' + fileData.filename;

        // Badge clickable untuk file yang sudah upload
        element.html(
            '<span class="badge bg-success text-white file-clickable" ' +
            'style="cursor: pointer; text-decoration: underline;" ' +
            'onclick="openFile(\'' + fileUrl + '\')" ' +
            'title="Klik untuk membuka file">' +
            'Sudah Upload</span>'
        );
    } else {
        // Badge tidak clickable untuk file yang belum upload
        element.html('<span class="badge bg-danger text-white">Belum Upload</span>');
    }
}

// Function untuk membuka file di tab baru
function openFile(fileUrl) {
    window.open(fileUrl, '_blank');
}

// ===== EDIT TANGGAL VISITASI FUNCTIONS =====

// Event handler untuk tombol edit tanggal
$(document).on('click', '#btn-edit-tanggal', function() {
    var idMapping = $('#modalDetailMapping').data('id-mapping');
    if (idMapping) {
        openEditTanggalModal(idMapping);
    } else {
        showAlert('error', 'ID mapping tidak ditemukan');
    }
});

function openEditTanggalModal(idMapping) {
    // Set ID mapping ke form
    $('#editTanggalIdMapping').val(idMapping);

    // Ambil tanggal saat ini dari modal detail
    var tglMulai = $('#detail-tgl-mulai-visitasi').text();
    var tglAkhir = $('#detail-tgl-akhir-visitasi').text();

    // Convert format tanggal dari dd/mm/yyyy ke yyyy-mm-dd untuk input date
    if (tglMulai !== '-' && tglMulai !== '') {
        var tglMulaiParts = tglMulai.split('/');
        if (tglMulaiParts.length === 3) {
            $('#editTglMulaiVisitasi').val(tglMulaiParts[2] + '-' + tglMulaiParts[1] + '-' + tglMulaiParts[0]);
        }
    }

    if (tglAkhir !== '-' && tglAkhir !== '') {
        var tglAkhirParts = tglAkhir.split('/');
        if (tglAkhirParts.length === 3) {
            $('#editTglAkhirVisitasi').val(tglAkhirParts[2] + '-' + tglAkhirParts[1] + '-' + tglAkhirParts[0]);
        }
    }

    // Tampilkan modal edit tanggal
    $('#modalEditTanggal').modal('show');
}

// Event handler untuk tombol update tanggal
$(document).on('click', '#btnUpdateTanggal', function() {
    var formData = {
        id_mapping: $('#editTanggalIdMapping').val(),
        tgl_mulai_visitasi: $('#editTglMulaiVisitasi').val(),
        tgl_akhir_visitasi: $('#editTglAkhirVisitasi').val()
    };

    // Validasi form
    if (!formData.tgl_mulai_visitasi || !formData.tgl_akhir_visitasi) {
        showAlert('warning', 'Semua field tanggal wajib diisi');
        return;
    }

    // Validasi tanggal akhir >= tanggal mulai
    if (new Date(formData.tgl_akhir_visitasi) < new Date(formData.tgl_mulai_visitasi)) {
        showAlert('warning', 'Tanggal akhir harus sama atau setelah tanggal mulai');
        return;
    }

    // Submit update
    $.ajax({
        url: 'ajax/update_tanggal_visitasi.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        beforeSend: function() {
            $('#btnUpdateTanggal').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Updating...');
        },
        success: function(response) {
            if (response.success) {
                // Update data di modal detail tanpa refresh
                $('#detail-tgl-mulai-visitasi').text(response.data.tgl_mulai_visitasi);
                $('#detail-tgl-akhir-visitasi').text(response.data.tgl_akhir_visitasi);

                // Refresh DataTable utama untuk menampilkan perubahan tanggal
                $('#table-mapping-visitasi').DataTable().ajax.reload(null, false);

                // Tutup modal edit tanggal
                $('#modalEditTanggal').modal('hide');

                // Reset form
                $('#formEditTanggal')[0].reset();
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat mengupdate tanggal visitasi');
        },
        complete: function() {
            $('#btnUpdateTanggal').prop('disabled', false).html('<i class="fas fa-save"></i> Update Perubahan');
        }
    });
});

// ===== EDIT ASESOR FUNCTIONS =====

// Event handler untuk tombol edit asesor
$(document).on('click', '#btn-edit-asesor', function() {
    var idMapping = $('#modalDetailMapping').data('id-mapping');
    if (idMapping) {
        openEditAsesorModal(idMapping);
    } else {
        showAlert('error', 'ID mapping tidak ditemukan');
    }
});

function openEditAsesorModal(idMapping) {
    // Set ID mapping ke form
    $('#editAsesorIdMapping').val(idMapping);

    // Ambil NIA asesor saat ini dari modal detail
    var niaAsesor1 = $('#detail-nia1').text();
    var niaAsesor2 = $('#detail-nia2').text();

    // Set nilai ke form
    if (niaAsesor1 !== '-' && niaAsesor1 !== '') {
        $('#editNiaAsesor1').val(niaAsesor1);
    }

    if (niaAsesor2 !== '-' && niaAsesor2 !== '') {
        $('#editNiaAsesor2').val(niaAsesor2);
    }

    // Tampilkan modal edit asesor
    $('#modalEditAsesor').modal('show');
}

// Event handler untuk tombol update asesor
$(document).on('click', '#btnUpdateAsesor', function() {
    var formData = {
        id_mapping: $('#editAsesorIdMapping').val(),
        nia_asesor1: $('#editNiaAsesor1').val().trim(),
        nia_asesor2: $('#editNiaAsesor2').val().trim()
    };

    // Validasi form
    if (!formData.nia_asesor1 || !formData.nia_asesor2) {
        showAlert('warning', 'NIA Asesor 1 dan Asesor 2 wajib diisi');
        return;
    }

    // Validasi NIA tidak sama
    if (formData.nia_asesor1 === formData.nia_asesor2) {
        showAlert('warning', 'NIA Asesor 1 dan Asesor 2 harus berbeda');
        return;
    }

    // Submit update
    $.ajax({
        url: 'ajax/update_asesor.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        beforeSend: function() {
            $('#btnUpdateAsesor').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Updating...');
        },
        success: function(response) {
            if (response.success) {
                // Update data asesor 1 di modal detail tanpa refresh
                $('#detail-nia1').text(response.data.asesor1.nia);
                $('#detail-nama-asesor1').text(response.data.asesor1.nama);
                $('#detail-hp-asesor1').text(response.data.asesor1.hp);
                $('#detail-kota-asesor1').text(response.data.asesor1.kota);

                // Update data asesor 2 di modal detail tanpa refresh
                $('#detail-nia2').text(response.data.asesor2.nia);
                $('#detail-nama-asesor2').text(response.data.asesor2.nama);
                $('#detail-hp-asesor2').text(response.data.asesor2.hp);
                $('#detail-kota-asesor2').text(response.data.asesor2.kota);

                // Refresh DataTable utama untuk menampilkan perubahan asesor
                $('#table-mapping-visitasi').DataTable().ajax.reload(null, false);

                // Tutup modal edit asesor
                $('#modalEditAsesor').modal('hide');

                // Reset form
                $('#formEditAsesor')[0].reset();
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat mengupdate data asesor');
        },
        complete: function() {
            $('#btnUpdateAsesor').prop('disabled', false).html('<i class="fas fa-save"></i> Update Perubahan');
        }
    });
});

// ===== DOWNLOAD SURAT TUGAS FUNCTIONS =====

// Event handler untuk tombol download ST Visitasi
$(document).on('click', '#btn-download-st-visitasi', function() {
    var idMapping = $('#modalDetailMapping').data('id-mapping');
    if (idMapping) {
        downloadSTVisitasi(idMapping);
    } else {
        showAlert('error', 'ID mapping tidak ditemukan');
    }
});

// Event handler untuk tombol download ST Pra-Visitasi
$(document).on('click', '#btn-download-st-pra', function() {
    var idMapping = $('#modalDetailMapping').data('id-mapping');
    if (idMapping) {
        downloadSTPraVisitasi(idMapping);
    } else {
        showAlert('error', 'ID mapping tidak ditemukan');
    }
});

function downloadSTVisitasi(idMapping) {
    // Buka file PDF surat tugas visitasi di tab baru dengan parameter kode yang benar
    var url = 'mapping_2025_st_visitasi.php?kode=' + idMapping;
    window.open(url, '_blank');
}

function downloadSTPraVisitasi(idMapping) {
    // Buka file PDF surat tugas pra-visitasi di tab baru dengan parameter kode
    var url = 'mapping_2025_st_pra_akreditasi.php?kode=' + idMapping;
    window.open(url, '_blank');
}

// ===== DELETE MAPPING FUNCTIONS =====

// Event handler untuk tombol hapus
$(document).on('click', '#btn-hapus', function() {
    var idMapping = $('#modalDetailMapping').data('id-mapping');
    if (idMapping) {
        confirmDeleteMapping(idMapping);
    } else {
        showAlert('error', 'ID mapping tidak ditemukan');
    }
});

function confirmDeleteMapping(idMapping) {
    // Ambil nama sekolah untuk konfirmasi
    var namaSekolah = $('#detail-nama-sekolah').text();
    var npsn = $('#detail-npsn').text();

    // Konfirmasi dengan SweetAlert
    Swal.fire({
        title: 'Konfirmasi Hapus Data',
        html: `
            <div class="text-left">
                <p><strong>Apakah Anda yakin ingin menghapus data mapping ini?</strong></p>
                <hr>
                <p><strong>Sekolah:</strong> ${namaSekolah}</p>
                <p><strong>NPSN:</strong> ${npsn}</p>
                <hr>
                <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> <strong>Peringatan:</strong> Data akan dihapus secara permanen dan tidak dapat dikembalikan!</p>
            </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: '<i class="fas fa-trash"></i> Ya, Hapus!',
        cancelButtonText: '<i class="fas fa-times"></i> Batal',
        reverseButtons: true,
        focusCancel: true
    }).then((result) => {
        if (result.isConfirmed) {
            executeDeleteMapping(idMapping);
        }
    });
}

function executeDeleteMapping(idMapping) {
    $.ajax({
        url: 'ajax/delete_mapping.php',
        type: 'POST',
        data: { id_mapping: idMapping },
        dataType: 'json',
        beforeSend: function() {
            Swal.fire({
                title: 'Menghapus Data...',
                html: 'Mohon tunggu sebentar',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        },
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Berhasil!',
                    text: response.message,
                    confirmButtonText: 'OK'
                }).then(() => {
                    // Tutup modal detail
                    $('#modalDetailMapping').modal('hide');

                    // Refresh DataTable untuk menghapus baris yang sudah dihapus
                    $('#table-mapping-visitasi').DataTable().ajax.reload(null, false);
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Gagal!',
                    text: response.message,
                    confirmButtonText: 'OK'
                });
            }
        },
        error: function() {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Terjadi kesalahan saat menghapus data',
                confirmButtonText: 'OK'
            });
        }
    });
}

// ===== EXPORT DATA FUNCTIONS =====

// Event handler untuk tombol export excel
$(document).on('click', '#btn-export-excel', function() {
    exportMappingData();
});

function exportMappingData() {
    console.log('Exporting mapping visitasi to Excel');

    // Show loading
    Swal.fire({
        title: 'Memproses Export Excel...',
        html: 'Mohon tunggu, sedang mengambil data...',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    $.ajax({
        url: 'export_mapping_data.php',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data && response.data.length > 0) {
                // Function untuk status file upload
                function getFileStatus(filename) {
                    return (filename && filename.trim() !== '') ? 'Sudah Unggah' : 'Belum Unggah';
                }

                // Function untuk format tanggal
                function formatTanggal(tanggal) {
                    if (!tanggal || tanggal === '0000-00-00') {
                        return '-';
                    }
                    var date = new Date(tanggal);
                    return date.toLocaleDateString('id-ID');
                }

                // Prepare data untuk Excel dengan 38 kolom
                var excelData = [];

                // Header
                excelData.push([
                    'NO',
                    'NPSN',
                    'NAMA SEKOLAH',
                    'JENJANG',
                    'KAB/KOTA SEKOLAH',
                    'RUMPUN',
                    'TAHUN AKREDITASI',
                    'NIA ASESOR 1',
                    'NAMA ASESOR 1',
                    'HP ASESOR 1',
                    'KOTA ASESOR 1',
                    'NIA ASESOR 2',
                    'NAMA ASESOR 2',
                    'HP ASESOR 2',
                    'KOTA ASESOR 2',
                    'TANGGAL PRA-VISITASI',
                    'TANGGAL ST PRA-VISITASI',
                    'NO. ST PRA-VISITASI',
                    'TANGGAL MULAI VISITASI',
                    'TANGGAL AKHIR VISITASI',
                    'TANGGAL ST VISITASI',
                    'NO. ST VISITASI',
                    'TAHAP',
                    'FILE FORMAT 3.1 PENILAIAN PRA-VISITASI 1',
                    'FILE FORMAT 3.1 PENILAIAN PRA-VISITASI 2',
                    'FILE FORMAT 3.2 LK PENGGALIAN DATA PRA-VISITASI 1',
                    'FILE FORMAT 3.2 LK PENGGALIAN DATA PRA-VISITASI 2',
                    'FILE FORMAT 4.1 SURAT TUGAS VISITASI',
                    'FILE FORMAT 4.2 PAKTA INTEGRITAS 1',
                    'FILE FORMAT 4.2 PAKTA INTEGRITAS 2',
                    'FILE FILE LEMBAR KERJA PENILAIAN 1',
                    'FILE FILE LEMBAR KERJA PENILAIAN 2',
                    'FILE FORMAT 4.4 BERITA ACARA VISITASI',
                    'FILE FORMAT 4.5 LAPORAN INDIVIDU 1',
                    'FILE FORMAT 4.5 LAPORAN INDIVIDU 2',
                    'FILE FORMAT 4.5 LAPORAN KELOMPOK',
                    'FILE FORMAT 4.5 CATATAN DAN SARAN',
                    'FILE FOTO VISITASI'
                ]);

                // Data rows
                response.data.forEach(function(row, index) {
                    excelData.push([
                        index + 1,
                        row.npsn || '-',
                        row.nama_sekolah || '-',
                        row.nm_jenjang || '-',
                        row.nm_kota || '-',
                        row.rumpun || '-',
                        row.tahun_akreditasi || '-',
                        row.nia1 || '-',
                        row.nm_asesor1 || '-',
                        row.hp1 || '-',
                        row.kota1 || '-',
                        row.nia2 || '-',
                        row.nm_asesor2 || '-',
                        row.hp2 || '-',
                        row.kota2 || '-',
                        formatTanggal(row.tgl_pra_visitasi),
                        formatTanggal(row.tgl_surat_tugas_pra_visitasi),
                        row.no_surat_tugas_pra_visitasi || '-',
                        formatTanggal(row.tgl_mulai_visitasi),
                        formatTanggal(row.tgl_akhir_visitasi),
                        formatTanggal(row.tgl_surat_tugas_visitasi),
                        row.no_surat_tugas_visitasi || '-',
                        row.tahap || '-',
                        getFileStatus(row.file_format_3_1_hasil_penilaian_pra_visitasi_1),
                        getFileStatus(row.file_format_3_1_hasil_penilaian_pra_visitasi_2),
                        getFileStatus(row.file_format_3_2_lk_penggalian_data_pra_visitasi_1),
                        getFileStatus(row.file_format_3_2_lk_penggalian_data_pra_visitasi_2),
                        getFileStatus(row.file_format_4_1_surat_tugas_visitasi),
                        getFileStatus(row.file_format_4_2_pakta_integritas_1),
                        getFileStatus(row.file_format_4_2_pakta_integritas_2),
                        getFileStatus(row.file_lk_penilaian_1),
                        getFileStatus(row.file_lk_penilaian_2),
                        getFileStatus(row.file_format_4_4_berita_acara_visitasi),
                        getFileStatus(row.file_format_4_5_laporan_individu_1),
                        getFileStatus(row.file_format_4_5_laporan_individu_2),
                        getFileStatus(row.file_format_4_5_laporan_kelompok),
                        getFileStatus(row.file_format_4_5_catatan_dan_saran),
                        getFileStatus(row.file_foto_visitasi_2025)
                    ]);
                });

                // Create workbook and worksheet
                var wb = XLSX.utils.book_new();
                var ws = XLSX.utils.aoa_to_sheet(excelData);

                // Set column widths untuk 38 kolom
                ws['!cols'] = [
                    {wch: 5},   // NO
                    {wch: 12},  // NPSN
                    {wch: 25},  // NAMA SEKOLAH
                    {wch: 10},  // JENJANG
                    {wch: 15},  // KAB/KOTA SEKOLAH
                    {wch: 10},  // RUMPUN
                    {wch: 12},  // TAHUN AKREDITASI
                    {wch: 12},  // NIA ASESOR 1
                    {wch: 20},  // NAMA ASESOR 1
                    {wch: 12},  // HP ASESOR 1
                    {wch: 15},  // KOTA ASESOR 1
                    {wch: 12},  // NIA ASESOR 2
                    {wch: 20},  // NAMA ASESOR 2
                    {wch: 12},  // HP ASESOR 2
                    {wch: 15},  // KOTA ASESOR 2
                    {wch: 15},  // TANGGAL PRA-VISITASI
                    {wch: 15},  // TANGGAL ST PRA-VISITASI
                    {wch: 20},  // NO. ST PRA-VISITASI
                    {wch: 15},  // TANGGAL MULAI VISITASI
                    {wch: 15},  // TANGGAL AKHIR VISITASI
                    {wch: 15},  // TANGGAL ST VISITASI
                    {wch: 20},  // NO. ST VISITASI
                    {wch: 10},  // TAHAP
                    {wch: 15},  // FILE FORMAT 3.1 PENILAIAN PRA-VISITASI 1
                    {wch: 15},  // FILE FORMAT 3.1 PENILAIAN PRA-VISITASI 2
                    {wch: 15},  // FILE FORMAT 3.2 LK PENGGALIAN DATA PRA-VISITASI 1
                    {wch: 15},  // FILE FORMAT 3.2 LK PENGGALIAN DATA PRA-VISITASI 2
                    {wch: 15},  // FILE FORMAT 4.1 SURAT TUGAS VISITASI
                    {wch: 15},  // FILE FORMAT 4.2 PAKTA INTEGRITAS 1
                    {wch: 15},  // FILE FORMAT 4.2 PAKTA INTEGRITAS 2
                    {wch: 15},  // FILE FILE LEMBAR KERJA PENILAIAN 1
                    {wch: 15},  // FILE FILE LEMBAR KERJA PENILAIAN 2
                    {wch: 15},  // FILE FORMAT 4.4 BERITA ACARA VISITASI
                    {wch: 15},  // FILE FORMAT 4.5 LAPORAN INDIVIDU 1
                    {wch: 15},  // FILE FORMAT 4.5 LAPORAN INDIVIDU 2
                    {wch: 15},  // FILE FORMAT 4.5 LAPORAN KELOMPOK
                    {wch: 15},  // FILE FORMAT 4.5 CATATAN DAN SARAN
                    {wch: 15}   // FILE FOTO VISITASI
                ];

                // Apply conditional formatting untuk file status
                applyConditionalFormattingVisitasiSM(ws, excelData, response.data);

                // Add worksheet to workbook
                XLSX.utils.book_append_sheet(wb, ws, 'Export Visitasi SM');

                // Generate filename dengan current date
                var now = new Date();
                var dateStr = now.getFullYear() +
                             String(now.getMonth() + 1).padStart(2, '0') +
                             String(now.getDate()).padStart(2, '0');
                var filename = 'Export_Mapping_Visitasi_SM_' + dateStr + '.xlsx';

                // Save file
                XLSX.writeFile(wb, filename);

                // Show success message
                Swal.fire({
                    icon: 'success',
                    title: 'Export Berhasil!',
                    text: 'File Excel berhasil didownload: ' + filename,
                    confirmButtonText: 'OK'
                });

            } else {
                Swal.fire({
                    icon: 'warning',
                    title: 'Tidak Ada Data',
                    text: response.message || 'Tidak ada data untuk diekspor',
                    confirmButtonText: 'OK'
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('Error exporting to Excel:', error);
            Swal.fire({
                icon: 'error',
                title: 'Export Gagal!',
                text: 'Terjadi kesalahan saat export Excel',
                confirmButtonText: 'OK'
            });
        }
    });
}

function applyConditionalFormattingVisitasiSM(ws, excelData, data) {
    // File status columns (kolom 24-38 adalah file status)
    const fileStatusColumns = [23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37];

    // Apply formatting untuk setiap baris data (skip header)
    for (let rowIndex = 1; rowIndex < excelData.length; rowIndex++) {
        const row = data[rowIndex - 1]; // data array dimulai dari 0, excelData dimulai dari 1 (karena header)

        if (row) {
            // File fields untuk cek status
            const fileFields = [
                row.file_format_3_1_hasil_penilaian_pra_visitasi_1,
                row.file_format_3_1_hasil_penilaian_pra_visitasi_2,
                row.file_format_3_2_lk_penggalian_data_pra_visitasi_1,
                row.file_format_3_2_lk_penggalian_data_pra_visitasi_2,
                row.file_format_4_1_surat_tugas_visitasi,
                row.file_format_4_2_pakta_integritas_1,
                row.file_format_4_2_pakta_integritas_2,
                row.file_lk_penilaian_1,
                row.file_lk_penilaian_2,
                row.file_format_4_4_berita_acara_visitasi,
                row.file_format_4_5_laporan_individu_1,
                row.file_format_4_5_laporan_individu_2,
                row.file_format_4_5_laporan_kelompok,
                row.file_format_4_5_catatan_dan_saran,
                row.file_foto_visitasi_2025
            ];

            // Apply formatting untuk setiap file status column
            fileStatusColumns.forEach((colIndex, fileIndex) => {
                const cellAddress = XLSX.utils.encode_cell({r: rowIndex, c: colIndex});
                const fileValue = fileFields[fileIndex];

                if (!ws[cellAddress]) {
                    ws[cellAddress] = {};
                }

                // Set cell style berdasarkan status file
                if (fileValue && fileValue.trim() !== '') {
                    // Sudah Unggah - Green background
                    ws[cellAddress].s = {
                        fill: {
                            fgColor: { rgb: "90EE90" } // Light green
                        },
                        font: {
                            color: { rgb: "006400" }, // Dark green
                            bold: true
                        }
                    };
                } else {
                    // Belum Unggah - Red background
                    ws[cellAddress].s = {
                        fill: {
                            fgColor: { rgb: "FFB6C1" } // Light red
                        },
                        font: {
                            color: { rgb: "8B0000" }, // Dark red
                            bold: true
                        }
                    };
                }
            });
        }
    }

    // Apply header styling
    for (let colIndex = 0; colIndex < excelData[0].length; colIndex++) {
        const cellAddress = XLSX.utils.encode_cell({r: 0, c: colIndex});
        if (!ws[cellAddress]) {
            ws[cellAddress] = {};
        }

        // Gray header styling
        ws[cellAddress].s = {
            fill: {
                fgColor: { rgb: "D3D3D3" } // Light gray
            },
            font: {
                bold: true,
                color: { rgb: "000000" } // Black
            },
            alignment: {
                horizontal: "center",
                vertical: "center"
            }
        };
    }
}

// Function untuk download template Excel menggunakan SheetJS
function downloadTemplateVisitasiSM() {
    // Check if SheetJS is available
    if (typeof XLSX === 'undefined') {
        Swal.fire({
            icon: 'error',
            title: 'Library Excel tidak tersedia',
            text: 'Silakan refresh halaman.',
            confirmButtonText: 'OK'
        });
        return;
    }

    try {
        // Create workbook
        const wb = XLSX.utils.book_new();

        // Create worksheet data
        const wsData = [
            // Headers
            ['NPSN', 'NIA_Asesor1', 'NIA_Asesor2', 'Tgl_Pra_Visitasi', 'Tgl_ST_Pra_Visitasi', 'No_ST_Pra_Visitasi', 'Tgl_ST_Visitasi', 'No_ST_Visitasi', 'Tahap', 'Tahun_Akreditasi', '', 'PETUNJUK PENGISIAN:'],
            // Sample data
            ['12345678', 'A123456', 'B789012', '2025-01-15', '2025-01-10', '001/2025', '2025-02-01', '002/2025', '1', '2025', '', '1. NPSN: 8 digit nomor sekolah DASMEN'],
            ['87654321', 'C456789', 'D012345', '2025-02-20', '2025-02-15', '003/2025', '2025-03-01', '004/2025', '2', '2025', '', '2. NIA_Asesor1: Nomor Induk Asesor 1'],
            ['11223344', 'E678901', 'F234567', '2025-03-10', '2025-03-05', '005/2025', '2025-03-15', '006/2025', '3', '2025', '', '3. NIA_Asesor2: Nomor Induk Asesor 2'],
            ['', '', '', '', '', '', '', '', '', '', '', '4. Tgl_Pra_Visitasi: Format yyyy-mm-dd'],
            ['', '', '', '', '', '', '', '', '', '', '', '5. Tgl_ST_Pra_Visitasi: Format yyyy-mm-dd'],
            ['', '', '', '', '', '', '', '', '', '', '', '6. No_ST_Pra_Visitasi: Nomor surat tugas'],
            ['', '', '', '', '', '', '', '', '', '', '', '7. Tgl_ST_Visitasi: Format yyyy-mm-dd'],
            ['', '', '', '', '', '', '', '', '', '', '', '8. No_ST_Visitasi: Nomor surat tugas'],
            ['', '', '', '', '', '', '', '', '', '', '', '9. Tahap: Angka 1, 2, 3, dst'],
            ['', '', '', '', '', '', '', '', '', '', '', '10. Tahun_Akreditasi: 4 digit tahun'],
            ['', '', '', '', '', '', '', '', '', '', '', '11. Asesor1 dan Asesor2 harus berbeda'],
            ['', '', '', '', '', '', '', '', '', '', '', '12. Hapus baris contoh sebelum import']
        ];

        // Create worksheet
        const ws = XLSX.utils.aoa_to_sheet(wsData);

        // Set column widths
        ws['!cols'] = [
            { width: 12 }, // NPSN
            { width: 15 }, // NIA_Asesor1
            { width: 15 }, // NIA_Asesor2
            { width: 16 }, // Tgl_Pra_Visitasi
            { width: 18 }, // Tgl_ST_Pra_Visitasi
            { width: 18 }, // No_ST_Pra_Visitasi
            { width: 16 }, // Tgl_ST_Visitasi
            { width: 16 }, // No_ST_Visitasi
            { width: 8 },  // Tahap
            { width: 16 }, // Tahun_Akreditasi
            { width: 3 },  // Empty column
            { width: 40 }  // Instructions
        ];

        // Set row heights
        ws['!rows'] = [
            { hpt: 25 }, // Header row height
            { hpt: 20 }, // Sample data rows
            { hpt: 20 },
            { hpt: 20 },
            { hpt: 20 },
            { hpt: 20 },
            { hpt: 20 },
            { hpt: 20 },
            { hpt: 20 },
            { hpt: 20 },
            { hpt: 20 },
            { hpt: 20 },
            { hpt: 20 }
        ];

        // Add cell comments for better guidance
        if (!ws['!comments']) ws['!comments'] = [];

        // Add comment to header row
        ws['A1'].c = [{
            a: 'System',
            t: 'Masukkan 8 digit NPSN sekolah DASMEN yang valid'
        }];

        ws['B1'].c = [{
            a: 'System',
            t: 'Masukkan NIA asesor 1 yang terdaftar dan aktif'
        }];

        ws['C1'].c = [{
            a: 'System',
            t: 'Masukkan NIA asesor 2 yang terdaftar dan aktif (harus berbeda dari Asesor 1)'
        }];

        ws['D1'].c = [{
            a: 'System',
            t: 'Tanggal pra visitasi format: YYYY-MM-DD (contoh: 2025-01-15)'
        }];

        ws['E1'].c = [{
            a: 'System',
            t: 'Tanggal surat tugas pra visitasi format: YYYY-MM-DD'
        }];

        ws['F1'].c = [{
            a: 'System',
            t: 'Nomor surat tugas pra visitasi'
        }];

        ws['G1'].c = [{
            a: 'System',
            t: 'Tanggal surat tugas visitasi format: YYYY-MM-DD'
        }];

        ws['H1'].c = [{
            a: 'System',
            t: 'Nomor surat tugas visitasi'
        }];

        ws['I1'].c = [{
            a: 'System',
            t: 'Tahap visitasi: 1, 2, 3, dst'
        }];

        ws['J1'].c = [{
            a: 'System',
            t: 'Tahun akreditasi 4 digit (contoh: 2025)'
        }];

        // Freeze header row
        ws['!freeze'] = { xSplit: 0, ySplit: 1 };

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Template');

        // Generate filename
        const filename = 'Template_Import_Mapping_Visitasi_SM_' + new Date().toISOString().split('T')[0] + '.xlsx';

        // Download file
        XLSX.writeFile(wb, filename);

        // Show success message
        Swal.fire({
            icon: 'success',
            title: 'Template Downloaded!',
            text: 'File template berhasil didownload: ' + filename,
            confirmButtonText: 'OK'
        });

    } catch (error) {
        console.error('Error generating template:', error);
        Swal.fire({
            icon: 'error',
            title: 'Gagal membuat template Excel',
            text: error.message,
            confirmButtonText: 'OK'
        });
    }
}

// Function untuk show import modal
function showImportModalVisitasiSM() {
    resetImportModalVisitasiSM();
    const modal = new bootstrap.Modal(document.getElementById('modal-import-excel-visitasi-sm'));
    modal.show();
}

// Function untuk reset import modal
function resetImportModalVisitasiSM() {
    // Reset file input
    $('#excel-file-visitasi-sm').val('');

    // Show upload section, hide others
    $('#upload-section-visitasi-sm').show();
    $('#progress-section-visitasi-sm').hide();
    $('#results-section-visitasi-sm').hide();

    // Always show petunjuk import
    $('#petunjuk-import-visitasi-sm').show();

    // Reset progress
    updateProgressVisitasiSM(0, 'Memulai proses import...');

    // Enable buttons
    $('#btn-start-import-visitasi-sm').prop('disabled', false);
    $('#btn-cancel-import-visitasi-sm').prop('disabled', false);
}

// Function untuk update progress
function updateProgressVisitasiSM(percentage, message) {
    $('#import-progress-bar-visitasi-sm')
        .css('width', percentage + '%')
        .attr('aria-valuenow', percentage)
        .text(percentage + '%');

    $('#progress-message-visitasi-sm').text(message);

    // Update progress title based on percentage
    if (percentage === 25) {
        $('#progress-title-visitasi-sm').text('Membaca file Excel...');
    } else if (percentage === 50) {
        $('#progress-title-visitasi-sm').text('Memproses data Excel...');
    } else if (percentage === 75) {
        $('#progress-title-visitasi-sm').text('Menyimpan data ke database...');
    } else if (percentage === 100) {
        $('#progress-title-visitasi-sm').text('Import complete!');
    }
}

// Function untuk start import menggunakan SheetJS
function startImportVisitasiSM() {
    // Check if SheetJS is available
    if (typeof XLSX === 'undefined') {
        Swal.fire({
            icon: 'error',
            title: 'Library Excel tidak tersedia',
            text: 'Silakan refresh halaman.',
            confirmButtonText: 'OK'
        });
        return;
    }

    // Validate file selection
    const fileInput = $('#excel-file-visitasi-sm')[0];
    if (!fileInput.files || fileInput.files.length === 0) {
        Swal.fire({
            icon: 'error',
            title: 'File tidak dipilih',
            text: 'Silakan pilih file Excel terlebih dahulu',
            confirmButtonText: 'OK'
        });
        return;
    }

    const file = fileInput.files[0];

    // Validate file type
    const allowedTypes = ['.xlsx', '.xls'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    if (!allowedTypes.includes(fileExtension)) {
        Swal.fire({
            icon: 'error',
            title: 'Format file tidak didukung',
            text: 'Gunakan .xlsx atau .xls',
            confirmButtonText: 'OK'
        });
        return;
    }

    // Hide upload section, show progress
    $('#upload-section-visitasi-sm').hide();
    $('#progress-section-visitasi-sm').show();
    $('#results-section-visitasi-sm').hide();

    // Disable buttons during import
    $('#btn-start-import-visitasi-sm').prop('disabled', true);
    $('#btn-cancel-import-visitasi-sm').prop('disabled', true);

    // Phase 1: Upload (25%)
    updateProgressVisitasiSM(25, 'Membaca file Excel...');

    // Read Excel file using SheetJS
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            // Phase 2: Parse (50%)
            updateProgressVisitasiSM(50, 'Memproses data Excel...');

            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });

            // Get first worksheet
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];

            // Convert to array of arrays
            const rows = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

            // Remove header row
            rows.shift();

            // Filter out empty rows
            const filteredRows = rows.filter(row => row.some(cell => cell !== undefined && cell !== ''));

            if (filteredRows.length === 0) {
                throw new Error('File tidak memiliki data untuk diimport');
            }

            if (filteredRows.length > 100) {
                throw new Error('Maksimal 100 baris data. File Anda memiliki ' + filteredRows.length + ' baris');
            }

            // Phase 3: Send to server (75%)
            updateProgressVisitasiSM(75, 'Menyimpan data ke database...');

            // Send parsed data to server
            $.ajax({
                url: 'ajax/import_excel_visitasi_sm.php',
                type: 'POST',
                data: {
                    excel_data: JSON.stringify(filteredRows)
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // Phase 4: Complete (100%)
                        updateProgressVisitasiSM(100, 'Import berhasil diselesaikan');

                        // Show results
                        showImportResultsVisitasiSM(response.data);

                        // Refresh DataTable
                        $('#table-mapping-visitasi').DataTable().ajax.reload(null, false);

                    } else {
                        showImportErrorVisitasiSM(response.message);
                    }
                },
                error: function(xhr, status, error) {
                    let errorMessage = 'Terjadi kesalahan saat menyimpan data';
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        errorMessage = errorResponse.message || errorMessage;
                    } catch (e) {
                        // Use default error message
                    }
                    showImportErrorVisitasiSM(errorMessage);
                },
                complete: function() {
                    // Re-enable buttons
                    $('#btn-start-import-visitasi-sm').prop('disabled', false);
                    $('#btn-cancel-import-visitasi-sm').prop('disabled', false);
                }
            });

        } catch (error) {
            showImportErrorVisitasiSM('Gagal membaca file Excel: ' + error.message);
            $('#btn-start-import-visitasi-sm').prop('disabled', false);
            $('#btn-cancel-import-visitasi-sm').prop('disabled', false);
        }
    };

    reader.onerror = function() {
        showImportErrorVisitasiSM('Gagal membaca file');
        $('#btn-start-import-visitasi-sm').prop('disabled', false);
        $('#btn-cancel-import-visitasi-sm').prop('disabled', false);
    };

    // Read file as array buffer
    reader.readAsArrayBuffer(file);
}

// Function untuk show import results
function showImportResultsVisitasiSM(data) {
    $('#progress-section-visitasi-sm').hide();
    $('#results-section-visitasi-sm').show();

    let resultHtml = '';

    if (data.success_count > 0 || data.error_count > 0) {
        resultHtml += '<div class="alert alert-success">';
        resultHtml += '<h6><i class="fas fa-check-circle"></i> Import Selesai</h6>';
        resultHtml += '<p class="mb-1"><strong>Berhasil:</strong> ' + data.success_count + ' data</p>';
        if (data.error_count > 0) {
            resultHtml += '<p class="mb-1"><strong>Gagal:</strong> ' + data.error_count + ' data</p>';
        }
        resultHtml += '<p class="mb-0"><strong>Total diproses:</strong> ' + data.total_processed + ' baris</p>';
        resultHtml += '</div>';

        // Show errors if any
        if (data.errors && data.errors.length > 0) {
            resultHtml += '<div class="alert alert-warning">';
            resultHtml += '<h6><i class="fas fa-exclamation-triangle"></i> Detail Error:</h6>';
            resultHtml += '<ul class="mb-0">';
            data.errors.slice(0, 10).forEach(function(error) { // Show max 10 errors
                resultHtml += '<li>' + error + '</li>';
            });
            if (data.errors.length > 10) {
                resultHtml += '<li><em>... dan ' + (data.errors.length - 10) + ' error lainnya</em></li>';
            }
            resultHtml += '</ul>';
            resultHtml += '</div>';
        }
    } else {
        resultHtml += '<div class="alert alert-info">';
        resultHtml += '<h6><i class="fas fa-info-circle"></i> Tidak Ada Data</h6>';
        resultHtml += '<p class="mb-0">Tidak ada data yang berhasil diimport. Semua data mungkin sudah ada atau tidak valid.</p>';
        resultHtml += '</div>';
    }

    $('#import-results-visitasi-sm').html(resultHtml);
}

// Function untuk show import error
function showImportErrorVisitasiSM(message) {
    $('#progress-section-visitasi-sm').hide();
    $('#results-section-visitasi-sm').show();

    const errorHtml = `
        <div class="alert alert-danger">
            <h6><i class="fas fa-times-circle"></i> Import Gagal</h6>
            <p class="mb-0">${message}</p>
        </div>
    `;

    $('#import-results-visitasi-sm').html(errorHtml);
}
