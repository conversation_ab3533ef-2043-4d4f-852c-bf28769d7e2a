<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Check if data was sent
    if (!isset($_POST['excel_data']) || empty($_POST['excel_data'])) {
        throw new Exception('Data Excel tidak ditemukan');
    }
    
    // Parse JSON data
    $rows = json_decode($_POST['excel_data'], true);
    
    if (!is_array($rows)) {
        throw new Exception('Format data tidak valid');
    }
    
    if (empty($rows)) {
        throw new Exception('Tidak ada data untuk diimport');
    }
    
    // Check max rows (100)
    if (count($rows) > 100) {
        throw new Exception('Maksimal 100 baris data. Data Anda memiliki ' . count($rows) . ' baris');
    }
    
    // Get session data
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Process and Insert
    $successCount = 0;
    $errorCount = 0;
    $errors = [];
    
    // Begin transaction
    $conn->autocommit(false);
    
    foreach ($rows as $index => $row) {
        $rowNumber = $index + 2; // +2 because we removed header and array is 0-based
        
        try {
            // Extract data from row (5 columns)
            $npsn = trim($row[0] ?? '');
            $nia_validator = trim($row[1] ?? '');
            $nia_verifikator = trim($row[2] ?? '');
            $tahap = trim($row[3] ?? '');
            $tahun_akreditasi = trim($row[4] ?? '');
            
            // Validate required fields
            if (empty($npsn) || empty($nia_validator) || empty($nia_verifikator) || 
                empty($tahap) || empty($tahun_akreditasi)) {
                throw new Exception("Baris $rowNumber: Data tidak lengkap");
            }
            
            // Validate tahun format
            if (!preg_match('/^\d{4}$/', $tahun_akreditasi)) {
                throw new Exception("Baris $rowNumber: Format tahun tidak valid");
            }
            
            // Validate tahap (numeric)
            if (!is_numeric($tahap) || $tahap < 1) {
                throw new Exception("Baris $rowNumber: Tahap harus angka positif");
            }
            
            // Validate validator dan verifikator tidak sama
            if ($nia_validator === $nia_verifikator) {
                throw new Exception("Baris $rowNumber: Validator dan Verifikator tidak boleh sama");
            }
            
            // Lookup NPSN to get sekolah_id
            $sekolah_query = "SELECT sekolah_id FROM sekolah 
                             WHERE npsn = ? AND provinsi_id = ? AND rumpun = 'paud' 
                             AND soft_delete = '1' AND status_keaktifan_id = '1'";
            $sekolah_stmt = $conn->prepare($sekolah_query);
            $sekolah_stmt->bind_param("si", $npsn, $provinsi_id);
            $sekolah_stmt->execute();
            $sekolah_result = $sekolah_stmt->get_result();
            
            if ($sekolah_result->num_rows === 0) {
                throw new Exception("Baris $rowNumber: NPSN $npsn tidak ditemukan");
            }
            
            $sekolah_data = $sekolah_result->fetch_assoc();
            $sekolah_id = $sekolah_data['sekolah_id'];
            
            // Lookup NIA Validator to get kd_asesor1
            $validator_query = "SELECT kd_asesor FROM asesor 
                               WHERE nia = ? AND provinsi_id = ? 
                               AND soft_delete = '1' AND status_keaktifan_id = '1'";
            $validator_stmt = $conn->prepare($validator_query);
            $validator_stmt->bind_param("si", $nia_validator, $provinsi_id);
            $validator_stmt->execute();
            $validator_result = $validator_stmt->get_result();
            
            if ($validator_result->num_rows === 0) {
                throw new Exception("Baris $rowNumber: NIA Validator $nia_validator tidak ditemukan");
            }
            
            $validator_data = $validator_result->fetch_assoc();
            $kd_asesor1 = $validator_data['kd_asesor'];
            
            // Lookup NIA Verifikator to get kd_asesor2
            $verifikator_query = "SELECT kd_asesor FROM asesor 
                                 WHERE nia = ? AND provinsi_id = ? 
                                 AND soft_delete = '1' AND status_keaktifan_id = '1'";
            $verifikator_stmt = $conn->prepare($verifikator_query);
            $verifikator_stmt->bind_param("si", $nia_verifikator, $provinsi_id);
            $verifikator_stmt->execute();
            $verifikator_result = $verifikator_stmt->get_result();
            
            if ($verifikator_result->num_rows === 0) {
                throw new Exception("Baris $rowNumber: NIA Verifikator $nia_verifikator tidak ditemukan");
            }
            
            $verifikator_data = $verifikator_result->fetch_assoc();
            $kd_asesor2 = $verifikator_data['kd_asesor'];
            
            // Double check: kd_asesor1 dan kd_asesor2 tidak boleh sama
            if ($kd_asesor1 === $kd_asesor2) {
                throw new Exception("Baris $rowNumber: Validator dan Verifikator merujuk ke asesor yang sama");
            }
            
            // Check for duplicate (same sekolah_id and tahun_akreditasi)
            $duplicate_query = "SELECT COUNT(*) as count FROM mapping_paud_validasi 
                               WHERE sekolah_id = ? AND tahun_akreditasi = ? AND provinsi_id = ?";
            $duplicate_stmt = $conn->prepare($duplicate_query);
            $duplicate_stmt->bind_param("isi", $sekolah_id, $tahun_akreditasi, $provinsi_id);
            $duplicate_stmt->execute();
            $duplicate_result = $duplicate_stmt->get_result();
            $duplicate_data = $duplicate_result->fetch_assoc();
            
            if ($duplicate_data['count'] > 0) {
                // Skip duplicate
                continue;
            }
            
            // Insert data
            $insert_query = "INSERT INTO mapping_paud_validasi 
                           (sekolah_id, kd_asesor1, kd_asesor2, tahap, tahun_akreditasi, provinsi_id) 
                           VALUES (?, ?, ?, ?, ?, ?)";
            $insert_stmt = $conn->prepare($insert_query);
            $insert_stmt->bind_param("issssi", $sekolah_id, $kd_asesor1, $kd_asesor2, $tahap, $tahun_akreditasi, $provinsi_id);
            
            if ($insert_stmt->execute()) {
                $successCount++;
            } else {
                throw new Exception("Baris $rowNumber: Gagal menyimpan data");
            }
            
        } catch (Exception $e) {
            $errorCount++;
            $errors[] = $e->getMessage();
        }
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Success response
    $response = [
        'success' => true,
        'message' => 'Import selesai',
        'data' => [
            'success_count' => $successCount,
            'error_count' => $errorCount,
            'errors' => $errors,
            'total_processed' => count($rows)
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
