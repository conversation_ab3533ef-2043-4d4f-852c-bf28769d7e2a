<?php
/**
 * AJAX handler untuk hapus mapping validasi PAUD
 */

require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validate required parameter
    if (!isset($_POST['id_mapping']) || empty($_POST['id_mapping'])) {
        throw new Exception('ID Mapping tidak ditemukan');
    }
    
    $id_mapping = intval($_POST['id_mapping']);
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Validate ID mapping
    if ($id_mapping <= 0) {
        throw new Exception('ID Mapping tidak valid');
    }
    
    // Begin transaction
    $conn->autocommit(false);
    
    // First, check if mapping exists and belongs to current provinsi
    $check_query = "SELECT mp.id_mapping, mp.sekolah_id, mp.tahun_akreditasi, s.npsn, s.nama_sekolah
                    FROM mapping_paud_validasi mp
                    LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
                    WHERE mp.id_mapping = ? AND mp.provinsi_id = ?";
    
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("ii", $id_mapping, $provinsi_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows === 0) {
        throw new Exception('Data mapping tidak ditemukan atau tidak memiliki akses');
    }
    
    $mapping_data = $check_result->fetch_assoc();
    
    // Log the deletion attempt
    error_log("Attempting to delete mapping validasi - ID: {$id_mapping}, NPSN: {$mapping_data['npsn']}, Sekolah: {$mapping_data['nama_sekolah']}, Provinsi: {$provinsi_id}");
    
    // Delete the mapping
    $delete_query = "DELETE FROM mapping_paud_validasi 
                     WHERE id_mapping = ? AND provinsi_id = ?";
    
    $delete_stmt = $conn->prepare($delete_query);
    $delete_stmt->bind_param("ii", $id_mapping, $provinsi_id);
    
    if ($delete_stmt->execute()) {
        $affected_rows = $delete_stmt->affected_rows;
        
        if ($affected_rows > 0) {
            // Commit transaction
            $conn->commit();
            $conn->autocommit(true);
            
            // Log successful deletion
            error_log("Successfully deleted mapping validasi - ID: {$id_mapping}, NPSN: {$mapping_data['npsn']}, Affected rows: {$affected_rows}");
            
            // Success response
            echo json_encode([
                'success' => true,
                'message' => 'Data mapping validasi berhasil dihapus',
                'data' => [
                    'id_mapping' => $id_mapping,
                    'npsn' => $mapping_data['npsn'],
                    'nama_sekolah' => $mapping_data['nama_sekolah'],
                    'affected_rows' => $affected_rows
                ]
            ]);
        } else {
            throw new Exception('Tidak ada data yang dihapus. Data mungkin sudah tidak ada.');
        }
    } else {
        throw new Exception('Gagal menghapus data mapping: ' . $conn->error);
    }
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Error deleting mapping validasi: " . $e->getMessage());
    
    // Error response
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
