<?php
/**
 * AJAX Handler untuk Update Password
 * Mengikuti pola dari tim_it/user_asesor/ajax/update_password.php
 */

// Include required files
require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Asesor', '../../../login.php');

// Set JSON header
header('Content-Type: application/json');

try {
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }

    // Get session user
    $kd_user = $_SESSION['kd_user'] ?? '';

    if (empty($kd_user)) {
        throw new Exception('Session kd_user tidak tersedia');
    }

    // Validate required parameters
    if (!isset($_POST['new_password']) || empty($_POST['new_password'])) {
        throw new Exception('Password baru tidak boleh kosong');
    }

    $new_password = $_POST['new_password'];

    // Start transaction (mengikuti pola asesor.php)
    $conn->autocommit(false);

    // Generate MD5 hash untuk password
    $hashed_password = md5($new_password);

    // Prepare SQL statement (mengikuti pola asesor.php)
    $sql = "UPDATE user SET `password` = ?, `pwd` = ? WHERE kd_user = ?";
    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        throw new Exception('Prepare statement failed: ' . $conn->error);
    }

    // Bind parameters
    $stmt->bind_param("sss", $hashed_password, $new_password, $kd_user);

    // Execute statement
    if (!$stmt->execute()) {
        throw new Exception('Gagal mengupdate password: ' . $stmt->error);
    }

    // Check if any rows were affected
    if ($stmt->affected_rows === 0) {
        throw new Exception('Tidak ada perubahan yang dilakukan');
    }

    // Commit transaction
    $conn->commit();

    // Close statement
    $stmt->close();

    // Success response
    echo json_encode([
        'success' => true,
        'message' => 'Password berhasil diubah'
    ]);

} catch (Exception $e) {
    // Rollback transaction
    $conn->rollback();

    // Error response
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Close connection
$conn->close();
?>
