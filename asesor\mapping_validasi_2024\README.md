# 📋 Mapping Validasi Dasmen IASP 2024

Modul untuk mengelola mapping validasi sekolah Dasmen IASP tahun 2024 dengan sistem upload file dan manajemen dokumen yang terintegrasi.

## 🎯 **FITUR UTAMA**

### 1. **Role-Based Access Control**
- **Validator 1**: Upload File Format 5.1 Berita Acara Hasil Validasi 1
- **Validator 2**: Upload File Format 5.1 Berita Acara Hasil Validasi 2
- **Session Management**: Aks<PERSON> berdasarkan `kd_user` dan `provinsi_id`

### 2. **File Management**
- **Format**: PDF only (maksimal 10MB)
- **Security**: Path traversal prevention, access control
- **Preview**: Clickable badge untuk preview file
- **Upload**: Real-time update tanpa reload browser

### 3. **Smart Display System**
- **Conditional Visibility**: Tombol dan badge sesuai role user
- **Real-time Updates**: Badge berubah langsung setelah upload
- **Visual Feedback**: Animasi untuk menunjukkan perubahan

### 4. **Document Generation**
- **Surat Tugas Validasi**: `mapping_validasi_st_validasi_2024.php`
- **PDF Generation**: Menggunakan DomPDF untuk generate surat
- **New Tab Opening**: Surat dibuka di tab baru untuk kemudahan akses

## 🗄️ Database Schema

### **Tabel Utama: `mapping_validasi_2024`**
```sql
- id_mapping (PK)
- sekolah_id (FK → sekolah)
- kd_asesor1 (FK → asesor_1)
- kd_asesor2 (FK → asesor_2)
- tgl_mulai_validasi, tgl_akhir_validasi
- file_format_5_1_berita_acara_hasil_validasi_1
- file_format_5_1_berita_acara_hasil_validasi_2
- provinsi_id (FK → filtering)
```

### **Relasi Tabel:**
```sql
mapping_validasi_2024 → sekolah (sekolah_id)
mapping_validasi_2024 → asesor_1 (kd_asesor1)
mapping_validasi_2024 → asesor_2 (kd_asesor2)
sekolah → jenjang (jenjang_id)
sekolah → kab_kota (kota_id)
```

## 📁 Struktur File

```
asesor/mapping_validasi_2024/
├── validasi.php                                    # Main interface
├── ajax/
│   └── upload_file.php                             # File upload handler
├── js/
│   └── mapping_validasi.js                        # JavaScript functions
├── preview_file.php                               # File preview handler
├── mapping_validasi_st_validasi_2024.php         # PDF Generator
└── README.md                                      # Documentation
```

## 🎨 Interface Features

### **5 Kolom Utama:**
1. **NO**: Auto increment
2. **SEKOLAH**: NPSN, Nama, Jenjang, Kab/Kota, Kepsek, Tahap
3. **VALIDATOR**: Data Validator 1 & 2 (NIA, Nama, Kab/Kota, No HP)
4. **FORM UPLOAD DOKUMEN**: Tombol upload berdasarkan role
5. **DOKUMEN UNGGAHAN**: Status badge dengan preview
6. **JADWAL DAN AKSI**: Tanggal validasi dan download surat

### **Upload Directories:**
```
Format 5.1 Validator 1: ../../../../simak/files/upload_file_format_5_1_berita_acara_hasil_validasi_1/
Format 5.1 Validator 2: ../../../../simak/files/upload_file_format_5_1_berita_acara_hasil_validasi_2/
```

## ⚡ JavaScript Features

### 1. **Upload Management**
```javascript
- File validation (PDF, 10MB max)
- Real-time badge update tanpa reload
- Error handling & user feedback
- Instant visual feedback dengan animasi
```

### 2. **User Experience**
```javascript
- Real-time updates tanpa reload
- Keyboard shortcuts (ESC)
- Visual feedback dengan animasi
- Minimal notification system (error only)
- Document download di tab baru
- Production ready - clean code tanpa debugging
```

## 🔒 Security Features

### **Multi-Layer Validation:**
- **Session Check**: `requireLevel('Asesor')`
- **Access Control**: User hanya bisa akses mapping mereka
- **File Validation**: PDF only, max 10MB
- **Path Security**: Basename sanitization
- **SQL Injection**: Prepared statements dan escaping

### **Role-Based Permissions:**
- **Validator 1**: Hanya bisa upload file untuk asesor1
- **Validator 2**: Hanya bisa upload file untuk asesor2
- **Province Isolation**: Data terisolasi per provinsi

## 🚀 Performance Optimizations

### **Database Query:**
- **Optimized JOINs**: Mengganti subquery dengan JOIN
- **Proper Indexing**: Menggunakan foreign key relationships
- **Filtered Results**: WHERE clause yang efisien

### **Frontend:**
- **Real-time Updates**: Tanpa reload browser
- **Targeted DOM Updates**: Hanya update element yang berubah
- **Efficient Selectors**: ID-based targeting

## 📊 File Upload Flow

```
1. User klik tombol upload (role-based)
2. Modal terbuka dengan form upload
3. User pilih file PDF (max 10MB)
4. AJAX upload ke server
5. Server validasi file & permissions
6. File disimpan ke direktori yang tepat
7. Database diupdate dengan filename
8. Badge status berubah real-time
9. Modal tertutup otomatis
```

## 🎯 Pattern Alignment

### **Mengikuti Pattern dari Modul Visitasi:**
- **PAUD Pattern**: Parameter `kode` untuk surat
- **Real-time Updates**: Badge updates tanpa reload
- **Security**: Multi-layer validation
- **Clean Architecture**: Dedicated files, no redundancy
- **User Experience**: Smooth interactions

## 📈 Benefits

### **👤 User Benefits:**
- **Instant Feedback**: Upload langsung terlihat hasilnya
- **Role-based Interface**: Hanya tampil yang relevan
- **Professional Documents**: Surat resmi yang rapi
- **Easy Navigation**: Interface yang intuitive

### **🔧 Technical Benefits:**
- **Scalable**: Mudah ditambah file type baru
- **Maintainable**: Code yang clean dan modular
- **Secure**: Access control yang proper
- **Efficient**: Direct file access tanpa processing berlebih

## 🧪 Testing Checklist

- ✅ **Role-based Access**: Validator 1 & 2 permissions
- ✅ **File Upload**: PDF validation dan size limit
- ✅ **Real-time Updates**: Badge changes tanpa reload
- ✅ **Security**: Access control dan file validation
- ✅ **Document Generation**: Surat tugas PDF output
- ✅ **Error Handling**: Proper error messages

**Status: ✅ Production ready - fully functional mapping validasi module!**
