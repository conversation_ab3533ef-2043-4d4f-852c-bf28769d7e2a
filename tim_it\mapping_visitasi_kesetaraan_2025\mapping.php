<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mapping Asesor Visitasi <PERSON> - SIMAK</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    
    <!-- Custom CSS -->
    <style>
        .content-wrapper {
            margin-left: 250px;
            padding: 20px;
        }
        
        .card-header {
            background-color: #17a2b8;
            color: white;
        }
        
        .btn-action {
            margin: 2px;
        }
        
        .table-responsive {
            margin-top: 20px;
        }
        
        .header-with-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-buttons .btn {
            white-space: nowrap;
        }

        .btn.btn-input-mapping {
            background-color: white !important;
            color: #007bff !important;
            border: 1px solid #007bff !important;
            transition: all 0.3s ease;
        }

        .btn.btn-input-mapping:hover {
            background-color: #ffc107 !important;
            color: #000000 !important;
            border-color: #ffc107 !important;
        }

        .btn.btn-input-mapping:focus {
            background-color: #ffc107 !important;
            color: #000000 !important;
            border-color: #ffc107 !important;
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
        }

        .btn.btn-input-mapping:active {
            background-color: #ffc107 !important;
            color: #000000 !important;
            border-color: #ffc107 !important;
        }

        /* Styling untuk file clickable */
        .file-clickable:hover {
            opacity: 0.8;
            transform: scale(1.05);
            transition: all 0.2s ease;
        }

        .file-clickable:active {
            transform: scale(0.95);
        }

        /* Modal stacking - Edit modals di atas modal detail */
        #modalEditTanggal, #modalEditAsesor {
            z-index: 1060 !important;
        }

        #modalEditTanggal .modal-backdrop, #modalEditAsesor .modal-backdrop {
            z-index: 1055 !important;
        }

        /* Styling untuk form edit */
        #modalEditTanggal .form-control:focus, #modalEditAsesor .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        /* Alert styling dalam modal edit asesor */
        #modalEditAsesor .alert-info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }

        @media (max-width: 768px) {
            .content-wrapper {
                margin-left: 0;
                padding: 10px;
            }

            .header-with-buttons {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .action-buttons {
                width: 100%;
                justify-content: flex-start;
            }

            .action-buttons .btn {
                flex: 1;
                min-width: 120px;
            }
        }
    </style>
</head>
<body>

<!-- Include header -->
<?php include '../header.php'; ?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Main Content -->
<div class="content-wrapper">
    <div class="container-fluid">
        
        <!-- Page Header -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="header-with-buttons">
                            <h4 class="mb-0">
                                <i class="fas fa-users"></i> Mapping Asesor Visitasi Kesetaraan
                            </h4>

                            <!-- Action Buttons -->
                            <div class="action-buttons">
                                <button type="button" class="btn btn-input-mapping" id="btn-input-mapping">
                                    <i class="fas fa-plus"></i> Input Data Mapping
                                </button>

                                <button type="button" class="btn btn-success" id="btn-export-excel">
                                    <i class="fas fa-file-excel"></i> Export Excel
                                </button>

                                <button type="button" class="btn btn-info" id="btn-import-excel">
                                    <i class="fas fa-file-upload"></i> Import Excel
                                </button>

                                <button type="button" class="btn btn-warning" id="btn-tahun-akreditasi">
                                    <i class="fas fa-calendar-alt"></i> Tahun Akreditasi
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        
                        <!-- DataTable -->
                        <div class="table-responsive">
                            <table id="table-mapping-visitasi" class="table table-striped table-bordered table-hover" style="width:100%">
                                <thead class="bg-primary text-white">
                                    <tr>
                                        <th>NO <br> &nbsp;</th>
                                        <th>NPSN <br> &nbsp;</th>
                                        <th>NAMA <br> SEKOLAH</th>
                                        <th>JENJANG <br> &nbsp;</th>
                                        <th>KAB/KOTA <br> &nbsp</th>
                                        <th>NIA <br> ASESOR 1</th>
                                        <th>NAMA <br> ASESOR 1</th>
                                        <th>NIA <br> ASESOR 2</th>
                                        <th>NAMA <br> ASESOR 2</th>
                                        <th>TAHUN <br> AKREDITASI</th>
                                        <th>TAHAP <br> VISITASI</th>
                                        <th>AKSI</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data akan dimuat via AJAX -->
                                </tbody>
                            </table>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
        
    </div>
</div>

<!-- Modal Input Data Mapping -->
<div class="modal fade" id="modalInputMapping" tabindex="-1" aria-labelledby="modalInputMappingLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #17a2b8; color: white;">
                <h5 class="modal-title" id="modalInputMappingLabel">
                    <i class="fas fa-plus"></i> Input Data Mapping Visitasi Kesetaraan
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="formInputMapping">
                    <div class="row">
                        <!-- Kolom Kiri -->
                        <div class="col-md-6">
                            <!-- NPSN Sekolah -->
                            <div class="mb-3">
                                <label for="npsn_sekolah" class="form-label">NPSN Sekolah <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="npsn_sekolah" name="npsn_sekolah" required>
                                <input type="hidden" id="sekolah_id" name="sekolah_id">
                                <div class="invalid-feedback" id="npsn_feedback"></div>
                                <small class="text-muted" id="sekolah_info"></small>
                            </div>

                            <!-- NIA Asesor 1 -->
                            <div class="mb-3">
                                <label for="nia_asesor1" class="form-label">NIA Asesor 1 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nia_asesor1" name="nia_asesor1" required>
                                <input type="hidden" id="kd_asesor1" name="kd_asesor1">
                                <div class="invalid-feedback" id="asesor1_feedback"></div>
                                <small class="text-muted" id="asesor1_info"></small>
                            </div>

                            <!-- NIA Asesor 2 -->
                            <div class="mb-3">
                                <label for="nia_asesor2" class="form-label">NIA Asesor 2 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nia_asesor2" name="nia_asesor2" required>
                                <input type="hidden" id="kd_asesor2" name="kd_asesor2">
                                <div class="invalid-feedback" id="asesor2_feedback"></div>
                                <small class="text-muted" id="asesor2_info"></small>
                            </div>

                            <!-- Tahun Akreditasi -->
                            <div class="mb-3">
                                <label for="tahun_akreditasi" class="form-label">Tahun Akreditasi <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="tahun_akreditasi" name="tahun_akreditasi" required maxlength="4" pattern="[0-9]{4}">
                                <div class="invalid-feedback">Tahun akreditasi wajib diisi (format: YYYY)</div>
                            </div>

                            <!-- Tahap Ke -->
                            <div class="mb-3">
                                <label for="tahap" class="form-label">Tahap Ke <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="tahap" name="tahap" required min="1" max="10">
                                <div class="invalid-feedback">Tahap wajib diisi (1-10)</div>
                            </div>

                            <!-- Tanggal Pra-Visitasi -->
                            <div class="mb-3">
                                <label for="tgl_pra_visitasi" class="form-label">Tanggal Pra-Visitasi</label>
                                <input type="date" class="form-control" id="tgl_pra_visitasi" name="tgl_pra_visitasi">
                            </div>
                        </div>

                        <!-- Kolom Kanan -->
                        <div class="col-md-6">
                            <!-- Tanggal Surat Tugas Pra-Visitasi -->
                            <div class="mb-3">
                                <label for="tgl_surat_tugas_pra_visitasi" class="form-label">Tanggal Surat Tugas Pra-Visitasi</label>
                                <input type="date" class="form-control" id="tgl_surat_tugas_pra_visitasi" name="tgl_surat_tugas_pra_visitasi">
                            </div>

                            <!-- Nomor Surat Tugas Pra-Visitasi -->
                            <div class="mb-3">
                                <label for="no_surat_tugas_pra_visitasi" class="form-label">Nomor Surat Tugas Pra-Visitasi</label>
                                <input type="text" class="form-control" id="no_surat_tugas_pra_visitasi" name="no_surat_tugas_pra_visitasi">
                            </div>

                            <!-- Tanggal Mulai Visitasi -->
                            <div class="mb-3">
                                <label for="tgl_mulai_visitasi" class="form-label">Tanggal Mulai Visitasi</label>
                                <input type="date" class="form-control" id="tgl_mulai_visitasi" name="tgl_mulai_visitasi">
                            </div>

                            <!-- Tanggal Akhir Visitasi -->
                            <div class="mb-3">
                                <label for="tgl_akhir_visitasi" class="form-label">Tanggal Akhir Visitasi</label>
                                <input type="date" class="form-control" id="tgl_akhir_visitasi" name="tgl_akhir_visitasi">
                            </div>

                            <!-- Tanggal Surat Tugas Visitasi -->
                            <div class="mb-3">
                                <label for="tgl_surat_tugas_visitasi" class="form-label">Tanggal Surat Tugas Visitasi</label>
                                <input type="date" class="form-control" id="tgl_surat_tugas_visitasi" name="tgl_surat_tugas_visitasi">
                            </div>

                            <!-- Nomor Surat Tugas Visitasi -->
                            <div class="mb-3">
                                <label for="no_surat_tugas_visitasi" class="form-label">Nomor Surat Tugas Visitasi</label>
                                <input type="text" class="form-control" id="no_surat_tugas_visitasi" name="no_surat_tugas_visitasi">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> Batal
                </button>
                <button type="button" class="btn btn-primary" id="btnSimpanMapping">
                    <i class="fas fa-save"></i> Simpan Data
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Tahun Akreditasi -->
<div class="modal fade" id="modalTahunAkreditasi" tabindex="-1" aria-labelledby="modalTahunAkreditasiLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTahunAkreditasiLabel">
                    <i class="fas fa-calendar-alt"></i> Edit Tahun Akreditasi
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="formTahunAkreditasi">
                    <div class="mb-3">
                        <label for="tahunAkreditasi" class="form-label">Tahun Akreditasi</label>
                        <input type="number" class="form-control" id="tahunAkreditasi" name="tahun_akreditasi"
                               min="2020" max="2030" placeholder="2025" required>
                        <div class="form-text">Format: YYYY (contoh: 2025)</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> Batal
                </button>
                <button type="button" class="btn btn-primary" id="btnSimpanTahun">
                    <i class="fas fa-save"></i> Simpan
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail Mapping Visitasi Kesetaraan -->
<div class="modal fade" id="modalDetailMapping" tabindex="-1" aria-labelledby="modalDetailMappingLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalDetailMappingLabel">
                    <i class="fas fa-info-circle"></i> Detail Mapping Visitasi Kesetaraan
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- Kolom 1: Data Sekolah -->
                    <div class="col-md-2">
                        <div class="card h-100">
                            <div class="card-header text-white" style="background-color: #87CEEB;">
                                <h6 class="mb-0"><i class="fas fa-school"></i> Data Sekolah</h6>
                            </div>
                            <div class="card-body p-2">
                                <table class="table table-sm table-borderless">
                                    <tr><td class="fw-bold">NPSN:</td><td id="detail-npsn">-</td></tr>
                                    <tr><td class="fw-bold">Nama Sekolah:</td><td id="detail-nama-sekolah">-</td></tr>
                                    <tr><td class="fw-bold">Jenjang:</td><td id="detail-jenjang">-</td></tr>
                                    <tr><td class="fw-bold">Kab/Kota:</td><td id="detail-kota-sekolah">-</td></tr>
                                    <tr><td class="fw-bold">Nama Kepsek:</td><td id="detail-nama-kepsek">-</td></tr>
                                    <tr><td class="fw-bold">HP Kepsek:</td><td id="detail-hp-kepsek">-</td></tr>
                                    <tr><td class="fw-bold">WA Kepsek:</td><td id="detail-wa-kepsek">-</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Kolom 2: Data Asesor -->
                    <div class="col-md-2">
                        <div class="card h-100">
                            <div class="card-header text-white" style="background-color: #87CEEB;">
                                <h6 class="mb-0"><i class="fas fa-users"></i> Data Asesor</h6>
                            </div>
                            <div class="card-body p-2">
                                <table class="table table-sm table-borderless">
                                    <tr><td colspan="2" class="fw-bold text-center bg-light">Asesor 1</td></tr>
                                    <tr><td class="fw-bold">NIA:</td><td id="detail-nia1">-</td></tr>
                                    <tr><td class="fw-bold">Nama:</td><td id="detail-nama-asesor1">-</td></tr>
                                    <tr><td class="fw-bold">No HP:</td><td id="detail-hp-asesor1">-</td></tr>
                                    <tr><td class="fw-bold">Kab/Kota:</td><td id="detail-kota-asesor1">-</td></tr>
                                    <tr><td colspan="2" class="fw-bold text-center bg-light">Asesor 2</td></tr>
                                    <tr><td class="fw-bold">NIA:</td><td id="detail-nia2">-</td></tr>
                                    <tr><td class="fw-bold">Nama:</td><td id="detail-nama-asesor2">-</td></tr>
                                    <tr><td class="fw-bold">No HP:</td><td id="detail-hp-asesor2">-</td></tr>
                                    <tr><td class="fw-bold">Kab/Kota:</td><td id="detail-kota-asesor2">-</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Kolom 3: Dokumen Unggahan -->
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-header text-white" style="background-color: #87CEEB;">
                                <h6 class="mb-0"><i class="fas fa-file-upload"></i> Dokumen Unggahan</h6>
                            </div>
                            <div class="card-body p-2">
                                <table class="table table-sm table-borderless">
                                    <tr><td class="fw-bold">Format 3.1 Penilaian Pra-Visitasi 1:</td><td id="status-file-3-1-1">-</td></tr>
                                    <tr><td class="fw-bold">Format 3.1 Penilaian Pra-Visitasi 2:</td><td id="status-file-3-1-2">-</td></tr>
                                    <tr><td class="fw-bold">Format 3.2 LK Penggalian Data Pra-Visitasi 1:</td><td id="status-file-3-2-1">-</td></tr>
                                    <tr><td class="fw-bold">Format 3.2 LK Penggalian Data Pra-Visitasi 2:</td><td id="status-file-3-2-2">-</td></tr>
                                    <tr><td class="fw-bold">Format 4.1 Surat Tugas Visitasi:</td><td id="status-file-4-1">-</td></tr>
                                    <tr><td class="fw-bold">Format 4.2 Pakta Integritas 1:</td><td id="status-file-4-2-1">-</td></tr>
                                    <tr><td class="fw-bold">Format 4.2 Pakta Integritas 2:</td><td id="status-file-4-2-2">-</td></tr>
                                    <tr><td class="fw-bold">Format 4.3 Rekap Penggalian Data Penilaian 1:</td><td id="status-file-4-3-1">-</td></tr>
                                    <tr><td class="fw-bold">Format 4.3 Rekap Penggalian Data Penilaian 2:</td><td id="status-file-4-3-2">-</td></tr>
                                    <tr><td class="fw-bold">Format 4.4 Berita Acara Visitasi:</td><td id="status-file-4-4">-</td></tr>
                                    <tr><td class="fw-bold">Format 4.5 Laporan Individu 1:</td><td id="status-file-4-5-1">-</td></tr>
                                    <tr><td class="fw-bold">Format 4.5 Laporan Individu 2:</td><td id="status-file-4-5-2">-</td></tr>
                                    <tr><td class="fw-bold">Format 4.5 Laporan Kelompok:</td><td id="status-file-4-5-kelompok">-</td></tr>
                                    <tr><td class="fw-bold">Format 4.5 Catatan Dan Saran:</td><td id="status-file-4-5-catatan">-</td></tr>
                                    <tr><td class="fw-bold">File Foto Visitasi:</td><td id="status-file-foto">-</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Kolom 4: Pelaksanaan Kegiatan -->
                    <div class="col-md-2">
                        <div class="card h-100">
                            <div class="card-header text-white" style="background-color: #87CEEB;">
                                <h6 class="mb-0"><i class="fas fa-calendar-check"></i> Pelaksanaan Kegiatan</h6>
                            </div>
                            <div class="card-body p-2">
                                <table class="table table-sm table-borderless">
                                    <tr><td class="fw-bold">Tanggal Pra-Visitasi:</td><td id="detail-tgl-pra-visitasi">-</td></tr>
                                    <tr><td class="fw-bold">No. ST Pra-Visitasi:</td><td id="detail-no-st-pra">-</td></tr>
                                    <tr><td class="fw-bold">Tanggal ST Pra-Visitasi:</td><td id="detail-tgl-st-pra">-</td></tr>
                                    <tr><td class="fw-bold">Tanggal Visitasi Dimulai:</td><td id="detail-tgl-mulai-visitasi">-</td></tr>
                                    <tr><td class="fw-bold">Tanggal Visitasi Berakhir:</td><td id="detail-tgl-akhir-visitasi">-</td></tr>
                                    <tr><td class="fw-bold">No. ST Visitasi:</td><td id="detail-no-st-visitasi">-</td></tr>
                                    <tr><td class="fw-bold">Tanggal ST Visitasi:</td><td id="detail-tgl-st-visitasi">-</td></tr>
                                    <tr><td class="fw-bold">Tahap:</td><td id="detail-tahap">-</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Kolom 5: Aksi -->
                    <div class="col-md-2">
                        <div class="card h-100">
                            <div class="card-header text-white" style="background-color: #87CEEB;">
                                <h6 class="mb-0"><i class="fas fa-cogs"></i> Aksi</h6>
                            </div>
                            <div class="card-body p-2">
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-warning btn-sm" id="btn-edit-asesor">
                                        <i class="fas fa-user-edit"></i> Edit Asesor
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm" id="btn-edit-tanggal">
                                        <i class="fas fa-calendar-edit"></i> Edit Tanggal
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm" id="btn-hapus">
                                        <i class="fas fa-trash"></i> Hapus
                                    </button>
                                    <button type="button" class="btn btn-success btn-sm" id="btn-download-st-visitasi">
                                        <i class="fas fa-download"></i> ST Visitasi
                                    </button>
                                    <button type="button" class="btn btn-primary btn-sm" id="btn-download-st-pra">
                                        <i class="fas fa-download"></i> ST Pra-Visitasi
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Tanggal Visitasi -->
<div class="modal fade" id="modalEditTanggal" tabindex="-1" aria-labelledby="modalEditTanggalLabel" aria-hidden="true" style="z-index: 1060;">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalEditTanggalLabel">
                    <i class="fas fa-calendar-edit"></i> Edit Tanggal Visitasi
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="formEditTanggal">
                    <input type="hidden" id="editTanggalIdMapping" name="id_mapping">

                    <div class="mb-3">
                        <label for="editTglMulaiVisitasi" class="form-label">Tanggal Visitasi Dimulai</label>
                        <input type="date" class="form-control" id="editTglMulaiVisitasi" name="tgl_mulai_visitasi" required>
                    </div>

                    <div class="mb-3">
                        <label for="editTglAkhirVisitasi" class="form-label">Tanggal Visitasi Berakhir</label>
                        <input type="date" class="form-control" id="editTglAkhirVisitasi" name="tgl_akhir_visitasi" required>
                    </div>

                    <div class="form-text">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            Tanggal akhir harus sama atau setelah tanggal mulai
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> Batal
                </button>
                <button type="button" class="btn btn-primary" id="btnUpdateTanggal">
                    <i class="fas fa-save"></i> Update Perubahan
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Asesor -->
<div class="modal fade" id="modalEditAsesor" tabindex="-1" aria-labelledby="modalEditAsesorLabel" aria-hidden="true" style="z-index: 1060;">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalEditAsesorLabel">
                    <i class="fas fa-user-edit"></i> Edit Asesor
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="formEditAsesor">
                    <input type="hidden" id="editAsesorIdMapping" name="id_mapping">

                    <div class="mb-3">
                        <label for="editNiaAsesor1" class="form-label">NIA Asesor 1</label>
                        <input type="text" class="form-control" id="editNiaAsesor1" name="nia_asesor1"
                               placeholder="Masukkan NIA Asesor 1" required>
                        <div class="form-text">
                            <small class="text-muted">Contoh: ASR001</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editNiaAsesor2" class="form-label">NIA Asesor 2</label>
                        <input type="text" class="form-control" id="editNiaAsesor2" name="nia_asesor2"
                               placeholder="Masukkan NIA Asesor 2" required>
                        <div class="form-text">
                            <small class="text-muted">Contoh: ASR002</small>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <small>
                            <i class="fas fa-info-circle"></i>
                            NIA Asesor 1 dan Asesor 2 harus berbeda
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> Batal
                </button>
                <button type="button" class="btn btn-primary" id="btnUpdateAsesor">
                    <i class="fas fa-save"></i> Update Perubahan
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Import Excel -->
<div class="modal fade" id="modal-import-excel-visitasi-kesetaraan" tabindex="-1" aria-labelledby="modal-import-excel-visitasi-kesetaraan-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #17a2b8; color: white;">
                <h5 class="modal-title" id="modal-import-excel-visitasi-kesetaraan-label">
                    <i class="fas fa-file-upload"></i> Import Data Mapping Visitasi Kesetaraan
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- File Upload Section -->
                <div id="upload-section-visitasi-kesetaraan">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="excel-file-visitasi-kesetaraan" class="form-label">Pilih File Excel <span class="text-danger">*</span></label>
                                <input type="file" class="form-control" id="excel-file-visitasi-kesetaraan" name="excel_file_visitasi_kesetaraan"
                                       accept=".xlsx,.xls" required>
                                <div class="form-text">
                                    <i class="fas fa-info-circle"></i>
                                    Format yang didukung: .xlsx, .xls (maksimal 100 baris)
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-info" id="petunjuk-import-visitasi-kesetaraan">
                                <h6><i class="fas fa-lightbulb"></i> Petunjuk Import:</h6>
                                <ul class="mb-0">
                                    <li>Download template Excel terlebih dahulu</li>
                                    <li>Isi data sesuai format yang tersedia</li>
                                    <li>Pastikan Asesor 1 dan Asesor 2 berbeda</li>
                                    <li>Hapus baris contoh sebelum import</li>
                                    <li>Pastikan format tanggal: yyyy-mm-dd</li>
                                    <li>NPSN yang sudah ada di tahun yang sama akan di-skip</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Section (hidden initially) -->
                <div id="progress-section-visitasi-kesetaraan" style="display: none;">
                    <div class="row">
                        <div class="col-md-12">
                            <h6 id="progress-title-visitasi-kesetaraan">Memproses import...</h6>
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     id="import-progress-bar-visitasi-kesetaraan" role="progressbar"
                                     style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                    0%
                                </div>
                            </div>
                            <p id="progress-message-visitasi-kesetaraan" class="text-muted">Memulai proses import...</p>
                        </div>
                    </div>
                </div>

                <!-- Results Section (hidden initially) -->
                <div id="results-section-visitasi-kesetaraan" style="display: none;">
                    <div class="row">
                        <div class="col-md-12">
                            <div id="import-results-visitasi-kesetaraan"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="btn-cancel-import-visitasi-kesetaraan">
                    <i class="fas fa-times"></i> Batal
                </button>
                <button type="button" class="btn btn-outline-info" id="btn-download-template-visitasi-kesetaraan">
                    <i class="fas fa-download"></i> Download Template
                </button>
                <button type="button" class="btn btn-info" id="btn-start-import-visitasi-kesetaraan">
                    <i class="fas fa-upload"></i> Mulai Import
                </button>
            </div>
        </div>
    </div>
</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<!-- DataTables JS -->
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Include XLSX library untuk export Excel -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<!-- Custom JavaScript -->
<script src="js/mapping_visitasi.js"></script>

<!-- Include footer -->
<?php include '../footer.php'; ?>

</body>
</html>
