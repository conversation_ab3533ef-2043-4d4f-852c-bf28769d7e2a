<?php
/**
 * AJAX handler untuk upload file visitasi Dasmen IASP 2024
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Asesor', '../../../login.php');

header('Content-Type: application/json');

// Set timezone
date_default_timezone_set('Asia/Singapore');

try {
    // Validasi request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }
    
    // Validasi input
    if (!isset($_POST['id_mapping']) || empty($_POST['id_mapping'])) {
        throw new Exception('ID Mapping tidak valid');
    }
    
    if (!isset($_POST['file_type']) || empty($_POST['file_type'])) {
        throw new Exception('File type tidak valid');
    }
    
    if (!isset($_FILES['file_visitasi']) || $_FILES['file_visitasi']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File tidak valid atau gagal diupload');
    }
    
    // Get data from POST
    $id_mapping = mysqli_real_escape_string($conn, $_POST['id_mapping']);
    $file_type = mysqli_real_escape_string($conn, $_POST['file_type']);
    
    // Validasi file extension
    $allowed_extensions = ['pdf'];
    $file_extension = strtolower(pathinfo($_FILES['file_visitasi']['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_extensions)) {
        throw new Exception('Format file harus PDF');
    }
    
    // Validasi file size (10MB max)
    $max_size = 10 * 1024 * 1024; // 10MB
    if ($_FILES['file_visitasi']['size'] > $max_size) {
        throw new Exception('Ukuran file maksimal 10MB');
    }
    
    // Cek apakah mapping exists dan milik asesor yang login
    $kd_user = mysqli_real_escape_string($conn, $_SESSION['kd_user'] ?? '');
    $provinsi_id = mysqli_real_escape_string($conn, $_SESSION['provinsi_id'] ?? '');
    
    $check_query = "SELECT m.*, m.kd_asesor1, m.kd_asesor2
                    FROM mapping_2024 m
                    WHERE m.id_mapping = '$id_mapping' 
                        AND (m.kd_asesor1 = '$kd_user' OR m.kd_asesor2 = '$kd_user')
                        AND m.provinsi_id = '$provinsi_id'";
    
    $check_result = mysqli_query($conn, $check_query);
    
    if (!$check_result || mysqli_num_rows($check_result) === 0) {
        throw new Exception('Data mapping tidak ditemukan atau tidak memiliki akses');
    }
    
    $mapping_data = mysqli_fetch_assoc($check_result);
    
    // Validasi akses berdasarkan file type dan asesor
    $asesor1_files = [
        'format_3_1_pra_visitasi_1', 'format_3_2_lk_pra_visitasi_1', 'format_4_1_surat_tugas',
        'format_4_2_pakta_1', 'format_4_3_rekap_1', 'format_4_4_berita_acara',
        'format_4_5_laporan_1', 'format_4_5_laporan_kelompok', 'format_4_5_catatan_saran', 'foto_visitasi'
    ];
    
    $asesor2_files = [
        'format_3_1_pra_visitasi_2', 'format_3_2_lk_pra_visitasi_2', 'format_4_2_pakta_2',
        'format_4_3_rekap_2', 'format_4_5_laporan_2'
    ];
    
    if (in_array($file_type, $asesor1_files) && $kd_user != $mapping_data['kd_asesor1']) {
        throw new Exception('Anda tidak memiliki akses untuk upload file ini');
    }
    
    if (in_array($file_type, $asesor2_files) && $kd_user != $mapping_data['kd_asesor2']) {
        throw new Exception('Anda tidak memiliki akses untuk upload file ini');
    }
    
    // Get field name dan upload directory untuk database
    $field_mapping = [
        'format_3_1_pra_visitasi_1' => [
            'field' => 'file_format_3_1_hasil_penilaian_pra_visitasi_1',
            'dir' => '../../../../simak/files/upload_file_format_3_1_hasil_penilaian_pra_visitasi_1/'
        ],
        'format_3_1_pra_visitasi_2' => [
            'field' => 'file_format_3_1_hasil_penilaian_pra_visitasi_2',
            'dir' => '../../../../simak/files/upload_file_format_3_1_hasil_penilaian_pra_visitasi_2/'
        ],
        'format_3_2_lk_pra_visitasi_1' => [
            'field' => 'file_format_3_2_lk_penggalian_data_pra_visitasi_1',
            'dir' => '../../../../simak/files/upload_file_format_3_2_lk_penggalian_data_pra_visitasi_1/'
        ],
        'format_3_2_lk_pra_visitasi_2' => [
            'field' => 'file_format_3_2_lk_penggalian_data_pra_visitasi_2',
            'dir' => '../../../../simak/files/upload_file_format_3_2_lk_penggalian_data_pra_visitasi_2/'
        ],
        'format_4_1_surat_tugas' => [
            'field' => 'file_format_4_1_surat_tugas_visitasi',
            'dir' => '../../../../simak/files/upload_file_format_4_1_surat_tugas_visitasi/'
        ],
        'format_4_2_pakta_1' => [
            'field' => 'file_format_4_2_pakta_integritas_1',
            'dir' => '../../../../simak/files/upload_file_format_4_2_pakta_integritas_1/'
        ],
        'format_4_2_pakta_2' => [
            'field' => 'file_format_4_2_pakta_integritas_2',
            'dir' => '../../../../simak/files/upload_file_format_4_2_pakta_integritas_2/'
        ],
        'format_4_3_rekap_1' => [
            'field' => 'file_format_4_3_lembar_rekap_penggalian_data_penilaian_1',
            'dir' => '../../../../simak/files/upload_file_format_4_3_lembar_rekap_penggalian_data_penilaian_1/'
        ],
        'format_4_3_rekap_2' => [
            'field' => 'file_format_4_3_lembar_rekap_penggalian_data_penilaian_2',
            'dir' => '../../../../simak/files/upload_file_format_4_3_lembar_rekap_penggalian_data_penilaian_2/'
        ],
        'format_4_4_berita_acara' => [
            'field' => 'file_format_4_4_berita_acara_visitasi',
            'dir' => '../../../../simak/files/upload_file_format_4_4_berita_acara_visitasi/'
        ],
        'format_4_5_laporan_1' => [
            'field' => 'file_format_4_5_laporan_individu_1',
            'dir' => '../../../../simak/files/upload_file_format_4_5_laporan_individu_1/'
        ],
        'format_4_5_laporan_2' => [
            'field' => 'file_format_4_5_laporan_individu_2',
            'dir' => '../../../../simak/files/upload_file_format_4_5_laporan_individu_2/'
        ],
        'format_4_5_laporan_kelompok' => [
            'field' => 'file_format_4_5_laporan_kelompok',
            'dir' => '../../../../simak/files/upload_file_format_4_5_laporan_kelompok/'
        ],
        'format_4_5_catatan_saran' => [
            'field' => 'file_format_4_5_catatan_dan_saran',
            'dir' => '../../../../simak/files/upload_file_format_4_5_catatan_dan_saran/'
        ],
        'foto_visitasi' => [
            'field' => 'file_foto_visitasi_2024',
            'dir' => '../../../../simak/files/upload_file_foto_visitasi_2024/'
        ]
    ];
    
    if (!isset($field_mapping[$file_type])) {
        throw new Exception('Jenis file tidak valid');
    }
    
    $db_field = $field_mapping[$file_type]['field'];
    $upload_dir = $field_mapping[$file_type]['dir'];
    
    // Pastikan direktori upload exists
    if (!is_dir($upload_dir) || !is_writable($upload_dir)) {
        throw new Exception('Direktori upload tidak tersedia atau tidak dapat ditulis');
    }
    
    // Hapus file lama jika ada
    if (!empty($mapping_data[$db_field])) {
        $old_file_path = $upload_dir . $mapping_data[$db_field];
        if (file_exists($old_file_path)) {
            unlink($old_file_path);
        }
    }
    
    // Generate nama file baru
    $temp = explode('.', $_FILES['file_visitasi']['name']);
    $nama_baru = round(microtime(true)) . '.' . end($temp);
    $nama_baru_escaped = mysqli_real_escape_string($conn, $nama_baru);

    // Upload file baru
    $upload_path = $upload_dir . $nama_baru;

    if (!move_uploaded_file($_FILES['file_visitasi']['tmp_name'], $upload_path)) {
        throw new Exception('Gagal mengupload file ke server');
    }

    // Update database
    $update_query = "UPDATE mapping_2024
                     SET $db_field = '$nama_baru_escaped'
                     WHERE id_mapping = '$id_mapping'";
    
    $update_result = mysqli_query($conn, $update_query);
    
    if (!$update_result) {
        // Jika update database gagal, hapus file yang sudah diupload
        if (file_exists($upload_path)) {
            unlink($upload_path);
        }
        $db_error = mysqli_error($conn);
        throw new Exception('Gagal menyimpan data ke database: ' . $db_error);
    }
    
    $affected_rows = mysqli_affected_rows($conn);
    
    if ($affected_rows === 0) {
        throw new Exception('Tidak ada data yang diupdate, periksa ID mapping');
    }
    
    // Response sukses
    echo json_encode([
        'success' => true,
        'message' => 'File berhasil di-upload',
        'data' => [
            'id_mapping' => $id_mapping,
            'file_type' => $file_type,
            'filename' => $nama_baru,
            'upload_date' => date('Y-m-d H:i:s')
        ]
    ]);
    
} catch (Exception $e) {
    // Response error
    echo json_encode([
        'success' => false,
        'message' => 'File gagal di-upload, silahkan upload ulang',
        'error' => $e->getMessage()
    ]);
}
?>
