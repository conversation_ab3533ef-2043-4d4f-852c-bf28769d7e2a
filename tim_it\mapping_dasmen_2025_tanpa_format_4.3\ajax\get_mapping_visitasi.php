<?php
/**
 * AJAX handler untuk DataTable mapping visitasi SM
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

header('Content-Type: application/json');

try {
    // Parameter DataTable
    $draw = intval($_POST['draw'] ?? 1);
    $start = intval($_POST['start'] ?? 0);
    $length = intval($_POST['length'] ?? 10);
    $search_value = trim($_POST['search']['value'] ?? '');
    $provinsi_id = intval($_SESSION['provinsi_id']);

    // Base WHERE clause
    $base_where = "WHERE mapping_2025.tahun_akreditasi = mapping_2025_tahun.nama_tahun
                   AND sekolah.rumpun = 'dasmen'
                   AND mapping_2025_tahun.provinsi_id = '$provinsi_id'
                   AND mapping_2025.provinsi_id = '$provinsi_id'";

    // Tambahkan search jika ada
    $search_where = "";
    if (!empty($search_value)) {
        $search = $conn->real_escape_string($search_value);
        $search_where = " AND (
            sekolah.npsn LIKE '%$search%' OR
            sekolah.nama_sekolah LIKE '%$search%' OR
            jenjang.nm_jenjang LIKE '%$search%' OR
            kab_kota.nm_kota LIKE '%$search%' OR
            mapping_2025.tahun_akreditasi LIKE '%$search%' OR
            asesor_1.nia1 LIKE '%$search%' OR
            asesor_1.nm_asesor1 LIKE '%$search%' OR
            asesor_2.nia2 LIKE '%$search%' OR
            asesor_2.nm_asesor2 LIKE '%$search%'
        )";
    }

    // Count total records
    $count_sql = "SELECT COUNT(*) as total
                  FROM mapping_2025
                  LEFT JOIN sekolah ON mapping_2025.sekolah_id=sekolah.sekolah_id
                  LEFT JOIN jenjang ON sekolah.jenjang_id=jenjang.jenjang_id
                  LEFT JOIN kab_kota ON sekolah.kota_id=kab_kota.kota_id
                  LEFT JOIN asesor_1 ON mapping_2025.kd_asesor1=asesor_1.kd_asesor1
                  LEFT JOIN asesor_2 ON mapping_2025.kd_asesor2=asesor_2.kd_asesor2
                  LEFT JOIN mapping_2025_tahun ON mapping_2025.tahun_akreditasi=mapping_2025_tahun.nama_tahun
                  $base_where";

    $count_result = $conn->query($count_sql);
    $total_records = $count_result ? $count_result->fetch_assoc()['total'] : 0;

    // Count filtered records
    $filtered_sql = "SELECT COUNT(*) as total
                     FROM mapping_2025
                     LEFT JOIN sekolah ON mapping_2025.sekolah_id=sekolah.sekolah_id
                     LEFT JOIN jenjang ON sekolah.jenjang_id=jenjang.jenjang_id
                     LEFT JOIN kab_kota ON sekolah.kota_id=kab_kota.kota_id
                     LEFT JOIN asesor_1 ON mapping_2025.kd_asesor1=asesor_1.kd_asesor1
                     LEFT JOIN asesor_2 ON mapping_2025.kd_asesor2=asesor_2.kd_asesor2
                     LEFT JOIN mapping_2025_tahun ON mapping_2025.tahun_akreditasi=mapping_2025_tahun.nama_tahun
                     $base_where $search_where";

    $filtered_result = $conn->query($filtered_sql);
    $filtered_records = $filtered_result ? $filtered_result->fetch_assoc()['total'] : 0;

    // Main query
    $sql = "SELECT mapping_2025.id_mapping, mapping_2025.sekolah_id, sekolah.npsn, sekolah.nama_sekolah, jenjang.nm_jenjang,
                   kab_kota.nm_kota, sekolah.rumpun, mapping_2025_tahun.nama_tahun, mapping_2025.tahun_akreditasi, asesor_1.nia1,
                   asesor_1.nm_asesor1, asesor_2.nia2, asesor_2.nm_asesor2, mapping_2025.tahap
            FROM mapping_2025
            LEFT JOIN sekolah ON mapping_2025.sekolah_id=sekolah.sekolah_id
            LEFT JOIN jenjang ON sekolah.jenjang_id=jenjang.jenjang_id
            LEFT JOIN kab_kota ON sekolah.kota_id=kab_kota.kota_id
            LEFT JOIN asesor_1 ON mapping_2025.kd_asesor1=asesor_1.kd_asesor1
            LEFT JOIN asesor_2 ON mapping_2025.kd_asesor2=asesor_2.kd_asesor2
            LEFT JOIN mapping_2025_tahun ON mapping_2025.tahun_akreditasi=mapping_2025_tahun.nama_tahun
            $base_where $search_where
            ORDER BY sekolah.nama_sekolah ASC
            LIMIT $start, $length";

    $result = $conn->query($sql);

    $data = [];
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $data[] = [
                'id_mapping' => $row['id_mapping'],
                'npsn' => $row['npsn'] ?? '-',
                'nama_sekolah' => $row['nama_sekolah'] ?? '-',
                'nm_jenjang' => $row['nm_jenjang'] ?? '-',
                'rumpun' => strtoupper($row['rumpun'] ?? '-'),
                'nm_kota' => $row['nm_kota'] ?? '-',
                'nia1' => $row['nia1'] ?? '-',
                'nm_asesor1' => $row['nm_asesor1'] ?? '-',
                'nia2' => $row['nia2'] ?? '-',
                'nm_asesor2' => $row['nm_asesor2'] ?? '-',
                'tahun_akreditasi' => $row['tahun_akreditasi'] ?? '-',
                'tahap' => $row['tahap'] ?? '-'
            ];
        }
    }

    // Response
    echo json_encode([
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $filtered_records,
        'data' => $data
    ]);

} catch (Exception $e) {
    echo json_encode([
        'draw' => intval($_POST['draw'] ?? 1),
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
