<?php
/**
 * AJAX handler untuk autocomplete NIA asesor
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Ambil parameter pencarian
    $search = isset($_POST['search']) ? trim($_POST['search']) : '';
    $type = isset($_POST['type']) ? $_POST['type'] : 'asesor1'; // asesor1 atau asesor2
    
    if (empty($search)) {
        echo json_encode(['success' => true, 'data' => []]);
        exit;
    }
    
    $data = [];
    
    if ($type === 'asesor1') {
        // Query untuk asesor_1
        $sql = "SELECT kd_asesor1, nia1, nm_asesor1, unit_kerja, 
                       k.nm_kota, jenjang_id, rumpun, grade
                FROM asesor_1 a1
                LEFT JOIN kab_kota k ON a1.kota_id1 = k.kota_id
                WHERE a1.soft_delete = '1'
                AND (a1.nia1 LIKE ? OR a1.nm_asesor1 LIKE ?)
                ORDER BY a1.nia1 ASC
                LIMIT 10";
        
        $search_param = "%{$search}%";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $search_param, $search_param);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $data[] = [
                'kd_asesor' => $row['kd_asesor1'],
                'nia' => $row['nia1'],
                'nama_asesor' => $row['nm_asesor1'],
                'unit_kerja' => $row['unit_kerja'],
                'kota' => $row['nm_kota'],
                'jenjang_id' => $row['jenjang_id'],
                'rumpun' => $row['rumpun'],
                'grade' => $row['grade'],
                'label' => $row['nia1'] . ' - ' . $row['nm_asesor1'],
                'value' => $row['nia1']
            ];
        }
        
    } else if ($type === 'asesor2') {
        // Query untuk asesor_2
        $sql = "SELECT kd_asesor2, nia2, nm_asesor2, unit_kerja, 
                       k.nm_kota, jenjang_id, rumpun, grade
                FROM asesor_2 a2
                LEFT JOIN kab_kota k ON a2.kota_id2 = k.kota_id
                WHERE a2.soft_delete = '1'
                AND (a2.nia2 LIKE ? OR a2.nm_asesor2 LIKE ?)
                ORDER BY a2.nia2 ASC
                LIMIT 10";
        
        $search_param = "%{$search}%";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $search_param, $search_param);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $data[] = [
                'kd_asesor' => $row['kd_asesor2'],
                'nia' => $row['nia2'],
                'nama_asesor' => $row['nm_asesor2'],
                'unit_kerja' => $row['unit_kerja'],
                'kota' => $row['nm_kota'],
                'jenjang_id' => $row['jenjang_id'],
                'rumpun' => $row['rumpun'],
                'grade' => $row['grade'],
                'label' => $row['nia2'] . ' - ' . $row['nm_asesor2'],
                'value' => $row['nia2']
            ];
        }
    }
    
    echo json_encode([
        'success' => true,
        'data' => $data
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
        'data' => []
    ]);
    error_log("Error in autocomplete_nia.php: " . $e->getMessage());
}
?>
