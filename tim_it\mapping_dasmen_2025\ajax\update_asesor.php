<?php
/**
 * AJAX handler untuk update asesor
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Ambil dan validasi input
    $id_mapping = intval($_POST['id_mapping'] ?? 0);
    $nia_asesor1 = trim($_POST['nia_asesor1'] ?? '');
    $nia_asesor2 = trim($_POST['nia_asesor2'] ?? '');
    $provinsi_id = intval($_SESSION['provinsi_id']);

    // Validasi required fields
    if ($id_mapping <= 0) {
        echo json_encode(['success' => false, 'message' => 'ID mapping tidak valid']);
        exit;
    }

    if (empty($nia_asesor1)) {
        echo json_encode(['success' => false, 'message' => 'NIA Asesor 1 wajib diisi']);
        exit;
    }

    if (empty($nia_asesor2)) {
        echo json_encode(['success' => false, 'message' => 'NIA Asesor 2 wajib diisi']);
        exit;
    }

    if ($nia_asesor1 === $nia_asesor2) {
        echo json_encode(['success' => false, 'message' => 'NIA Asesor 1 dan Asesor 2 harus berbeda']);
        exit;
    }

    // Escape input untuk keamanan
    $nia_asesor1 = $conn->real_escape_string($nia_asesor1);
    $nia_asesor2 = $conn->real_escape_string($nia_asesor2);

    // Cek apakah mapping exists dan milik provinsi yang benar
    $check_mapping = "SELECT id_mapping FROM mapping_2025 
                     WHERE id_mapping = $id_mapping AND provinsi_id = $provinsi_id";
    $result_mapping = $conn->query($check_mapping);

    if (!$result_mapping || $result_mapping->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Data mapping tidak ditemukan']);
        exit;
    }

    // Lookup asesor 1 berdasarkan NIA untuk mendapatkan kd_asesor1
    $asesor1_query = "SELECT a1.kd_asesor1, a1.nia1, a1.nm_asesor1, a1.no_hp, k.nm_kota
                     FROM asesor_1 a1
                     LEFT JOIN kab_kota k ON a1.kota_id1 = k.kota_id
                     WHERE a1.nia1 = '$nia_asesor1' AND a1.provinsi_id = $provinsi_id AND a1.status_keaktifan_id = '1'";
    $result_asesor1 = $conn->query($asesor1_query);

    if (!$result_asesor1 || $result_asesor1->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Asesor 1 dengan NIA ' . $nia_asesor1 . ' tidak ditemukan atau tidak aktif']);
        exit;
    }

    // Lookup asesor 2 berdasarkan NIA untuk mendapatkan kd_asesor2
    $asesor2_query = "SELECT a2.kd_asesor2, a2.nia2, a2.nm_asesor2, a2.no_hp, k.nm_kota
                     FROM asesor_2 a2
                     LEFT JOIN kab_kota k ON a2.kota_id2 = k.kota_id
                     WHERE a2.nia2 = '$nia_asesor2' AND a2.provinsi_id = $provinsi_id AND a2.status_keaktifan_id = '1'";
    $result_asesor2 = $conn->query($asesor2_query);

    if (!$result_asesor2 || $result_asesor2->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Asesor 2 dengan NIA ' . $nia_asesor2 . ' tidak ditemukan atau tidak aktif']);
        exit;
    }

    // Ambil data asesor
    $data_asesor1 = $result_asesor1->fetch_assoc();
    $data_asesor2 = $result_asesor2->fetch_assoc();

    // Update mapping dengan kd_asesor (bukan NIA)
    $update_query = "UPDATE mapping_2025 SET
                        kd_asesor1 = '" . $conn->real_escape_string($data_asesor1['kd_asesor1']) . "',
                        kd_asesor2 = '" . $conn->real_escape_string($data_asesor2['kd_asesor2']) . "'
                     WHERE id_mapping = $id_mapping AND provinsi_id = $provinsi_id";

    if ($conn->query($update_query)) {
        // Log activity
        error_log("Asesor Updated - ID: $id_mapping, Asesor1: $nia_asesor1, Asesor2: $nia_asesor2, User: " . ($_SESSION['nm_user'] ?? 'Unknown'));

        echo json_encode([
            'success' => true,
            'message' => 'Data asesor berhasil diupdate',
            'data' => [
                'asesor1' => [
                    'nia' => $data_asesor1['nia1'] ?? '-',
                    'nama' => $data_asesor1['nm_asesor1'] ?? '-',
                    'hp' => $data_asesor1['no_hp'] ?? '-',
                    'kota' => $data_asesor1['nm_kota'] ?? '-'
                ],
                'asesor2' => [
                    'nia' => $data_asesor2['nia2'] ?? '-',
                    'nama' => $data_asesor2['nm_asesor2'] ?? '-',
                    'hp' => $data_asesor2['no_hp'] ?? '-',
                    'kota' => $data_asesor2['nm_kota'] ?? '-'
                ]
            ]
        ]);
    } else {
        error_log("Update Asesor Failed: " . $conn->error);
        echo json_encode([
            'success' => false,
            'message' => 'Gagal update data asesor: ' . $conn->error
        ]);
    }

} catch (Exception $e) {
    error_log("Update Asesor Error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
