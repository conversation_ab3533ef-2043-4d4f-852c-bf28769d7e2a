# Mapping Asesor Visitasi SM - IASP 2025

## 📋 Deskripsi
Modul untuk mengelola mapping asesor visitasi sekolah menengah (SM) dalam program akreditasi IASP 2025.

## 🗂️ Struktur File
```
tim_it/mapping_dasmen_2025/
├── mapping.php              # Halaman utama
├── README.md               # Dokumentasi
├── js/
│   └── mapping_visitasi.js  # JavaScript DataTable + Tahun Akreditasi
└── ajax/
    ├── get_mapping_visitasi.php     # DataTable server-side
    ├── validate_npsn.php           # Validasi NPSN
    ├── validate_asesor.php         # Validasi asesor
    ├── simpan_mapping.php          # Simpan data mapping
    ├── tahun_akreditasi.php        # Kelola tahun akreditasi
    ├── detail_mapping.php          # Detail mapping visitasi
    ├── update_tanggal_visitasi.php # Update tanggal visitasi
    ├── update_asesor.php           # Update asesor
    └── delete_mapping.php          # Delete mapping visitasi
├── mapping_2025_st_visitasi.php    # Generate PDF Surat Tugas Visitasi
├── mapping_2025_st_pra_akreditasi.php # Generate PDF Surat Tugas Pra-Akreditasi
├── export_mapping_data.php        # Export data mapping ke CSV/Excel
```

## 🎯 Fitur Utama
- **DataTable Server-side** - Menampilkan data mapping dengan pagination
- **Real-time Search** - Pencarian data di semua kolom
- **Input Data Mapping** - Form untuk menambah data mapping baru
- **Validasi NPSN** - Validasi sekolah berdasarkan NPSN
- **Validasi Asesor** - Validasi asesor 1 dan asesor 2
- **Flexible Input** - Field optional boleh kosong (NULL)
- **Tahun Akreditasi** - Edit tahun akreditasi dengan auto-refresh DataTable
- **Detail Mapping** - Modal detail dengan 5 kolom informasi lengkap
- **Edit Tanggal Visitasi** - Nested modal untuk edit tanggal mulai & akhir visitasi
- **Edit Asesor** - Nested modal untuk edit NIA asesor 1 & 2
- **Delete Mapping** - Hapus data mapping dengan konfirmasi yang aman
- **Export Data** - Download data mapping dalam format CSV/Excel
- **Generate PDF Surat Tugas** - Cetak surat tugas visitasi & pra-akreditasi dalam format PDF

## 📊 Query Database
Menggunakan query dengan JOIN ke tabel:
- `mapping_2025` (data utama)
- `sekolah` (data sekolah)
- `jenjang` (jenjang pendidikan)
- `kab_kota` (kabupaten/kota)
- `asesor_1` (asesor pertama)
- `asesor_2` (asesor kedua)
- `mapping_2025_tahun` (tahun akreditasi)

## 🔍 Filter Data
- **Provinsi**: Berdasarkan session login user
- **Rumpun**: 'dasmen' OR 'kesetaraan'
- **Tahun**: Sesuai dengan data di `mapping_2025_tahun`

## 💻 Penggunaan
1. Akses halaman `mapping.php`
2. Klik tombol "Input Data Mapping" untuk menambah data
3. Klik tombol "Tahun Akreditasi" untuk mengatur filter tahun
4. Gunakan fitur search untuk mencari data
5. Data akan ditampilkan dalam format DataTable

## 📅 Fitur Tahun Akreditasi
### **Fungsi:**
- **Edit tahun akreditasi** untuk filter data
- **Auto-refresh DataTable** tanpa reload halaman
- **Filter berdasarkan** `mapping_2025.tahun_akreditasi = mapping_2025_tahun.nama_tahun`

### **Cara Penggunaan:**
1. Klik tombol "Tahun Akreditasi"
2. Modal kecil akan muncul dengan form edit
3. Masukkan tahun (2020-2030)
4. Klik "Simpan"
5. DataTable otomatis refresh dengan filter tahun baru

## 📋 Fitur Detail Mapping Visitasi SM
### **Fungsi:**
- **Modal detail** dengan 5 kolom informasi lengkap
- **Data sekolah** - NPSN, nama, jenjang, kepala sekolah
- **Data asesor** - NIA, nama, kontak kedua asesor
- **Status dokumen** - 15 file upload dengan status badge
- **Pelaksanaan kegiatan** - Tanggal dan nomor surat tugas
- **Aksi** - Tombol edit, hapus, dan download

### **Layout Modal:**
1. **Kolom 1**: Data Sekolah (NPSN, nama, kepsek, kontak)
2. **Kolom 2**: Data Asesor (asesor 1 & 2 dengan detail)
3. **Kolom 3**: Dokumen Unggahan (15 file dengan status)
4. **Kolom 4**: Pelaksanaan Kegiatan (tanggal & surat tugas)
5. **Kolom 5**: Aksi (edit, hapus, download ST)

### **Status Dokumen:**
- **Hijau "Sudah Upload"** - File sudah ada (clickable, buka di tab baru)
- **Merah "Belum Upload"** - File belum ada (tidak clickable)

### **Cara Penggunaan:**
1. Klik tombol detail (mata) di kolom "Aksi" DataTable
2. Modal extra large akan muncul
3. Lihat informasi lengkap dalam 5 kolom
4. **Klik badge "Sudah Upload"** untuk membuka file PDF
5. **Klik tombol "ST Visitasi"** untuk download surat tugas visitasi
6. **Klik tombol "ST Pra-Visitasi"** untuk download surat tugas pra-visitasi
7. **Klik tombol "Hapus"** untuk menghapus data mapping (dengan konfirmasi)
8. Gunakan tombol aksi sesuai kebutuhan

### **File Management:**
- **15 jenis dokumen** dengan direktori terpisah
- **Path**: `../../../simak/files/[direktori]/[nama_file]`
- **Format**: PDF yang dibuka di tab baru
- **Hover effect**: Badge akan membesar saat di-hover

## 📅 Fitur Edit Tanggal Visitasi
### **Fungsi:**
- **Nested modal** - Modal edit di atas modal detail
- **Edit 2 field tanggal**: Mulai & akhir visitasi
- **Real-time update** - Data berubah tanpa refresh browser
- **Validasi tanggal** - Akhir >= mulai visitasi

### **Cara Penggunaan:**
1. Buka modal detail mapping visitasi
2. Klik tombol "Edit Tanggal" di kolom "Aksi"
3. Modal kecil edit tanggal muncul di atas modal detail
4. Edit tanggal mulai dan akhir visitasi
5. Klik "Update Perubahan"
6. Data di kolom "Pelaksanaan Kegiatan" update otomatis
7. **DataTable utama refresh** otomatis tanpa reload halaman
8. Modal edit tutup, modal detail tetap terbuka

### **Validasi:**
- **Tanggal wajib diisi** - Kedua field harus terisi
- **Format tanggal valid** - YYYY-MM-DD
- **Logika tanggal** - Akhir >= mulai visitasi

## 👥 Fitur Edit Asesor
### **Fungsi:**
- **Nested modal** - Modal edit di atas modal detail
- **Edit 2 field NIA**: Asesor 1 & Asesor 2
- **Real-time update** - Data asesor berubah tanpa refresh browser
- **Validasi NIA** - Cek keberadaan di tabel asesor

### **Cara Penggunaan:**
1. Buka modal detail mapping visitasi
2. Klik tombol "Edit Asesor" di kolom "Aksi"
3. Modal kecil edit asesor muncul di atas modal detail
4. Edit NIA asesor 1 dan asesor 2
5. Klik "Update Perubahan"
6. Data di kolom "Data Asesor" update otomatis (NIA, nama, HP, kota)
7. **DataTable utama refresh** otomatis tanpa reload halaman
8. Modal edit tutup, modal detail tetap terbuka

### **Validasi:**
- **NIA wajib diisi** - Kedua field harus terisi
- **NIA harus exist** - Cek di tabel asesor_1 & asesor_2
- **Status aktif** - Asesor harus memiliki status_keaktifan_id = '1'
- **NIA harus berbeda** - Asesor 1 ≠ Asesor 2
- **Lookup kd_asesor** - NIA di-lookup untuk mendapatkan kd_asesor1 & kd_asesor2

### **Database Logic:**
- **Input**: NIA asesor (user-friendly)
- **Lookup**: NIA → kd_asesor1 & kd_asesor2 (database keys)
- **Update**: mapping_2025.kd_asesor1 & kd_asesor2 (bukan NIA)
- **Response**: Data lengkap asesor (NIA, nama, HP, kota)

### **Real-time Updates:**
- **Modal detail**: Data asesor update otomatis
- **DataTable utama**: Refresh otomatis tanpa reload halaman
- **Seamless UX**: Perubahan terlihat di semua tempat secara real-time

## 📥 Fitur Download Surat Tugas
### **Tombol ST Visitasi:**
- **Fungsi**: Download PDF surat tugas visitasi
- **Aksi**: Buka `mapping_2025_st_visitasi.php` di tab baru
- **Parameter**: `kode=[id_mapping]` - ID data mapping sebagai nilai parameter kode
- **Output**: PDF surat tugas siap cetak

### **Tombol ST Pra-Visitasi:**
- **Fungsi**: Download PDF surat tugas pra-visitasi
- **Aksi**: Buka `mapping_2025_st_pra_akreditasi.php` di tab baru
- **Parameter**: `kode=[id_mapping]` - ID data mapping sebagai nilai parameter kode
- **Output**: PDF surat tugas pra-akreditasi siap cetak

## 📄 Fitur Generate PDF Surat Tugas Visitasi
### **Fungsi:**
- **Generate PDF** - Cetak surat tugas visitasi dalam format PDF
- **Template resmi** - Menggunakan format BAN-S/M yang standar
- **Data dinamis** - Informasi sekolah, asesor, dan tanggal otomatis
- **Logo dan TTD** - Menggunakan logo dan tanda tangan digital

### **Format Surat Tugas:**
- **Header**: Logo BAN-S/M + Nama Provinsi
- **Nomor Surat**: Dari field `no_surat_tugas_visitasi`
- **Asesor**: Nama asesor 1 (Ketua) & asesor 2 (Anggota)
- **Sekolah**: Nama, alamat, jenjang sekolah
- **Tugas**: 6 poin tugas asesor visitasi
- **Tanda Tangan**: TTD digital ketua BAN-S/M

### **Cara Penggunaan:**
- Akses: `mapping_2025_st_visitasi.php?kode=[ID_MAPPING]`
- Parameter: `kode` - ID mapping visitasi sebagai nilai parameter
- Output: PDF yang siap dicetak atau disimpan

## 📄 Fitur Generate PDF Surat Tugas Pra-Akreditasi
### **Fungsi:**
- **Generate PDF** - Cetak surat tugas pra-akreditasi dalam format PDF
- **Template resmi** - Menggunakan format BAN-S/M yang standar
- **Data dinamis** - Informasi sekolah, asesor, dan tanggal otomatis
- **Logo dan TTD** - Menggunakan logo dan tanda tangan digital

### **Format Surat Tugas Pra-Akreditasi:**
- **Header**: Logo BAN-S/M + Nama Provinsi
- **Nomor Surat**: Dari field `no_surat_tugas_pra_visitasi`
- **Asesor**: Nama asesor 1 (Ketua) & asesor 2 (Anggota)
- **Sekolah**: Nama, alamat, jenjang sekolah
- **Tugas**: 5 poin tugas asesor pra-akreditasi
- **Tanda Tangan**: TTD digital ketua BAN-S/M

### **Cara Penggunaan:**
- Akses: `mapping_2025_st_pra_akreditasi.php?kode=[ID_MAPPING]`
- Parameter: `kode` - ID mapping untuk pra-akreditasi
- Output: PDF yang siap dicetak atau disimpan

## 🗑️ Fitur Delete Mapping
### **Fungsi:**
- **Delete data** - Hapus record mapping visitasi secara permanen
- **Konfirmasi aman** - Dialog konfirmasi dengan SweetAlert2
- **Security check** - Validasi provinsi dan ownership data
- **Activity logging** - Log penghapusan untuk audit trail

### **Cara Penggunaan:**
1. Buka modal detail mapping visitasi
2. Klik tombol "Hapus" (merah) di kolom "Aksi"
3. Dialog konfirmasi muncul dengan detail sekolah
4. Klik "Ya, Hapus!" untuk konfirmasi atau "Batal" untuk membatalkan
5. Data terhapus permanen dari database
6. Modal detail tutup otomatis
7. DataTable refresh untuk menghilangkan baris yang dihapus

### **Keamanan:**
- **Konfirmasi ganda** - Dialog konfirmasi dengan detail data
- **Provinsi check** - Hanya bisa hapus data dari provinsi sendiri
- **Permanent delete** - Data terhapus permanen (tidak soft delete)
- **Activity log** - Pencatatan aktivitas penghapusan
- **Error handling** - Pesan error yang jelas jika gagal

### **Validasi:**
- **ID mapping valid** - Cek keberadaan data
- **Ownership check** - Data harus milik provinsi user
- **Database integrity** - Transaksi yang aman

## 📊 Fitur Export Data Mapping
### **Fungsi:**
- **Export Excel XLSX** - Download semua data mapping dalam format Excel (.xlsx)
- **Status file upload** - Menampilkan "Sudah Unggah" atau "Belum Unggah" untuk setiap file
- **Conditional formatting** - Warna hijau untuk "Sudah Unggah", merah untuk "Belum Unggah"
- **Filter tahun** - Data difilter berdasarkan tahun akreditasi yang aktif
- **Data lengkap** - Semua informasi sekolah, asesor, tanggal, dan status file

### **Kolom Data yang Diekspor:**
1. **Data Sekolah**: NPSN, Nama, Jenjang, Kab/Kota, Rumpun
2. **Tahun Akreditasi**: Sesuai filter yang aktif
3. **Data Asesor**: NIA, Nama, HP, Kota (Asesor 1 & 2)
4. **Tanggal Kegiatan**: Pra-visitasi, ST Pra-visitasi, Mulai/Akhir Visitasi, ST Visitasi
5. **Status File Upload** (Sudah Unggah/Belum Unggah):
   - File Format 3.1 Penilaian Pra-Visitasi 1 & 2
   - File Format 3.2 LK Penggalian Data Pra-Visitasi 1 & 2
   - File Format 4.1 Surat Tugas Visitasi
   - File Format 4.2 Pakta Integritas 1 & 2
   - File Format 4.3 Rekap Penggalian Data Penilaian 1 & 2
   - File Format 4.4 Berita Acara Visitasi
   - File Format 4.5 Laporan Individu 1 & 2
   - File Format 4.5 Laporan Kelompok
   - File Format 4.5 Catatan Dan Saran
   - File Foto Visitasi

### **Cara Penggunaan:**
1. Klik tombol "Export Excel" di halaman utama
2. Dialog konfirmasi muncul dengan detail data yang akan diekspor
3. Klik "Ya, Download!" untuk memulai export
4. File Excel (.xlsx) akan diunduh otomatis dengan formatting
5. Buka file dengan Microsoft Excel atau aplikasi spreadsheet lainnya

### **Format Output:**
- **File**: Excel XLSX dengan formatting
- **Nama**: Export_Mapping_Visitasi_SM_YYYYMMDD.xlsx
- **Library**: XLSX.js (SheetJS) untuk client-side generation
- **Conditional Formatting**:
  - **Header**: Background abu-abu, font bold, center alignment
  - **Sudah Unggah**: Background hijau muda, font hijau tua, bold
  - **Belum Unggah**: Background merah muda, font merah tua, bold

### **Filter Data:**
- **Rumpun**: Hanya data dengan rumpun = 'dasmen'
- **Tahun**: Sesuai dengan mapping_2025_tahun yang aktif
- **Provinsi**: Sesuai dengan provinsi user yang login
- **Urutan**: Diurutkan berdasarkan nama sekolah (A-Z)

## 📝 Validasi Form Input
### **Required Fields:**
- `sekolah_id` - ID sekolah (dari validasi NPSN)
- `kd_asesor1` - Kode asesor pertama
- `kd_asesor2` - Kode asesor kedua
- `tahun_akreditasi` - Tahun akreditasi (format YYYY)
- `tahap` - Tahap visitasi (1-10)

### **Optional Fields (boleh kosong):**
- `tgl_pra_visitasi` - Tanggal pra visitasi
- `tgl_surat_tugas_pra_visitasi` - Tanggal surat tugas pra visitasi
- `no_surat_tugas_pra_visitasi` - Nomor surat tugas pra visitasi
- `tgl_mulai_visitasi` - Tanggal mulai visitasi
- `tgl_akhir_visitasi` - Tanggal akhir visitasi
- `tgl_surat_tugas_visitasi` - Tanggal surat tugas visitasi
- `no_surat_tugas_visitasi` - Nomor surat tugas visitasi

### **Strategi Penyimpanan:**
1. **INSERT** hanya field required terlebih dahulu
2. **UPDATE** field optional yang diisi (jika ada)
3. Field kosong tidak disimpan ke database

## 🛠️ Teknologi
- **Backend**: PHP dengan MySQLi
- **Frontend**: Bootstrap 5 + DataTables + jQuery
- **Database**: MySQL/MariaDB
