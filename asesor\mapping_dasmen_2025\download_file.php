<?php
/**
 * File download handler untuk LK Penilaian 1 & 2
 * Direct download tanpa preview
 */

require_once '../../koneksi.php';
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');

// Get parameters
$filename = $_GET['file'] ?? '';

if (empty($filename)) {
    http_response_code(400);
    die('File parameter required');
}

// Security: Validate filename (no path traversal)
if (strpos($filename, '..') !== false || strpos($filename, '/') !== false || strpos($filename, '\\') !== false) {
    http_response_code(403);
    die('Invalid filename');
}

// Sanitize filename
$filename = basename($filename);

if (empty($filename)) {
    http_response_code(400);
    die('Invalid filename');
}

// Get session variables
$kd_user = $_SESSION['kd_user'] ?? '';
$provinsi_id = $_SESSION['provinsi_id'] ?? '';

// Verify file belongs to current asesor (hanya untuk LK Penilaian 1 & 2)
$check_query = "SELECT m.file_lk_penilaian_1, m.file_lk_penilaian_2
                FROM mapping_2025 m
                WHERE (m.kd_asesor1 = '$kd_user' OR m.kd_asesor2 = '$kd_user')
                  AND m.provinsi_id = '$provinsi_id'
                  AND (m.file_lk_penilaian_1 = '$filename' OR m.file_lk_penilaian_2 = '$filename')";

$check_result = $conn->query($check_query);

if (!$check_result || $check_result->num_rows === 0) {
    http_response_code(403);
    die('Access denied: File not found or you do not have permission to download this file');
}

// Determine file path based on filename
$file_path_1 = "../../../simak/files/upload_file_lk_penilaian_1/" . $filename;
$file_path_2 = "../../../simak/files/upload_file_lk_penilaian_2/" . $filename;

$file_path = '';
if (file_exists($file_path_1)) {
    $file_path = $file_path_1;
} elseif (file_exists($file_path_2)) {
    $file_path = $file_path_2;
}

// Check if file exists
if (empty($file_path) || !file_exists($file_path)) {
    http_response_code(404);
    die('File not found on server');
}

// Get file info
$file_size = filesize($file_path);
$file_extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

// Set appropriate content type based on file extension
$content_types = [
    'pdf' => 'application/pdf',
    'doc' => 'application/msword',
    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls' => 'application/vnd.ms-excel',
    'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt' => 'application/vnd.ms-powerpoint',
    'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'txt' => 'text/plain',
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'zip' => 'application/zip',
    'rar' => 'application/x-rar-compressed'
];

$content_type = $content_types[$file_extension] ?? 'application/octet-stream';

// Set headers for download
header('Content-Type: ' . $content_type);
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Content-Length: ' . $file_size);
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Clear output buffer
if (ob_get_level()) {
    ob_end_clean();
}

// Read and output file
readfile($file_path);
exit();
?>
