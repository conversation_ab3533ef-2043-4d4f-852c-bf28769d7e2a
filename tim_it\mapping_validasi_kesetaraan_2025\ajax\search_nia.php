<?php
/**
 * AJAX handler untuk search NIA asesor (autocomplete)
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method GET
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi parameter
    if (!isset($_GET['q']) || empty($_GET['q'])) {
        throw new Exception('Query pencarian tidak valid');
    }
    
    if (!isset($_GET['type']) || empty($_GET['type'])) {
        throw new Exception('Tipe asesor tidak valid');
    }
    
    $query = trim($_GET['q']);
    $type = $_GET['type'];
    
    // Validasi minimal 2 karakter
    if (strlen($query) < 2) {
        echo json_encode([
            'success' => true,
            'data' => []
        ]);
        exit;
    }
    
    $data = [];
    
    // Search berdasarkan tipe asesor
    if ($type === 'asesor1') {
        // Search di tabel asesor_1
        $sql = "SELECT nia1 as nia, nm_asesor1 as nama 
                FROM asesor_1 
                WHERE (nia1 LIKE ? OR nm_asesor1 LIKE ?)
                ORDER BY nia1 ASC 
                LIMIT 10";
        
        $search_term = "%{$query}%";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $search_term, $search_term);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $data[] = [
                'nia' => $row['nia'],
                'nama' => $row['nama']
            ];
        }
        
    } elseif ($type === 'asesor2') {
        // Search di tabel asesor_2
        $sql = "SELECT nia2 as nia, nm_asesor2 as nama 
                FROM asesor_2 
                WHERE (nia2 LIKE ? OR nm_asesor2 LIKE ?)
                ORDER BY nia2 ASC 
                LIMIT 10";
        
        $search_term = "%{$query}%";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $search_term, $search_term);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $data[] = [
                'nia' => $row['nia'],
                'nama' => $row['nama']
            ];
        }
        
    } else {
        throw new Exception('Tipe asesor tidak valid');
    }
    
    echo json_encode([
        'success' => true,
        'data' => $data
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
        'data' => []
    ]);
    error_log("Error in search_nia.php: " . $e->getMessage());
}
?>
