sekarang kita menuju file "sidebar.php" yang terdapat di direktori "asesor/", file "sidebar.php" tersebut terdapat menu dengan nama "Ganti Password", saya ingin ketika menu tersebut di-klik akan tampil modal yang berisi form untuk edit password berdasarkan field user.kd_user session login. form edit password tersebut akan update/edit data user.password dan user.pwd yang ada di database.

bisnis prosesnya seperti ini, user klik menu "Ganti Password", kemudian muncul modal dengan nama "Ganti Password Baru" yang berisi form input dengan field user.password:
<input type="password" id="password" name="password" value="">
ketika tombol "Simpan Perubahan" data akan update field user.password dengan function MD5 dan update ke field user.pwd juga, datanya berasal dari form input "password" dengan text biasa tanpa MD5

sehingga ketika asesor menyimpan perubahan, kami sebagai admin bisa tau password apa yang dipakai user yang datanya ada di tabel user

silahkan tulis kode sumber untuk modul "Ganti Password" ini di direktori "asesor/ganti_password/"


berikut field dari tabel user:
kd_user Index varchar(25)
password varchar(50)
pwd varchar(50)


sampai disini apakah anda mengerti dengan apa yang saya maksudkan?
jika ada pertanyaan yang ingin ditanyakan dipersilahkan

1. Validasi Password: tidak perlu validasi minimum length atau complexity untuk password baru
2. Current Password: tidak perlu input password lama untuk verifikasi sebelum ganti password baru
3. Success Feedback: Setelah berhasil update, perlu notifikasi success berbentuk modal juga
4. Session Management: tidak perlu logout otomatis setelah ganti password dan user tetap login
5. File Structure: di dalam direktori "ganti_password" terdapat direktori "js" dan direktori "ajax"



silahkan anda membaca dan menganalisis file "asesor/sidebar.php" silahkan anda lakukan implementasi agar menu "Ganti Password" ketika di-klik membuka modal "Ganti Password Baru"