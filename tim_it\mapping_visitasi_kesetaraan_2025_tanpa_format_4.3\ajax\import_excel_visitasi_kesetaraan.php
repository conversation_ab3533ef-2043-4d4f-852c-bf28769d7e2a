<?php
/**
 * AJAX handler untuk import Excel mapping visitasi kesetaraan
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

header('Content-Type: application/json');

try {
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Check if data was sent
    if (!isset($_POST['excel_data']) || empty($_POST['excel_data'])) {
        throw new Exception('Data Excel tidak ditemukan');
    }
    
    // Parse JSON data
    $rows = json_decode($_POST['excel_data'], true);
    
    if (!is_array($rows)) {
        throw new Exception('Format data tidak valid');
    }
    
    if (empty($rows)) {
        throw new Exception('Tidak ada data untuk diimport');
    }
    
    // Check max rows (100)
    if (count($rows) > 100) {
        throw new Exception('Maksimal 100 baris data. Data Anda memiliki ' . count($rows) . ' baris');
    }
    
    // Get session data
    $provinsi_id = intval($_SESSION['provinsi_id']);
    
    // Process and Insert
    $successCount = 0;
    $errorCount = 0;
    $errors = [];
    
    // Begin transaction
    $conn->autocommit(false);
    
    foreach ($rows as $index => $row) {
        $rowNumber = $index + 2; // +2 because we removed header and array is 0-based
        
        try {
            // Extract data from row (10 columns)
            $npsn = trim($row[0] ?? '');
            $nia_asesor1 = trim($row[1] ?? '');
            $nia_asesor2 = trim($row[2] ?? '');
            $tgl_pra_visitasi = trim($row[3] ?? '');
            $tgl_surat_tugas_pra_visitasi = trim($row[4] ?? '');
            $no_surat_tugas_pra_visitasi = trim($row[5] ?? '');
            $tgl_surat_tugas_visitasi = trim($row[6] ?? '');
            $no_surat_tugas_visitasi = trim($row[7] ?? '');
            $tahap = trim($row[8] ?? '');
            $tahun_akreditasi = trim($row[9] ?? '');
            
            // Validate required fields
            if (empty($npsn) || empty($nia_asesor1) || empty($nia_asesor2) || 
                empty($tgl_pra_visitasi) || empty($tgl_surat_tugas_pra_visitasi) || 
                empty($no_surat_tugas_pra_visitasi) || empty($tgl_surat_tugas_visitasi) || 
                empty($no_surat_tugas_visitasi) || empty($tahap) || empty($tahun_akreditasi)) {
                throw new Exception("Baris $rowNumber: Data tidak lengkap");
            }
            
            // Validate tahun format
            if (!preg_match('/^\d{4}$/', $tahun_akreditasi)) {
                throw new Exception("Baris $rowNumber: Format tahun tidak valid");
            }
            
            // Validate tahap (numeric)
            if (!is_numeric($tahap) || $tahap < 1) {
                throw new Exception("Baris $rowNumber: Tahap harus angka positif");
            }
            
            // Validate asesor1 dan asesor2 tidak sama
            if ($nia_asesor1 === $nia_asesor2) {
                throw new Exception("Baris $rowNumber: Asesor 1 dan Asesor 2 tidak boleh sama");
            }
            
            // Validate date formats
            $dateFields = [
                'tgl_pra_visitasi' => $tgl_pra_visitasi,
                'tgl_surat_tugas_pra_visitasi' => $tgl_surat_tugas_pra_visitasi,
                'tgl_surat_tugas_visitasi' => $tgl_surat_tugas_visitasi
            ];
            
            foreach ($dateFields as $fieldName => $dateValue) {
                if (!empty($dateValue)) {
                    $date = DateTime::createFromFormat('Y-m-d', $dateValue);
                    if (!$date || $date->format('Y-m-d') !== $dateValue) {
                        throw new Exception("Baris $rowNumber: Format tanggal $fieldName tidak valid (gunakan yyyy-mm-dd)");
                    }
                }
            }
            
            // Lookup NPSN to get sekolah_id
            $sekolah_query = "SELECT sekolah_id FROM sekolah 
                             WHERE npsn = ? AND provinsi_id = ? AND rumpun = 'kesetaraan' 
                             AND soft_delete = '1' AND status_keaktifan_id = '1'";
            $sekolah_stmt = $conn->prepare($sekolah_query);
            $sekolah_stmt->bind_param("si", $npsn, $provinsi_id);
            $sekolah_stmt->execute();
            $sekolah_result = $sekolah_stmt->get_result();
            
            if ($sekolah_result->num_rows === 0) {
                throw new Exception("Baris $rowNumber: NPSN $npsn tidak ditemukan");
            }
            
            $sekolah_data = $sekolah_result->fetch_assoc();
            $sekolah_id = $sekolah_data['sekolah_id'];
            
            // Lookup NIA Asesor 1 to get kd_asesor1
            $asesor1_query = "SELECT kd_asesor FROM asesor 
                             WHERE nia = ? AND provinsi_id = ? 
                             AND soft_delete = '1' AND status_keaktifan_id = '1'";
            $asesor1_stmt = $conn->prepare($asesor1_query);
            $asesor1_stmt->bind_param("si", $nia_asesor1, $provinsi_id);
            $asesor1_stmt->execute();
            $asesor1_result = $asesor1_stmt->get_result();
            
            if ($asesor1_result->num_rows === 0) {
                throw new Exception("Baris $rowNumber: NIA Asesor 1 $nia_asesor1 tidak ditemukan");
            }
            
            $asesor1_data = $asesor1_result->fetch_assoc();
            $kd_asesor1 = $asesor1_data['kd_asesor'];
            
            // Lookup NIA Asesor 2 to get kd_asesor2
            $asesor2_query = "SELECT kd_asesor FROM asesor 
                             WHERE nia = ? AND provinsi_id = ? 
                             AND soft_delete = '1' AND status_keaktifan_id = '1'";
            $asesor2_stmt = $conn->prepare($asesor2_query);
            $asesor2_stmt->bind_param("si", $nia_asesor2, $provinsi_id);
            $asesor2_stmt->execute();
            $asesor2_result = $asesor2_stmt->get_result();
            
            if ($asesor2_result->num_rows === 0) {
                throw new Exception("Baris $rowNumber: NIA Asesor 2 $nia_asesor2 tidak ditemukan");
            }
            
            $asesor2_data = $asesor2_result->fetch_assoc();
            $kd_asesor2 = $asesor2_data['kd_asesor'];
            
            // Double check: kd_asesor1 dan kd_asesor2 tidak boleh sama
            if ($kd_asesor1 === $kd_asesor2) {
                throw new Exception("Baris $rowNumber: Asesor 1 dan Asesor 2 merujuk ke asesor yang sama");
            }
            
            // Check for duplicate (same sekolah_id and tahun_akreditasi)
            $duplicate_query = "SELECT COUNT(*) as count FROM mapping_2025 
                               WHERE sekolah_id = ? AND tahun_akreditasi = ? AND provinsi_id = ?";
            $duplicate_stmt = $conn->prepare($duplicate_query);
            $duplicate_stmt->bind_param("isi", $sekolah_id, $tahun_akreditasi, $provinsi_id);
            $duplicate_stmt->execute();
            $duplicate_result = $duplicate_stmt->get_result();
            $duplicate_data = $duplicate_result->fetch_assoc();
            
            if ($duplicate_data['count'] > 0) {
                // Skip duplicate
                continue;
            }
            
            // Insert data
            $insert_query = "INSERT INTO mapping_2025 
                           (sekolah_id, kd_asesor1, kd_asesor2, tgl_pra_visitasi, tgl_surat_tugas_pra_visitasi, 
                            no_surat_tugas_pra_visitasi, tgl_surat_tugas_visitasi, no_surat_tugas_visitasi, 
                            tahap, tahun_akreditasi, provinsi_id) 
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $insert_stmt = $conn->prepare($insert_query);
            $insert_stmt->bind_param("isssssssssi", 
                $sekolah_id, $kd_asesor1, $kd_asesor2, $tgl_pra_visitasi, $tgl_surat_tugas_pra_visitasi,
                $no_surat_tugas_pra_visitasi, $tgl_surat_tugas_visitasi, $no_surat_tugas_visitasi,
                $tahap, $tahun_akreditasi, $provinsi_id
            );
            
            if ($insert_stmt->execute()) {
                $successCount++;
            } else {
                throw new Exception("Baris $rowNumber: Gagal menyimpan data");
            }
            
        } catch (Exception $e) {
            $errorCount++;
            $errors[] = $e->getMessage();
        }
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Success response
    $response = [
        'success' => true,
        'message' => 'Import selesai',
        'data' => [
            'success_count' => $successCount,
            'error_count' => $errorCount,
            'errors' => $errors,
            'total_processed' => count($rows)
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
