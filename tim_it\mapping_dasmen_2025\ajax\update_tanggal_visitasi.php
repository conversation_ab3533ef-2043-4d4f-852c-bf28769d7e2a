<?php
/**
 * AJAX handler untuk update tanggal visitasi
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Ambil dan validasi input
    $id_mapping = intval($_POST['id_mapping'] ?? 0);
    $tgl_mulai_visitasi = trim($_POST['tgl_mulai_visitasi'] ?? '');
    $tgl_akhir_visitasi = trim($_POST['tgl_akhir_visitasi'] ?? '');
    $provinsi_id = intval($_SESSION['provinsi_id']);

    // Validasi required fields
    if ($id_mapping <= 0) {
        echo json_encode(['success' => false, 'message' => 'ID mapping tidak valid']);
        exit;
    }

    if (empty($tgl_mulai_visitasi)) {
        echo json_encode(['success' => false, 'message' => 'Tanggal mulai visitasi wajib diisi']);
        exit;
    }

    if (empty($tgl_akhir_visitasi)) {
        echo json_encode(['success' => false, 'message' => 'Tanggal akhir visitasi wajib diisi']);
        exit;
    }

    // Validasi format tanggal
    if (!DateTime::createFromFormat('Y-m-d', $tgl_mulai_visitasi)) {
        echo json_encode(['success' => false, 'message' => 'Format tanggal mulai tidak valid']);
        exit;
    }

    if (!DateTime::createFromFormat('Y-m-d', $tgl_akhir_visitasi)) {
        echo json_encode(['success' => false, 'message' => 'Format tanggal akhir tidak valid']);
        exit;
    }

    // Validasi tanggal akhir >= tanggal mulai
    if (strtotime($tgl_akhir_visitasi) < strtotime($tgl_mulai_visitasi)) {
        echo json_encode(['success' => false, 'message' => 'Tanggal akhir harus sama atau setelah tanggal mulai']);
        exit;
    }

    // Escape input untuk keamanan
    $tgl_mulai_visitasi = $conn->real_escape_string($tgl_mulai_visitasi);
    $tgl_akhir_visitasi = $conn->real_escape_string($tgl_akhir_visitasi);

    // Cek apakah mapping exists dan milik provinsi yang benar
    $check_query = "SELECT id_mapping FROM mapping_2025 
                   WHERE id_mapping = $id_mapping AND provinsi_id = $provinsi_id";
    $check_result = $conn->query($check_query);

    if (!$check_result || $check_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Data mapping tidak ditemukan']);
        exit;
    }

    // Update tanggal visitasi
    $update_query = "UPDATE mapping_2025 SET 
                        tgl_mulai_visitasi = '$tgl_mulai_visitasi',
                        tgl_akhir_visitasi = '$tgl_akhir_visitasi'
                     WHERE id_mapping = $id_mapping AND provinsi_id = $provinsi_id";

    if ($conn->query($update_query)) {
        // Format tanggal untuk response
        $tgl_mulai_formatted = date('d/m/Y', strtotime($tgl_mulai_visitasi));
        $tgl_akhir_formatted = date('d/m/Y', strtotime($tgl_akhir_visitasi));

        // Log activity
        error_log("Tanggal Visitasi Updated - ID: $id_mapping, User: " . ($_SESSION['nm_user'] ?? 'Unknown'));

        echo json_encode([
            'success' => true,
            'message' => 'Tanggal visitasi berhasil diupdate',
            'data' => [
                'tgl_mulai_visitasi' => $tgl_mulai_formatted,
                'tgl_akhir_visitasi' => $tgl_akhir_formatted
            ]
        ]);
    } else {
        error_log("Update Tanggal Visitasi Failed: " . $conn->error);
        echo json_encode([
            'success' => false,
            'message' => 'Gagal update tanggal visitasi: ' . $conn->error
        ]);
    }

} catch (Exception $e) {
    error_log("Update Tanggal Visitasi Error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
