<?php
/**
 * AJAX handler untuk save tanggal visitasi Dasmen IASP 2025
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Asesor', '../../../login.php');

header('Content-Type: application/json');

// Set timezone
date_default_timezone_set('Asia/Singapore');

try {
    // Validasi request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }
    
    // Validasi input
    if (!isset($_POST['id_mapping']) || empty($_POST['id_mapping'])) {
        throw new Exception('ID Mapping tidak valid');
    }
    
    if (!isset($_POST['tgl_mulai_visitasi']) || empty($_POST['tgl_mulai_visitasi'])) {
        throw new Exception('Tanggal mulai visitasi harus diisi');
    }
    
    if (!isset($_POST['tgl_akhir_visitasi']) || empty($_POST['tgl_akhir_visitasi'])) {
        throw new Exception('Tanggal akhir visitasi harus diisi');
    }
    
    // Get data from POST
    $id_mapping = mysqli_real_escape_string($conn, $_POST['id_mapping']);
    $tgl_mulai_visitasi = mysqli_real_escape_string($conn, $_POST['tgl_mulai_visitasi']);
    $tgl_akhir_visitasi = mysqli_real_escape_string($conn, $_POST['tgl_akhir_visitasi']);
    
    // Validasi format tanggal
    $date_mulai = DateTime::createFromFormat('Y-m-d', $tgl_mulai_visitasi);
    $date_akhir = DateTime::createFromFormat('Y-m-d', $tgl_akhir_visitasi);
    
    if (!$date_mulai || $date_mulai->format('Y-m-d') !== $tgl_mulai_visitasi) {
        throw new Exception('Format tanggal mulai visitasi tidak valid');
    }
    
    if (!$date_akhir || $date_akhir->format('Y-m-d') !== $tgl_akhir_visitasi) {
        throw new Exception('Format tanggal akhir visitasi tidak valid');
    }
    
    // Validasi logika tanggal
    if ($date_mulai > $date_akhir) {
        throw new Exception('Tanggal mulai visitasi tidak boleh lebih besar dari tanggal akhir');
    }
    
    // Validasi tanggal tidak boleh di masa lalu (opsional, bisa dihapus jika tidak diperlukan)
    $today = new DateTime();
    $today->setTime(0, 0, 0); // Set ke awal hari
    
    if ($date_mulai < $today) {
        throw new Exception('Tanggal mulai visitasi tidak boleh di masa lalu');
    }
    
    // Cek apakah mapping exists dan milik asesor 1 (hanya asesor 1 yang bisa input tanggal)
    $kd_user = mysqli_real_escape_string($conn, $_SESSION['kd_user'] ?? '');
    $provinsi_id = mysqli_real_escape_string($conn, $_SESSION['provinsi_id'] ?? '');
    
    $check_query = "SELECT m.id_mapping, m.kd_asesor1, m.kd_asesor2,
                           m.tgl_mulai_visitasi, m.tgl_akhir_visitasi
                    FROM mapping_2025 m
                    WHERE m.id_mapping = '$id_mapping' 
                        AND m.kd_asesor1 = '$kd_user'
                        AND m.provinsi_id = '$provinsi_id'";
    
    $check_result = mysqli_query($conn, $check_query);
    
    if (!$check_result || mysqli_num_rows($check_result) === 0) {
        throw new Exception('Data mapping tidak ditemukan atau Anda tidak memiliki akses untuk mengubah tanggal visitasi');
    }
    
    $mapping_data = mysqli_fetch_assoc($check_result);
    
    // Update database
    $update_query = "UPDATE mapping_2025 
                     SET tgl_mulai_visitasi = '$tgl_mulai_visitasi',
                         tgl_akhir_visitasi = '$tgl_akhir_visitasi'
                     WHERE id_mapping = '$id_mapping'";
    
    $update_result = mysqli_query($conn, $update_query);
    
    if (!$update_result) {
        $db_error = mysqli_error($conn);
        throw new Exception('Gagal menyimpan data ke database: ' . $db_error);
    }
    
    $affected_rows = mysqli_affected_rows($conn);
    
    if ($affected_rows === 0) {
        throw new Exception('Tidak ada data yang diupdate, periksa ID mapping');
    }
    
    // Format tanggal untuk response
    $tgl_mulai_formatted = $date_mulai->format('d-m-Y');
    $tgl_akhir_formatted = $date_akhir->format('d-m-Y');
    
    // Response sukses
    echo json_encode([
        'success' => true,
        'message' => 'Tanggal visitasi berhasil disimpan',
        'data' => [
            'id_mapping' => $id_mapping,
            'tgl_mulai_visitasi' => $tgl_mulai_visitasi,
            'tgl_akhir_visitasi' => $tgl_akhir_visitasi,
            'tgl_mulai_formatted' => $tgl_mulai_formatted,
            'tgl_akhir_formatted' => $tgl_akhir_formatted,
            'update_date' => date('Y-m-d H:i:s')
        ]
    ]);
    
} catch (Exception $e) {
    // Response error
    echo json_encode([
        'success' => false,
        'message' => 'Gagal menyimpan tanggal visitasi',
        'error' => $e->getMessage()
    ]);
}
?>
