<?php
/**
 * AJAX handler untuk import Excel mapping validasi kesetaraan
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

header('Content-Type: application/json');

try {
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Check if data was sent
    if (!isset($_POST['excel_data']) || empty($_POST['excel_data'])) {
        throw new Exception('Data Excel tidak ditemukan');
    }
    
    // Parse JSON data
    $rows = json_decode($_POST['excel_data'], true);
    
    if (!is_array($rows)) {
        throw new Exception('Format data tidak valid');
    }
    
    if (empty($rows)) {
        throw new Exception('Tidak ada data untuk diimport');
    }
    
    // Check max rows (100)
    if (count($rows) > 100) {
        throw new Exception('Maksimal 100 baris data. Data Anda memiliki ' . count($rows) . ' baris');
    }
    
    // Get session data
    $provinsi_id = intval($_SESSION['provinsi_id']);
    
    // Process and Insert
    $successCount = 0;
    $errorCount = 0;
    $errors = [];
    
    // Begin transaction
    $conn->autocommit(false);
    
    foreach ($rows as $index => $row) {
        $rowNumber = $index + 2; // +2 because we removed header and array is 0-based
        
        try {
            // Extract data from row (9 columns)
            $npsn = trim($row[0] ?? '');
            $nia_validator1 = trim($row[1] ?? '');
            $nia_validator2 = trim($row[2] ?? '');
            $tgl_mulai_validasi = trim($row[3] ?? '');
            $tgl_akhir_validasi = trim($row[4] ?? '');
            $no_surat_validasi = trim($row[5] ?? '');
            $tgl_surat_validasi = trim($row[6] ?? '');
            $tahap = trim($row[7] ?? '');
            $tahun_akreditasi = trim($row[8] ?? '');
            
            // Validate required fields
            if (empty($npsn) || empty($nia_validator1) || empty($nia_validator2) || 
                empty($tgl_mulai_validasi) || empty($tgl_akhir_validasi) || 
                empty($no_surat_validasi) || empty($tgl_surat_validasi) || 
                empty($tahap) || empty($tahun_akreditasi)) {
                throw new Exception("Baris $rowNumber: Data tidak lengkap");
            }
            
            // Validate tahun format
            if (!preg_match('/^\d{4}$/', $tahun_akreditasi)) {
                throw new Exception("Baris $rowNumber: Format tahun tidak valid");
            }
            
            // Validate tahap (numeric)
            if (!is_numeric($tahap) || $tahap < 1) {
                throw new Exception("Baris $rowNumber: Tahap harus angka positif");
            }
            
            // Validate validator1 dan validator2 tidak sama
            if ($nia_validator1 === $nia_validator2) {
                throw new Exception("Baris $rowNumber: Validator 1 dan Validator 2 tidak boleh sama");
            }
            
            // Validate date formats
            $dateFields = [
                'tgl_mulai_validasi' => $tgl_mulai_validasi,
                'tgl_akhir_validasi' => $tgl_akhir_validasi,
                'tgl_surat_validasi' => $tgl_surat_validasi
            ];
            
            foreach ($dateFields as $fieldName => $dateValue) {
                if (!empty($dateValue)) {
                    $date = DateTime::createFromFormat('Y-m-d', $dateValue);
                    if (!$date || $date->format('Y-m-d') !== $dateValue) {
                        throw new Exception("Baris $rowNumber: Format tanggal $fieldName tidak valid (gunakan yyyy-mm-dd)");
                    }
                }
            }
            
            // Lookup NPSN to get sekolah_id
            $sekolah_query = "SELECT sekolah_id FROM sekolah 
                             WHERE npsn = ? AND provinsi_id = ? AND rumpun = 'kesetaraan' 
                             AND soft_delete = '1' AND status_keaktifan_id = '1'";
            $sekolah_stmt = $conn->prepare($sekolah_query);
            $sekolah_stmt->bind_param("si", $npsn, $provinsi_id);
            $sekolah_stmt->execute();
            $sekolah_result = $sekolah_stmt->get_result();
            
            if ($sekolah_result->num_rows === 0) {
                throw new Exception("Baris $rowNumber: NPSN $npsn tidak ditemukan");
            }
            
            $sekolah_data = $sekolah_result->fetch_assoc();
            $sekolah_id = $sekolah_data['sekolah_id'];
            
            // Lookup NIA Validator 1 to get kd_asesor1
            $validator1_query = "SELECT kd_asesor FROM asesor 
                                WHERE nia = ? AND provinsi_id = ? 
                                AND soft_delete = '1' AND status_keaktifan_id = '1'";
            $validator1_stmt = $conn->prepare($validator1_query);
            $validator1_stmt->bind_param("si", $nia_validator1, $provinsi_id);
            $validator1_stmt->execute();
            $validator1_result = $validator1_stmt->get_result();
            
            if ($validator1_result->num_rows === 0) {
                throw new Exception("Baris $rowNumber: NIA Validator 1 $nia_validator1 tidak ditemukan");
            }
            
            $validator1_data = $validator1_result->fetch_assoc();
            $kd_asesor1 = $validator1_data['kd_asesor'];
            
            // Lookup NIA Validator 2 to get kd_asesor2
            $validator2_query = "SELECT kd_asesor FROM asesor 
                                WHERE nia = ? AND provinsi_id = ? 
                                AND soft_delete = '1' AND status_keaktifan_id = '1'";
            $validator2_stmt = $conn->prepare($validator2_query);
            $validator2_stmt->bind_param("si", $nia_validator2, $provinsi_id);
            $validator2_stmt->execute();
            $validator2_result = $validator2_stmt->get_result();
            
            if ($validator2_result->num_rows === 0) {
                throw new Exception("Baris $rowNumber: NIA Validator 2 $nia_validator2 tidak ditemukan");
            }
            
            $validator2_data = $validator2_result->fetch_assoc();
            $kd_asesor2 = $validator2_data['kd_asesor'];
            
            // Double check: kd_asesor1 dan kd_asesor2 tidak boleh sama
            if ($kd_asesor1 === $kd_asesor2) {
                throw new Exception("Baris $rowNumber: Validator 1 dan Validator 2 merujuk ke asesor yang sama");
            }
            
            // Check for duplicate (same sekolah_id and tahun_akreditasi)
            $duplicate_query = "SELECT COUNT(*) as count FROM mapping_validasi_2024 
                               WHERE sekolah_id = ? AND tahun_akreditasi = ? AND provinsi_id = ?";
            $duplicate_stmt = $conn->prepare($duplicate_query);
            $duplicate_stmt->bind_param("isi", $sekolah_id, $tahun_akreditasi, $provinsi_id);
            $duplicate_stmt->execute();
            $duplicate_result = $duplicate_stmt->get_result();
            $duplicate_data = $duplicate_result->fetch_assoc();
            
            if ($duplicate_data['count'] > 0) {
                // Skip duplicate
                continue;
            }
            
            // Insert data
            $insert_query = "INSERT INTO mapping_validasi_2024 
                           (sekolah_id, kd_asesor1, kd_asesor2, tgl_mulai_validasi, tgl_akhir_validasi, 
                            no_surat_validasi, tgl_surat_validasi, tahap, tahun_akreditasi, provinsi_id) 
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $insert_stmt = $conn->prepare($insert_query);
            $insert_stmt->bind_param("issssssssi", 
                $sekolah_id, $kd_asesor1, $kd_asesor2, $tgl_mulai_validasi, $tgl_akhir_validasi,
                $no_surat_validasi, $tgl_surat_validasi, $tahap, $tahun_akreditasi, $provinsi_id
            );
            
            if ($insert_stmt->execute()) {
                $successCount++;
            } else {
                throw new Exception("Baris $rowNumber: Gagal menyimpan data");
            }
            
        } catch (Exception $e) {
            $errorCount++;
            $errors[] = $e->getMessage();
        }
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Success response
    $response = [
        'success' => true,
        'message' => 'Import selesai',
        'data' => [
            'success_count' => $successCount,
            'error_count' => $errorCount,
            'errors' => $errors,
            'total_processed' => count($rows)
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
