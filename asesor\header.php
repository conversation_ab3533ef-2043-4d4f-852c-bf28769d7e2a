<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>SIM4K - Sistem Informasi Manajemen Akreditasi Sekolah Kalimantan Timur</title>

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Ionicons -->
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css">
    <!-- iCheck -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css">
    <!-- JQVMap -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqvmap/1.5.1/jqvmap.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/css/adminlte.min.css">
    <!-- overlayScrollbars -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.1/css/OverlayScrollbars.min.css">
    <!-- Daterange picker -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-daterangepicker/3.0.5/daterangepicker.css">
    <!-- summernote -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote-bs4.min.css">
    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/datatables.net-responsive-bs4/2.2.9/responsive.bootstrap4.min.css">
    
    <!-- Custom CSS -->
    <style>
        /* Global Font Size Reduction */
        body, .wrapper {
            font-size: 15px !important;
        }

        /* Navbar */
        .main-header .navbar-brand {
            font-weight: bold;
            font-size: 18px !important;
        }

        .navbar-nav .nav-link {
            font-size: 14px !important;
        }

        /* Sidebar */
        .main-sidebar .nav-sidebar .nav-link {
            font-size: 14px !important;
        }

        .main-sidebar .brand-text {
            font-size: 18px !important;
        }

        /* Content */
        .content-wrapper {
            background-color: #f4f6f9;
            font-size: 15px !important;
        }

        .content-header h1 {
            font-size: 24px !important;
        }

        /* Cards */
        .card-header {
            background-color: #007bff;
            color: white;
            font-size: 16px !important;
        }

        .card-title {
            font-size: 16px !important;
            font-weight: 600;
        }

        .card-body {
            font-size: 14px !important;
        }

        /* Buttons */
        .btn {
            font-size: 13px !important;
            padding: 0.25rem 0.5rem !important;
        }

        .btn-sm {
            font-size: 12px !important;
            padding: 0.2rem 0.4rem !important;
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }

        /* Tables */
        .table {
            font-size: 13px !important;
        }

        .table th {
            font-size: 13px !important;
            font-weight: 600;
            padding: 0.5rem !important;
        }

        .table td {
            font-size: 13px !important;
            padding: 0.4rem !important;
        }

        /* DataTables */
        .dataTables_wrapper .dataTables_length select,
        .dataTables_wrapper .dataTables_filter input {
            font-size: 13px !important;
        }

        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_paginate {
            font-size: 13px !important;
        }

        .page-link {
            font-size: 12px !important;
            padding: 0.25rem 0.4rem !important;
        }

        /* Forms */
        .form-control {
            font-size: 14px !important;
            padding: 0.3rem 0.5rem !important;
        }

        .form-control-sm {
            font-size: 13px !important;
            padding: 0.2rem 0.4rem !important;
        }

        label {
            font-size: 14px !important;
            font-weight: 600;
        }

        /* Modals */
        .modal-title {
            font-size: 18px !important;
        }

        .modal-body {
            font-size: 14px !important;
        }

        /* Alerts */
        .alert {
            font-size: 14px !important;
            padding: 0.5rem 0.75rem !important;
        }

        /* Breadcrumb */
        .breadcrumb {
            font-size: 13px !important;
        }

        /* Badges */
        .badge {
            font-size: 11px !important;
            padding: 0.2em 0.4em !important;
        }

        /* Small boxes (dashboard) */
        .small-box h3 {
            font-size: 30px !important;
        }

        .small-box p {
            font-size: 14px !important;
        }

        .small-box .small-box-footer {
            font-size: 13px !important;
        }

        /* Dropdown */
        .dropdown-menu {
            font-size: 14px !important;
        }

        .dropdown-item {
            font-size: 14px !important;
            padding: 0.3rem 1rem !important;
        }

        /* Footer */
        .main-footer {
            font-size: 13px !important;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            body, .wrapper {
                font-size: 14px !important;
            }

            .table {
                font-size: 12px !important;
            }

            .btn {
                font-size: 12px !important;
            }
        }
    </style>


</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

    <!-- Preloader -->
    <div class="preloader flex-column justify-content-center align-items-center">
        <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
            <span class="sr-only">Loading...</span>
        </div>
        <p class="mt-3 text-muted">Memuat SIM4K...</p>
    </div>
