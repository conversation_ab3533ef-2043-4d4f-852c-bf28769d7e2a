# Modul Mapping Asesor Validasi SM

## 📋 Deskripsi
Modul untuk mengelola mapping asesor validasi Sekolah Menengah (SMA dan SMK) tahun 2025. Modul ini memungkinkan Staff IT untuk melihat, mengelola, dan memantau proses validasi akreditasi sekolah menengah.

## 🗂️ Struktur File
```
tim_it/mapping_dasmen_validasi_2025/
├── validasi.php              # File utama (view)
├── ajax/
│   └── get_validasi.php      # Handler untuk mengambil data
├── js/
│   └── validasi.js           # JavaScript untuk interaksi
└── README.md                 # Dokumentasi ini
```

## 🗄️ Struktur Database

### Tabel Utama
- **mapping_validasi_2025** - Data mapping validasi
- **sekolah** - Data sekolah yang divalidasi
- **asesor_1** & **asesor_2** - Data validator
- **jenjang** - Master jenjang pendidikan
- **kab_kota** - Master kabupaten/kota

### Re<PERSON>i <PERSON>
```sql
mapping_validasi_2025.sekolah_id = sekolah.sekolah_id
mapping_validasi_2025.kd_asesor1 = asesor_1.kd_asesor1
mapping_validasi_2025.kd_asesor2 = asesor_2.kd_asesor2
sekolah.jenjang_id = jenjang.jenjang_id
sekolah.kota_id = kab_kota.kota_id
```

## 📊 Kolom Tabel

| No | Kolom | Sumber Data | Deskripsi |
|----|-------|-------------|-----------|
| 1 | NO | Auto increment | Nomor urut |
| 2 | NPSN | sekolah.npsn | Nomor Pokok Sekolah Nasional |
| 3 | NAMA SEKOLAH | sekolah.nama_sekolah | Nama lengkap sekolah |
| 4 | JENJANG | jenjang.nm_jenjang | Jenjang pendidikan (SMA/SMK) |
| 5 | RUMPUN | sekolah.rumpun | Rumpun sekolah |
| 6 | KAB/KOTA | kab_kota.nm_kota | Kabupaten/Kota sekolah |
| 7 | NIA VALIDATOR 1 | asesor_1.nia1 | NIA asesor pertama |
| 8 | NAMA VALIDATOR 1 | asesor_1.nm_asesor1 | Nama asesor pertama |
| 9 | NIA VALIDATOR 2 | asesor_2.nia2 | NIA asesor kedua |
| 10 | NAMA VALIDATOR 2 | asesor_2.nm_asesor2 | Nama asesor kedua |
| 11 | TAHUN AKREDITASI | mapping_validasi_2025.tahun_akreditasi | Tahun akreditasi |
| 12 | TAHAP VISITASI | mapping_validasi_2025.tahap | Status tahap validasi |
| 13 | AKSI | - | Tombol aksi (Detail) |

## 🎯 Fitur Utama

### ✅ Sudah Diimplementasikan
- [x] Tampilan data dalam DataTables
- [x] Filter berdasarkan tahun akreditasi
- [x] Search/pencarian data
- [x] Pagination dan sorting
- [x] Responsive design
- [x] Tombol detail untuk setiap record
- [x] Filter otomatis untuk jenjang SM (SMA/SMK)
- [x] Session-based filtering berdasarkan provinsi

### 🔄 Dalam Pengembangan
- [ ] Form input data mapping
- [ ] Form edit data mapping
- [ ] Upload dan manajemen file dokumen
- [ ] Export data ke Excel
- [ ] Import data dari Excel
- [ ] Modal detail lengkap
- [ ] Validasi form yang komprehensif

## 🔒 Keamanan & Akses

### Kontrol Akses
- Hanya user dengan level **Staff IT** yang dapat mengakses
- Data difilter berdasarkan `provinsi_id` dari session user
- Semua input di-sanitize untuk mencegah SQL injection

### Session Requirements
```php
requireLevel('Staff IT', '../../login.php');
$provinsi_id_session = $_SESSION['provinsi_id'];
```

## 🎨 UI/UX Features

### DataTables Configuration
- Server-side processing untuk performa optimal
- Responsive design untuk mobile compatibility
- Custom styling dengan AdminLTE theme
- Auto-refresh dan real-time updates

### Filter & Search
- Filter dropdown tahun akreditasi
- Global search across all columns
- Advanced filtering capabilities

## 🔧 Teknologi

### Frontend
- **AdminLTE 3.2.0** - Admin template
- **DataTables** - Table management
- **Bootstrap 4** - CSS framework
- **jQuery** - JavaScript library
- **Font Awesome** - Icons

### Backend
- **PHP 7+** - Server-side language
- **MySQLi** - Database connection
- **Prepared Statements** - SQL injection protection

## 📝 Penggunaan

### Akses Modul
1. Login sebagai Staff IT
2. Navigasi ke menu Mapping Dasmen Validasi 2025
3. Pilih submenu "Validasi SM"

### Filter Data
1. Klik dropdown "Tahun Akreditasi"
2. Pilih tahun yang diinginkan
3. Data akan otomatis difilter

### Pencarian
1. Gunakan kotak search di kanan atas tabel
2. Ketik kata kunci (NPSN, nama sekolah, dll)
3. Hasil akan ditampilkan secara real-time

## 🚀 Pengembangan Selanjutnya

### Prioritas Tinggi
1. Implementasi form input/edit mapping
2. Upload dan manajemen file dokumen
3. Modal detail dengan informasi lengkap

### Prioritas Medium
1. Export/Import Excel functionality
2. Bulk operations (edit multiple records)
3. Advanced reporting features

### Prioritas Rendah
1. Email notifications
2. Audit trail
3. Advanced analytics dashboard

## 🐛 Troubleshooting

### Error Umum
1. **DataTables tidak muncul**: Pastikan file JS sudah di-include
2. **Data kosong**: Cek koneksi database dan session provinsi_id
3. **Filter tidak bekerja**: Pastikan parameter POST terkirim dengan benar

### Debug Mode
Untuk debugging, aktifkan error reporting di PHP:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## 📞 Support
Untuk pertanyaan atau issue, hubungi tim pengembang SIM4K.
