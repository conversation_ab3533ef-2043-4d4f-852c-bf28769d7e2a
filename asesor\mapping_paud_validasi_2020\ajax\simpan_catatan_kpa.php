<?php
require_once '../../../koneksi.php';

// Include session checker dan require level As<PERSON><PERSON>
require_once '../../../check_session.php';
requireLevel('Asesor', '../../../login.php');

// Set response header
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }

    // Get session variables
    $kd_user = $_SESSION['kd_user'] ?? '';
    $provinsi_id = $_SESSION['provinsi_id'] ?? '';

    // Get data from POST
    $id_mapping = mysqli_real_escape_string($conn, $_POST['id_mapping'] ?? '');
    $nama_asesor_kpa = mysqli_real_escape_string($conn, $_POST['nama_asesor_kpa'] ?? '');
    $catatan_penilaian_asesor_kpa = mysqli_real_escape_string($conn, $_POST['catatan_penilaian_asesor_kpa'] ?? '');

    if (empty($id_mapping)) {
        throw new Exception('ID mapping tidak valid');
    }

    // Cek apakah mapping exists dan user memiliki akses
    $check_query = "SELECT kd_asesor1, kd_asesor2 
                    FROM mapping_paud_validasi 
                    WHERE id_mapping = ? AND provinsi_id = ?";
    
    $stmt_check = $conn->prepare($check_query);
    $stmt_check->bind_param('si', $id_mapping, $provinsi_id);
    $stmt_check->execute();
    $check_result = $stmt_check->get_result();
    
    if ($check_result->num_rows === 0) {
        throw new Exception('Data mapping tidak ditemukan');
    }
    
    $mapping_data = $check_result->fetch_assoc();
    
    // Validasi akses - hanya asesor1 yang bisa input catatan KPA
    if ($kd_user !== $mapping_data['kd_asesor1']) {
        throw new Exception('Anda tidak memiliki akses untuk mengisi catatan ini');
    }

    // Update database
    $update_query = "UPDATE mapping_paud_validasi 
                     SET nama_asesor_kpa = ?, 
                         catatan_penilaian_asesor_kpa = ? 
                     WHERE id_mapping = ? AND provinsi_id = ?";
    
    $stmt_update = $conn->prepare($update_query);
    $stmt_update->bind_param('sssi', $nama_asesor_kpa, $catatan_penilaian_asesor_kpa, $id_mapping, $provinsi_id);
    
    if (!$stmt_update->execute()) {
        throw new Exception('Gagal menyimpan data ke database: ' . $conn->error);
    }

    // Response sukses
    echo json_encode([
        'success' => true,
        'message' => 'Catatan penilaian asesor KPA berhasil disimpan'
    ]);

} catch (Exception $e) {
    // Log error untuk debugging
    error_log("Simpan catatan KPA error: " . $e->getMessage());
    
    // Response error
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    // Close database connection
    if (isset($conn)) {
        $conn->close();
    }
}
?>
