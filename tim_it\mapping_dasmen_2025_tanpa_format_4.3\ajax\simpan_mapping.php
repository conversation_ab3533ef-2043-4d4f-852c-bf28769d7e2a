<?php
/**
 * AJAX handler untuk simpan data mapping visitasi SM
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Debug: Log received data
    error_log("Received POST data: " . print_r($_POST, true));

    // Ambil dan validasi input dengan real_escape_string
    $sekolah_id = intval($_POST['sekolah_id'] ?? 0);
    $kd_asesor1 = $conn->real_escape_string(trim($_POST['kd_asesor1'] ?? ''));
    $kd_asesor2 = $conn->real_escape_string(trim($_POST['kd_asesor2'] ?? ''));
    $tahun_akreditasi = $conn->real_escape_string(trim($_POST['tahun_akreditasi'] ?? ''));
    $tahap = intval($_POST['tahap'] ?? 0);

    // Optional fields - gunakan string kosong untuk field yang tidak diisi
    $tgl_pra_visitasi = !empty($_POST['tgl_pra_visitasi']) ? $conn->real_escape_string($_POST['tgl_pra_visitasi']) : '';
    $tgl_surat_tugas_pra_visitasi = !empty($_POST['tgl_surat_tugas_pra_visitasi']) ? $conn->real_escape_string($_POST['tgl_surat_tugas_pra_visitasi']) : '';
    $no_surat_tugas_pra_visitasi = !empty($_POST['no_surat_tugas_pra_visitasi']) ? $conn->real_escape_string(trim($_POST['no_surat_tugas_pra_visitasi'])) : '';
    $tgl_mulai_visitasi = !empty($_POST['tgl_mulai_visitasi']) ? $conn->real_escape_string($_POST['tgl_mulai_visitasi']) : '';
    $tgl_akhir_visitasi = !empty($_POST['tgl_akhir_visitasi']) ? $conn->real_escape_string($_POST['tgl_akhir_visitasi']) : '';
    $tgl_surat_tugas_visitasi = !empty($_POST['tgl_surat_tugas_visitasi']) ? $conn->real_escape_string($_POST['tgl_surat_tugas_visitasi']) : '';
    $no_surat_tugas_visitasi = !empty($_POST['no_surat_tugas_visitasi']) ? $conn->real_escape_string(trim($_POST['no_surat_tugas_visitasi'])) : '';

    // Debug: Log processed data
    error_log("Processed data - sekolah_id: $sekolah_id, kd_asesor1: $kd_asesor1, kd_asesor2: $kd_asesor2");
    
    // Validasi required fields
    if ($sekolah_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'Sekolah ID tidak valid']);
        exit;
    }
    
    if (empty($kd_asesor1)) {
        echo json_encode(['success' => false, 'message' => 'Kode Asesor 1 wajib diisi']);
        exit;
    }
    
    if (empty($kd_asesor2)) {
        echo json_encode(['success' => false, 'message' => 'Kode Asesor 2 wajib diisi']);
        exit;
    }
    
    if (empty($tahun_akreditasi)) {
        echo json_encode(['success' => false, 'message' => 'Tahun akreditasi wajib diisi']);
        exit;
    }
    
    if ($tahap <= 0) {
        echo json_encode(['success' => false, 'message' => 'Tahap wajib diisi']);
        exit;
    }
    
    if ($kd_asesor1 === $kd_asesor2) {
        echo json_encode(['success' => false, 'message' => 'Asesor 1 dan Asesor 2 tidak boleh sama']);
        exit;
    }
    
    // Ambil provinsi_id dari session
    $provinsi_id = intval($_SESSION['provinsi_id']);

    // Validasi sekolah exists dan belum ada mapping
    $check_sekolah = "SELECT sekolah_id FROM sekolah
                     WHERE sekolah_id = $sekolah_id AND provinsi_id = $provinsi_id AND rumpun = 'dasmen'";
    $result_check = $conn->query($check_sekolah);

    if (!$result_check || $result_check->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Sekolah tidak valid']);
        exit;
    }

    // Cek duplikasi mapping
    $check_mapping = "SELECT id_mapping FROM mapping_2025
                     WHERE sekolah_id = $sekolah_id AND provinsi_id = $provinsi_id";
    $result_mapping = $conn->query($check_mapping);

    if (!$result_mapping) {
        echo json_encode(['success' => false, 'message' => 'Error checking duplicate mapping']);
        exit;
    }

    if ($result_mapping->num_rows > 0) {
        echo json_encode(['success' => false, 'message' => 'Sekolah sudah ada dalam mapping visitasi']);
        exit;
    }
    
    // Insert data mapping - hanya field required saja untuk menghindari error NULL
    $insert_query = "INSERT INTO mapping_2025 (
                        sekolah_id,
                        kd_asesor1,
                        kd_asesor2,
                        tahap,
                        tahun_akreditasi,
                        provinsi_id
                    ) VALUES (
                        $sekolah_id,
                        '$kd_asesor1',
                        '$kd_asesor2',
                        $tahap,
                        '$tahun_akreditasi',
                        $provinsi_id
                    )";

    // Debug: Log query
    error_log("Insert Query: " . $insert_query);

    $result_insert = $conn->query($insert_query);

    if ($result_insert) {
        $id_mapping = $conn->insert_id;

        // Update field optional jika ada yang diisi
        $update_fields = [];

        if (!empty($tgl_pra_visitasi)) {
            $update_fields[] = "tgl_pra_visitasi = '$tgl_pra_visitasi'";
        }
        if (!empty($tgl_surat_tugas_pra_visitasi)) {
            $update_fields[] = "tgl_surat_tugas_pra_visitasi = '$tgl_surat_tugas_pra_visitasi'";
        }
        if (!empty($no_surat_tugas_pra_visitasi)) {
            $update_fields[] = "no_surat_tugas_pra_visitasi = '$no_surat_tugas_pra_visitasi'";
        }
        if (!empty($tgl_mulai_visitasi)) {
            $update_fields[] = "tgl_mulai_visitasi = '$tgl_mulai_visitasi'";
        }
        if (!empty($tgl_akhir_visitasi)) {
            $update_fields[] = "tgl_akhir_visitasi = '$tgl_akhir_visitasi'";
        }
        if (!empty($tgl_surat_tugas_visitasi)) {
            $update_fields[] = "tgl_surat_tugas_visitasi = '$tgl_surat_tugas_visitasi'";
        }
        if (!empty($no_surat_tugas_visitasi)) {
            $update_fields[] = "no_surat_tugas_visitasi = '$no_surat_tugas_visitasi'";
        }

        // Jika ada field optional yang diisi, update
        if (!empty($update_fields)) {
            $update_query = "UPDATE mapping_2025 SET " . implode(', ', $update_fields) . " WHERE id_mapping = $id_mapping";
            $conn->query($update_query);
        }

        // Log activity
        error_log("Mapping Visitasi Created - ID: $id_mapping, User: " . ($_SESSION['nm_user'] ?? 'Unknown'));

        echo json_encode([
            'success' => true,
            'message' => 'Data mapping berhasil disimpan',
            'id_mapping' => $id_mapping
        ]);
    } else {
        // Log detailed error
        error_log("Insert Query Failed: " . $conn->error);
        error_log("Query was: " . $insert_query);

        echo json_encode([
            'success' => false,
            'message' => 'Database error: ' . $conn->error
        ]);
    }
    
} catch (Exception $e) {
    error_log("Simpan Mapping Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));

    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
