/**
 * JavaScript untuk Modul Mapping Validasi Dasmen IASP 2024
 * Based on KPA PAUD pattern
 * Version: 1.0.0
 */

// Pastikan jQuery tersedia sebelum menjalankan script
if (typeof jQuery !== 'undefined' && typeof $ !== 'undefined') {
    $(document).ready(function() {
        // Initialize page
        initializePage();
    });
} else {
    // Fallback: wait for window load
    window.addEventListener('load', function() {
        setTimeout(function() {
            if (typeof jQuery !== 'undefined' && typeof $ !== 'undefined') {
                initializePage();
            } else {
                console.error('jQuery is not loaded. Please make sure jQuery is included before this script.');
            }
        }, 100);
    });
}

/**
 * Initialize page functionality
 */
function initializePage() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Initialize modals
    $('#modal-upload').on('hidden.bs.modal', function () {
        resetUploadForm();
    });
    
    // File input change event
    $('#file-upload').on('change', function() {
        const file = this.files[0];
        if (file) {
            // Show file info
            const fileInfo = `File: ${file.name} (${formatFileSize(file.size)})`;
            $(this).next('.form-text').text(fileInfo);
        }
    });
    
    // Keyboard shortcuts
    $(document).keydown(function(e) {
        // ESC key to close modals
        if (e.keyCode === 27) {
            $('.modal').modal('hide');
        }
    });
    
    // Add CSS for visual effects
    addCustomStyles();
}

/**
 * Add custom CSS styles
 */
function addCustomStyles() {
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .badge-updated {
                animation: pulse-success 2s ease-in-out;
                box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
            }
            
            @keyframes pulse-success {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); box-shadow: 0 0 15px rgba(40, 167, 69, 0.7); }
                100% { transform: scale(1); }
            }
            
            .btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }
            
            .badge[onclick]:hover {
                transform: scale(1.05);
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }
            
            .badge-success[onclick]:hover {
                background-color: #1e7e34 !important;
            }
        `)
        .appendTo('head');
}

/**
 * Upload file function - opens modal with file type and mapping ID
 * @param {number} idMapping - ID mapping
 * @param {string} fileType - Type of file to upload
 */
function uploadFile(idMapping, fileType) {
    // Set hidden form values
    $('#upload-id-mapping').val(idMapping);
    $('#upload-file-type').val(fileType);
    
    // Set modal title based on file type
    let modalTitle = 'Upload File Validasi';
    switch(fileType) {
        case 'format_5_1_berita_acara_hasil_validasi_1':
            modalTitle = 'Upload File Format 5.1 Berita Acara Hasil Validasi 1';
            break;
        case 'format_5_1_berita_acara_hasil_validasi_2':
            modalTitle = 'Upload File Format 5.1 Berita Acara Hasil Validasi 2';
            break;
    }
    
    $('#modal-upload-label').text(modalTitle);
    
    // Show modal
    $('#modal-upload').modal('show');
}

/**
 * Upload file to server via AJAX dengan real-time update
 */
function uploadFileToServer() {
    const formData = new FormData($('#form-upload')[0]);
    const idMapping = $('#upload-id-mapping').val();
    const fileType = $('#upload-file-type').val();
    
    // Disable upload button dan ubah text
    $('#btn-upload').prop('disabled', true).text('Uploading...');
    
    $.ajax({
        url: 'ajax/upload_file.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(response) {
            console.log('Upload response:', response);
            if (response && response.success) {
                // Close modal tanpa notifikasi
                $('#modal-upload').modal('hide');
                
                // Update status badge secara real-time menggunakan pola KPA PAUD
                updatePreviewStatus(idMapping, fileType, true, response.data.filename);
                
            } else {
                showError(response.message || 'Gagal mengupload file');
            }
        },
        error: function(xhr, status, error) {
            console.error('Upload error:', error);
            console.error('Response text:', xhr.responseText);
            
            // Try to parse error response
            try {
                const errorResponse = JSON.parse(xhr.responseText);
                showError(errorResponse.message || 'Terjadi kesalahan saat mengupload file');
            } catch (e) {
                showError('Terjadi kesalahan saat mengupload file: ' + xhr.responseText);
            }
        },
        complete: function() {
            // Enable button kembali
            $('#btn-upload').prop('disabled', false).text('Upload');
        }
    });
}

/**
 * Update preview status in table - mengikuti pola KPA PAUD
 * @param {number} idMapping - ID mapping
 * @param {string} fileType - File type
 * @param {boolean} uploaded - Upload status
 * @param {string} filename - Filename (optional)
 */
function updatePreviewStatus(idMapping, fileType, uploaded, filename) {
    // Buat ID badge yang spesifik berdasarkan idMapping dan fileType
    const badgeId = `badge-${idMapping}-${fileType}`;
    
    // Cari badge berdasarkan ID yang spesifik
    const $badge = $(`#${badgeId}`);
    
    if ($badge.length > 0) {
        if (uploaded) {
            // Update badge dari "Belum Upload" ke "Sudah Upload" dengan onclick
            $badge.removeClass('badge-danger')
                   .addClass('badge-success')
                   .html('<i class="fas fa-eye mr-1"></i>Sudah Upload')
                   .css('cursor', 'pointer')
                   .attr('title', 'Klik untuk melihat file')
                   .attr('onclick', `previewFile('${filename}')`);
        } else {
            // Update badge ke "Belum Upload"
            $badge.removeClass('badge-success')
                   .addClass('badge-danger')
                   .text('Belum Upload')
                   .css('cursor', 'default')
                   .removeAttr('title')
                   .removeAttr('onclick');
        }
        
        // Tambah efek visual untuk menunjukkan perubahan
        $badge.addClass('badge-updated');
        setTimeout(() => {
            $badge.removeClass('badge-updated');
        }, 2000);
    }
}

/**
 * Preview uploaded file in new tab - mengikuti pola KPA PAUD
 * @param {string} filename - Filename to preview
 */
function previewFile(filename) {
    if (!filename) {
        showError('File tidak ditemukan');
        return;
    }
    
    // Open preview in new tab
    const previewUrl = `preview_file.php?file=${encodeURIComponent(filename)}`;
    window.open(previewUrl, '_blank');
}

/**
 * Reset upload form
 */
function resetUploadForm() {
    $('#form-upload')[0].reset();
    $('#btn-upload').prop('disabled', false).text('Upload');
    $('#file-upload').next('.form-text').text('Format file yang diizinkan: PDF (maksimal 10MB)');
}

/**
 * Minimal error notification function (hanya untuk error)
 */
function showError(message) {
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;
    $('#alert-container').html(alertHtml);
    setTimeout(() => $('.alert').fadeOut(), 5000);
}

/**
 * Format file size helper function
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Download surat tugas validasi function
 */
function downloadSuratTugasValidasi(idMapping) {
    const suratTugasUrl = 'mapping_validasi_st_validasi_2024.php?kode=' + idMapping;
    window.open(suratTugasUrl, '_blank');
}
