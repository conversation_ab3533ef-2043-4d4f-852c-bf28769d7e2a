<?php
/**
 * AJAX handler untuk hapus mapping validasi
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi parameter
    if (!isset($_POST['id_mapping']) || empty($_POST['id_mapping'])) {
        throw new Exception('ID mapping tidak valid');
    }
    
    $id_mapping = intval($_POST['id_mapping']);
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Cek apakah mapping exists dan milik provinsi user
    $check_sql = "SELECT mv.id_mapping, s.nama_sekolah, s.npsn
                  FROM mapping_validasi_2025 mv
                  LEFT JOIN sekolah s ON mv.sekolah_id = s.sekolah_id
                  WHERE mv.id_mapping = ? 
                  AND mv.provinsi_id = ?
                  AND s.rumpun = 'dasmen'";
    
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("ii", $id_mapping, $provinsi_id_session);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows == 0) {
        throw new Exception('Data mapping tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    $mapping_data = $check_result->fetch_assoc();
    
    // Hapus mapping dari database
    $delete_sql = "DELETE FROM mapping_validasi_2025 
                   WHERE id_mapping = ? 
                   AND provinsi_id = ?";
    
    $delete_stmt = $conn->prepare($delete_sql);
    $delete_stmt->bind_param("ii", $id_mapping, $provinsi_id_session);
    
    if ($delete_stmt->execute()) {
        // Cek apakah data benar-benar terhapus
        if ($delete_stmt->affected_rows > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Mapping validasi berhasil dihapus',
                'data' => [
                    'id_mapping' => $id_mapping,
                    'nama_sekolah' => $mapping_data['nama_sekolah'],
                    'npsn' => $mapping_data['npsn']
                ]
            ]);
        } else {
            throw new Exception('Data tidak dapat dihapus atau sudah tidak ada');
        }
    } else {
        throw new Exception('Gagal menghapus mapping: ' . $conn->error);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
    error_log("Error in hapus_mapping.php: " . $e->getMessage());
}
?>
