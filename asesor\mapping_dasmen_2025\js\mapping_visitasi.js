/**
 * JavaScript untuk Mapping Visitasi Dasmen IASP 2025
 * Handles upload files, tanggal input, dan preview functionality
 */

$(document).ready(function() {
    // Initialize components
    initializeUploadModal();
    initializeTanggalModal();
    
    // Set minimum date untuk input tanggal (hari ini)
    const today = new Date().toISOString().split('T')[0];
    $('#tgl-mulai-visitasi, #tgl-akhir-visitasi').attr('min', today);
});

/**
 * Initialize Upload Modal Events
 */
function initializeUploadModal() {
    // Handle upload button click
    $('#btn-upload').on('click', function() {
        const fileInput = $('#file-upload')[0];
        
        if (!fileInput.files.length) {
            showError('Silakan pilih file terlebih dahulu');
            return;
        }
        
        const file = fileInput.files[0];
        const fileType = $('#upload-file-type').val();

        // File types yang bebas upload semua jenis file
        const freeUploadTypes = ['format_4_3_rekap_1', 'format_4_3_rekap_2'];

        if (freeUploadTypes.includes(fileType)) {
            // Untuk LK Penilaian 1 & 2: Bebas semua jenis file, tanpa limit size
            // Hanya validasi basic
            if (file.size <= 0) {
                showError('File tidak valid atau kosong');
                return;
            }
        } else {
            // Untuk file lainnya: Tetap PDF only dengan limit 10MB
            if (file.type !== 'application/pdf') {
                showError('File harus berformat PDF');
                return;
            }

            // Validasi file size (10MB)
            const maxSize = 10 * 1024 * 1024; // 10MB
            if (file.size > maxSize) {
                showError('Ukuran file maksimal 10MB');
                return;
            }
        }
        
        uploadFileToServer();
    });
    
    // Reset form when modal is hidden
    $('#modal-upload').on('hidden.bs.modal', function() {
        resetUploadForm();
    });
    
    // File input change event
    $('#file-upload').on('change', function() {
        const file = this.files[0];
        if (file) {
            // Show file info
            const fileInfo = `File: ${file.name} (${formatFileSize(file.size)})`;
            $(this).next('.form-text').text(fileInfo);
        }
    });

    // Remove progress bar references
    $('.progress').remove();
}

/**
 * Initialize Tanggal Modal Events
 */
function initializeTanggalModal() {
    // Handle save tanggal button click
    $('#btn-save-tanggal').on('click', function() {
        const tglMulai = $('#tgl-mulai-visitasi').val();
        const tglAkhir = $('#tgl-akhir-visitasi').val();
        
        if (!tglMulai || !tglAkhir) {
            showError('Tanggal mulai dan akhir visitasi harus diisi');
            return;
        }
        
        // Validasi tanggal mulai tidak boleh lebih besar dari tanggal akhir
        if (new Date(tglMulai) > new Date(tglAkhir)) {
            showError('Tanggal mulai visitasi tidak boleh lebih besar dari tanggal akhir');
            return;
        }
        
        saveTanggalVisitasi();
    });
    
    // Reset form when modal is hidden
    $('#modal-tanggal').on('hidden.bs.modal', function() {
        resetTanggalForm();
    });
    
    // Auto-set tanggal akhir when tanggal mulai changes
    $('#tgl-mulai-visitasi').on('change', function() {
        const tglMulai = $(this).val();
        if (tglMulai && !$('#tgl-akhir-visitasi').val()) {
            // Set tanggal akhir = tanggal mulai + 1 hari (default)
            const nextDay = new Date(tglMulai);
            nextDay.setDate(nextDay.getDate() + 1);
            $('#tgl-akhir-visitasi').val(nextDay.toISOString().split('T')[0]);
        }
    });
}

/**
 * Upload file to server via AJAX dengan real-time update
 */
function uploadFileToServer() {
    const formData = new FormData($('#form-upload')[0]);
    const idMapping = $('#upload-id-mapping').val();
    const fileType = $('#upload-file-type').val();

    // Disable upload button dan ubah text
    $('#btn-upload').prop('disabled', true).text('Uploading...');

    $.ajax({
        url: 'ajax/upload_file.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                // Close modal tanpa notifikasi
                $('#modal-upload').modal('hide');

                // Update status badge secara real-time
                updateFileStatusBadge(idMapping, fileType, response.data.filename);

            } else {
                showError(response.message || 'Gagal mengupload file');
            }
        },
        error: function(xhr, status, error) {
            console.error('Upload error:', error);
            showError('Terjadi kesalahan saat mengupload file');
        },
        complete: function() {
            // Enable button kembali
            $('#btn-upload').prop('disabled', false).text('Upload');
        }
    });
}

/**
 * Update file status badge secara real-time tanpa reload - HANYA badge yang spesifik
 */
function updateFileStatusBadge(idMapping, fileType, filename) {
    // Buat ID badge yang spesifik berdasarkan idMapping dan fileType
    const badgeId = `badge-${idMapping}-${fileType}`;

    // Cari badge berdasarkan ID yang spesifik - PALING AKURAT!
    const $badge = $(`#${badgeId}`);

    if ($badge.length > 0) {
        // File types yang menggunakan direct download
        const directDownloadTypes = ['format_4_3_rekap_1', 'format_4_3_rekap_2'];

        if (directDownloadTypes.includes(fileType)) {
            // Untuk LK Penilaian 1 & 2: Direct download
            $badge.removeClass('badge-danger')
                   .addClass('badge-success')
                   .html('<i class="fas fa-download"></i> Sudah Upload')
                   .css('cursor', 'pointer')
                   .attr('onclick', `downloadFile('${filename}')`);
        } else {
            // Untuk file lainnya: Preview
            $badge.removeClass('badge-danger')
                   .addClass('badge-success')
                   .html('<i class="fas fa-eye"></i> Sudah Upload')
                   .css('cursor', 'pointer')
                   .attr('onclick', `previewFile('${filename}')`);
        }

        // Tambah efek visual untuk menunjukkan perubahan
        $badge.addClass('badge-updated');
        setTimeout(() => {
            $badge.removeClass('badge-updated');
        }, 2000);
    }
}

/**
 * Save tanggal visitasi via AJAX dengan real-time update
 */
function saveTanggalVisitasi() {
    const formData = $('#form-tanggal').serialize();
    const idMapping = $('#tanggal-id-mapping').val();
    const tglMulai = $('#tgl-mulai-visitasi').val();
    const tglAkhir = $('#tgl-akhir-visitasi').val();

    // Disable save button
    $('#btn-save-tanggal').prop('disabled', true).text('Menyimpan...');

    $.ajax({
        url: 'ajax/save_tanggal.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Close modal tanpa notifikasi
                $('#modal-tanggal').modal('hide');

                // Update tanggal secara real-time
                updateTanggalDisplay(idMapping, response.data.tgl_mulai_formatted, response.data.tgl_akhir_formatted);

            } else {
                showError(response.message || 'Gagal menyimpan tanggal visitasi');
            }
        },
        error: function(xhr, status, error) {
            console.error('Save tanggal error:', error);
            showError('Terjadi kesalahan saat menyimpan tanggal');
        },
        complete: function() {
            // Enable save button
            $('#btn-save-tanggal').prop('disabled', false).text('Simpan');
        }
    });
}

/**
 * Update display tanggal secara real-time tanpa reload dan update visibility tombol
 */
function updateTanggalDisplay(idMapping, tglMulaiFormatted, tglAkhirFormatted) {
    // Cari row dengan approach yang lebih reliable
    $('table tbody tr').each(function() {
        const $row = $(this);

        // Cek apakah ini row yang tepat dengan mencari button yang mengandung idMapping
        // Bisa jadi button "Input Tanggal" sudah hilang, jadi cari dengan cara lain
        const hasMatchingUploadButton = $row.find(`button[onclick*="uploadFile(${idMapping}"]`).length > 0;
        const hasMatchingInputButton = $row.find(`button[onclick*="inputTanggalVisitasi(${idMapping}"]`).length > 0;
        const hasMatchingDownloadButton = $row.find(`button[onclick*="downloadSuratPraAkreditasi(${idMapping}"]`).length > 0;

        if (hasMatchingUploadButton || hasMatchingInputButton || hasMatchingDownloadButton) {
            // Cari kolom jadwal dan aksi dalam row ini (kolom terakhir)
            const $jadwalCol = $row.find('td').last();

            // Update HANYA tanggal mulai dengan targeting yang lebih spesifik
            $jadwalCol.find('div').each(function() {
                const $div = $(this);
                const divText = $div.text().trim();

                // Update tanggal mulai - cari div yang mengandung "Tanggal Visit Mulai:" SAJA
                if (divText.includes('Tanggal Visit Mulai:') && !divText.includes('Tanggal Visit Akhir:')) {
                    $div.html(`<strong>Tanggal Visit Mulai:</strong><br>${tglMulaiFormatted}`);
                    // Tambah efek visual
                    $div.addClass('text-updated');
                    setTimeout(() => $div.removeClass('text-updated'), 2000);
                }

                // Update tanggal akhir - cari div yang mengandung "Tanggal Visit Akhir:" SAJA
                if (divText.includes('Tanggal Visit Akhir:') && !divText.includes('Tanggal Visit Mulai:')) {
                    $div.html(`<strong>Tanggal Visit Akhir:</strong><br>${tglAkhirFormatted}`);
                    // Tambah efek visual
                    $div.addClass('text-updated');
                    setTimeout(() => $div.removeClass('text-updated'), 2000);
                }
            });

            // Update visibility tombol setelah tanggal diisi
            updateButtonVisibility($jadwalCol, idMapping);

            return false; // Break the loop
        }
    });
}

/**
 * Update visibility tombol berdasarkan status tanggal - HANYA untuk tombol visitasi
 */
function updateButtonVisibility($jadwalCol, idMapping) {
    // Hide HANYA tombol "Input Tanggal Visitasi" karena tanggal sudah diisi
    const $inputTanggalBtn = $jadwalCol.find(`button[onclick*="inputTanggalVisitasi(${idMapping}"]`);

    if ($inputTanggalBtn.length > 0) {
        // Hanya hide div yang mengandung tombol input tanggal, bukan yang lain
        $inputTanggalBtn.closest('div').fadeOut(500, function() {
            $(this).remove();
        });
    }

    // Show tombol "Download Surat Tugas Visitasi" karena tanggal sudah diisi
    // Cari div yang mengandung tombol download surat pra-akreditasi sebagai reference point
    const $praAkreditasiBtn = $jadwalCol.find(`button[onclick*="downloadSuratPraAkreditasi(${idMapping}"]`);

    // Cek apakah tombol download surat tugas sudah ada
    const $existingDownloadBtn = $jadwalCol.find(`button[onclick*="downloadSuratTugasVisitasi(${idMapping}"]`);

    if ($existingDownloadBtn.length === 0 && $praAkreditasiBtn.length > 0) {
        // Tambah tombol download surat tugas visitasi SETELAH tombol pra-akreditasi
        const downloadButtonHtml = `
            <div style="opacity: 0;">
                <button type="button" class="btn btn-info btn-xs" style="font-size: 10px; padding: 3px 6px;"
                        onclick="downloadSuratTugasVisitasi(${idMapping})">
                    <i class="fas fa-download"></i> Download Surat Tugas Visitasi
                </button>
            </div>
        `;

        // Insert setelah div yang mengandung tombol pra-akreditasi
        $praAkreditasiBtn.closest('div').after(downloadButtonHtml);

        // Animate show dengan fade in
        $praAkreditasiBtn.closest('div').next().animate({opacity: 1}, 500);
    }
}

/**
 * Reset upload form
 */
function resetUploadForm() {
    $('#form-upload')[0].reset();
    $('#btn-upload').prop('disabled', false).text('Upload');

    // Reset ke default (PDF only)
    $('#file-upload-label').text('Pilih File (PDF Only):');
    $('#file-upload-info').text('Format file yang diizinkan: PDF (maksimal 10MB)');
    $('#file-upload').attr('accept', '.pdf');
}

/**
 * Download surat dengan membuka file PHP di tab baru (mengikuti pattern PAUD)
 */
function downloadSurat(jenisSurat, idMapping) {
    let fileName = '';

    // Tentukan nama file berdasarkan jenis surat
    switch(jenisSurat) {
        case 'pra_akreditasi':
            fileName = 'mapping_2025_st_pra_akreditasi.php';
            break;
        case 'tugas_visitasi':
            fileName = 'mapping_2025_st_visitasi.php';
            break;
        default:
            showError('Jenis surat tidak valid');
            return;
    }

    // Buat URL dengan parameter 'id_mapping' sesuai dengan pattern PAUD
    const url = `${fileName}?id_mapping=${idMapping}`;

    // Buka file di tab baru
    window.open(url, '_blank');
}

/**
 * Download surat pra-akreditasi (mengikuti pattern PAUD)
 * @param {number} idMapping - ID mapping
 */
function downloadSuratPraAkreditasi(idMapping) {
    const suratPraAkreditasiUrl = 'mapping_2025_st_pra_akreditasi.php?id_mapping=' + idMapping;
    window.open(suratPraAkreditasiUrl, '_blank');
}

/**
 * Download surat tugas visitasi (mengikuti pattern PAUD)
 * @param {number} idMapping - ID mapping
 */
function downloadSuratTugasVisitasi(idMapping) {
    const suratTugasUrl = 'mapping_2025_st_visitasi.php?id_mapping=' + idMapping;
    window.open(suratTugasUrl, '_blank');
}

/**
 * Reset tanggal form
 */
function resetTanggalForm() {
    $('#form-tanggal')[0].reset();
    $('#btn-save-tanggal').prop('disabled', false).text('Simpan');
}

/**
 * Format file size untuk display
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Show loading overlay
 */
function showLoading() {
    if ($('#loading-overlay').length === 0) {
        $('body').append(`
            <div id="loading-overlay" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 9999;
                display: flex;
                justify-content: center;
                align-items: center;
            ">
                <div class="spinner-border text-light" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
            </div>
        `);
    }
}

/**
 * Hide loading overlay
 */
function hideLoading() {
    $('#loading-overlay').remove();
}

/**
 * Validate date input
 */
function validateDate(dateString) {
    const date = new Date(dateString);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    return date >= today;
}

/**
 * Format date untuk display Indonesia
 */
function formatDateIndonesia(dateString) {
    const date = new Date(dateString);
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        timeZone: 'Asia/Jakarta'
    };
    
    return date.toLocaleDateString('id-ID', options);
}

/**
 * Download file directly (untuk LK Penilaian 1 & 2)
 */
function downloadFile(filename) {
    // Buat URL download dengan parameter file
    const downloadUrl = `download_file.php?file=${encodeURIComponent(filename)}`;

    // Trigger download
    window.location.href = downloadUrl;
}

/**
 * Handle keyboard shortcuts
 */
$(document).on('keydown', function(e) {
    // ESC key to close modals
    if (e.keyCode === 27) {
        $('.modal').modal('hide');
    }

    // Ctrl+R to refresh (prevent default and show confirmation)
    if (e.ctrlKey && e.keyCode === 82) {
        e.preventDefault();
        if (confirm('Refresh halaman? Data yang belum disimpan akan hilang.')) {
            location.reload();
        }
    }
});

/**
 * CSS untuk efek visual update real-time
 */
$(document).ready(function() {
    // Tambahkan CSS untuk efek update
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .badge-updated {
                animation: pulse-success 2s ease-in-out;
                box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
            }

            .text-updated {
                animation: highlight-text 2s ease-in-out;
                background-color: rgba(40, 167, 69, 0.1);
                border-radius: 3px;
                padding: 2px 4px;
            }

            @keyframes pulse-success {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); box-shadow: 0 0 15px rgba(40, 167, 69, 0.7); }
                100% { transform: scale(1); }
            }

            @keyframes highlight-text {
                0% { background-color: rgba(40, 167, 69, 0.3); }
                50% { background-color: rgba(40, 167, 69, 0.1); }
                100% { background-color: transparent; }
            }

            .btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }

            /* Proteksi elemen yang tidak boleh hilang */
            .protected-element {
                position: relative;
            }

            .protected-element::before {
                content: '';
                position: absolute;
                top: -2px;
                left: -2px;
                right: -2px;
                bottom: -2px;
                pointer-events: none;
                z-index: -1;
            }
        `)
        .appendTo('head');
});
