<?php
/**
 * File preview handler untuk Mapping Visitasi Dasmen IASP 2025
 * Menampilkan file PDF dengan security check
 */

require_once '../../koneksi.php';
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');

// Set timezone
date_default_timezone_set('Asia/Singapore');

try {
    // Validasi parameter
    if (!isset($_GET['file']) || empty($_GET['file'])) {
        http_response_code(400);
        die('Parameter file tidak valid');
    }
    
    $filename = $_GET['file'];
    
    // Security: Validate filename (no path traversal)
    if (strpos($filename, '..') !== false || strpos($filename, '/') !== false || strpos($filename, '\\') !== false) {
        http_response_code(403);
        die('Invalid filename');
    }
    
    // Validasi extension
    $file_extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    if ($file_extension !== 'pdf') {
        http_response_code(400);
        die('File type not supported');
    }
    
    // Get session variables
    $kd_user = mysqli_real_escape_string($conn, $_SESSION['kd_user'] ?? '');
    $provinsi_id = mysqli_real_escape_string($conn, $_SESSION['provinsi_id'] ?? '');
    
    // Verify file belongs to current asesor dengan query yang lebih efisien
    $check_query = "SELECT m.id_mapping, m.kd_asesor1, m.kd_asesor2,
                           m.file_format_3_1_hasil_penilaian_pra_visitasi_1,
                           m.file_format_3_1_hasil_penilaian_pra_visitasi_2,
                           m.file_format_3_2_lk_penggalian_data_pra_visitasi_1,
                           m.file_format_3_2_lk_penggalian_data_pra_visitasi_2,
                           m.file_format_4_1_surat_tugas_visitasi,
                           m.file_format_4_2_pakta_integritas_1,
                           m.file_format_4_2_pakta_integritas_2,
                           m.file_lk_penilaian_1,
                           m.file_lk_penilaian_2,
                           m.file_format_4_4_berita_acara_visitasi,
                           m.file_format_4_5_laporan_individu_1,
                           m.file_format_4_5_laporan_individu_2,
                           m.file_format_4_5_laporan_kelompok,
                           m.file_format_4_5_catatan_dan_saran,
                           m.file_foto_visitasi_2025
                    FROM mapping_2025 m
                    WHERE (m.kd_asesor1 = '$kd_user' OR m.kd_asesor2 = '$kd_user')
                        AND m.provinsi_id = '$provinsi_id'
                        AND (
                            m.file_format_3_1_hasil_penilaian_pra_visitasi_1 = '$filename' OR
                            m.file_format_3_1_hasil_penilaian_pra_visitasi_2 = '$filename' OR
                            m.file_format_3_2_lk_penggalian_data_pra_visitasi_1 = '$filename' OR
                            m.file_format_3_2_lk_penggalian_data_pra_visitasi_2 = '$filename' OR
                            m.file_format_4_1_surat_tugas_visitasi = '$filename' OR
                            m.file_format_4_2_pakta_integritas_1 = '$filename' OR
                            m.file_format_4_2_pakta_integritas_2 = '$filename' OR
                            m.file_lk_penilaian_1 = '$filename' OR
                            m.file_lk_penilaian_2 = '$filename' OR
                            m.file_format_4_4_berita_acara_visitasi = '$filename' OR
                            m.file_format_4_5_laporan_individu_1 = '$filename' OR
                            m.file_format_4_5_laporan_individu_2 = '$filename' OR
                            m.file_format_4_5_laporan_kelompok = '$filename' OR
                            m.file_format_4_5_catatan_dan_saran = '$filename' OR
                            m.file_foto_visitasi_2025 = '$filename'
                        )";
    
    $check_result = mysqli_query($conn, $check_query);
    
    if (!$check_result || mysqli_num_rows($check_result) === 0) {
        http_response_code(403);
        die('Access denied: File not found or no permission');
    }
    
    $mapping_data = mysqli_fetch_assoc($check_result);
    
    // Determine file path berdasarkan field yang match
    $file_path = '';
    $file_directories = [
        'file_format_3_1_hasil_penilaian_pra_visitasi_1' => '../../../simak/files/upload_file_format_3_1_hasil_penilaian_pra_visitasi_1/',
        'file_format_3_1_hasil_penilaian_pra_visitasi_2' => '../../../simak/files/upload_file_format_3_1_hasil_penilaian_pra_visitasi_2/',
        'file_format_3_2_lk_penggalian_data_pra_visitasi_1' => '../../../simak/files/upload_file_format_3_2_lk_penggalian_data_pra_visitasi_1/',
        'file_format_3_2_lk_penggalian_data_pra_visitasi_2' => '../../../simak/files/upload_file_format_3_2_lk_penggalian_data_pra_visitasi_2/',
        'file_format_4_1_surat_tugas_visitasi' => '../../../simak/files/upload_file_format_4_1_surat_tugas_visitasi/',
        'file_format_4_2_pakta_integritas_1' => '../../../simak/files/upload_file_format_4_2_pakta_integritas_1/',
        'file_format_4_2_pakta_integritas_2' => '../../../simak/files/upload_file_format_4_2_pakta_integritas_2/',
        'file_lk_penilaian_1' => '../../../simak/files/upload_file_lk_penilaian_1/',
        'file_lk_penilaian_2' => '../../../simak/files/upload_file_lk_penilaian_2/',
        'file_format_4_4_berita_acara_visitasi' => '../../../simak/files/upload_file_format_4_4_berita_acara_visitasi/',
        'file_format_4_5_laporan_individu_1' => '../../../simak/files/upload_file_format_4_5_laporan_individu_1/',
        'file_format_4_5_laporan_individu_2' => '../../../simak/files/upload_file_format_4_5_laporan_individu_2/',
        'file_format_4_5_laporan_kelompok' => '../../../simak/files/upload_file_format_4_5_laporan_kelompok/',
        'file_format_4_5_catatan_dan_saran' => '../../../simak/files/upload_file_format_4_5_catatan_dan_saran/',
        'file_foto_visitasi_2025' => '../../../simak/files/upload_file_foto_visitasi_2025/'
    ];
    
    // Find which field contains the filename
    foreach ($file_directories as $field => $directory) {
        if ($mapping_data[$field] === $filename) {
            $file_path = $directory . $filename;
            break;
        }
    }
    
    if (empty($file_path)) {
        http_response_code(404);
        die('File path not determined');
    }
    
    // Check if file exists
    if (!file_exists($file_path)) {
        http_response_code(404);
        die('File not found on server');
    }
    
    // Check if file is readable
    if (!is_readable($file_path)) {
        http_response_code(403);
        die('File not readable');
    }
    
    // Get file info
    $file_size = filesize($file_path);
    $file_mime = 'application/pdf';
    
    // Set headers untuk PDF preview
    header('Content-Type: ' . $file_mime);
    header('Content-Length: ' . $file_size);
    header('Content-Disposition: inline; filename="' . basename($filename) . '"');
    header('Cache-Control: private, max-age=0, must-revalidate');
    header('Pragma: public');
    
    // Security headers
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: SAMEORIGIN');
    header('X-XSS-Protection: 1; mode=block');
    
    // Output file
    readfile($file_path);
    
} catch (Exception $e) {
    http_response_code(500);
    die('Internal server error: ' . $e->getMessage());
}
?>
