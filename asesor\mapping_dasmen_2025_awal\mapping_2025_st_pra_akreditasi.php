<?php
    require_once('../../koneksi.php');
    require_once('../../check_session.php');
    requireLevel('Asesor');
    require_once('../../library/format_tanggal/format_tgl_saja.php');
    require_once('../../library/dompdf/autoload.inc.php');
?>

<?php
    // Rererence the Dompdf namespace
    use Dompdf\Dompdf;

    // Instantiate dompdf class
    $dompdf = new dompdf();

?>

<?php
// Validasi parameter id_mapping (sesuai dengan pattern PAUD)
if(isset($_GET['id_mapping']) && !empty($_GET['id_mapping'])) {
    $id_mapping = mysqli_real_escape_string($conn, $_GET['id_mapping']);

    // Validasi bahwa id_mapping adalah numeric
    if (!is_numeric($id_mapping)) {
        die('Parameter tidak valid');
    }
} else {
    die('Parameter id_mapping tidak ditemukan');
}

// Lanjutkan proses jika parameter valid
        
$sqledt =  "SELECT mapping_2025.*, mapping_2025.sekolah_id, sekolah.npsn, sekolah.nama_sekolah, sekolah.alamat,
            sekolah.kecamatan, sekolah.nama_kepsek, sekolah.no_hp_kepsek, sekolah.no_wa_kepsek, jenjang.nm_jenjang,
            kab_kota.nm_kota, asesor_1.nia1, asesor_1.nm_asesor1, asesor_1.no_hp as hp1,
            (SELECT kab_kota.nm_kota from asesor_1 LEFT JOIN kab_kota ON asesor_1.kota_id1=kab_kota.kota_id
             WHERE mapping_2025.kd_asesor1=asesor_1.kd_asesor1) as kota1,
            asesor_2.nia2, asesor_2.nm_asesor2, asesor_2.no_hp as hp2,
            (SELECT kab_kota.nm_kota from asesor_2 LEFT JOIN kab_kota ON asesor_2.kota_id2=kab_kota.kota_id
            WHERE mapping_2025.kd_asesor2=asesor_2.kd_asesor2) as kota2 FROM mapping_2025
            LEFT JOIN sekolah ON mapping_2025.sekolah_id=sekolah.sekolah_id
            LEFT JOIN jenjang ON sekolah.jenjang_id=jenjang.jenjang_id
            LEFT JOIN kab_kota ON sekolah.kota_id=kab_kota.kota_id
            LEFT JOIN asesor_1 ON mapping_2025.kd_asesor1=asesor_1.kd_asesor1
             LEFT JOIN asesor_2 ON mapping_2025.kd_asesor2=asesor_2.kd_asesor2
            WHERE mapping_2025.id_mapping = '$id_mapping'
                AND mapping_2025.provinsi_id = '".$_SESSION['provinsi_id']."'
                AND (mapping_2025.kd_asesor1 = '".$_SESSION['kd_user']."' OR mapping_2025.kd_asesor2 = '".$_SESSION['kd_user']."')";
$result = $conn->query($sqledt);
if($result->num_rows > 0) {
while($row = $result->fetch_assoc()) {
    
    $npsn = $row['npsn'];
    $sekolah = $row['nama_sekolah'];

    $sqlprov = " SELECT provinsi.*, kab_kota.nm_kota as kota_kantor FROM provinsi
                 LEFT JOIN kab_kota ON provinsi.kota_id=kab_kota.kota_id
                 WHERE provinsi.provinsi_id='".$_SESSION['provinsi_id']."' ";
    $hasil = $conn->query($sqlprov);
    if($hasil->num_rows > 0) {
    while($baris = $hasil->fetch_assoc()) {

        $nama_provinsi   = $baris['nama_provinsi'];
        $alamat          = $baris['alamat_provinsi'];
        $kota_kantor     = $baris['kota_kantor'];
        $alamat          = $baris['alamat_provinsi'];
        $nama_ketua_banp = $baris['nama_ketua_banp'];
        $ttd_ketua_banp  = $baris['ttd_ketua_banp'];


    // batas atas load html ke pdf
    $dompdf->load_html('

<html>
<style type="text/css">
    html{margin:10px 80px;}
</style>
<body>

<table width="100%" border="0" cellspacing="0" cellpadding="0" align="center" style="font-family:Arial, Helvetica, sans-serif">
  <tr>
    <td width="10%"><img src="../../logo ban-baru.png" width="80" height="80"></td>
    <td width="1%">&nbsp;&nbsp;</td>
    <td valign="top" align="left" width="50%">
        <b style="font-size:24px; color: #4682B4;">BADAN AKREDITASI NASIONAL</b><br>
        <b style="font-size:12px; color: #4682B4;">PENDIDIKAN ANAK USIA DINI, PENDIDIKAN DASAR, DAN PENDIDIKAN MENENGAH</b><br>
        <b style="font-size:24px; color: #4682B4;">PROVINSI '.strtoupper($nama_provinsi).'</b> <br>
        <a style="font-size:11px;">'.$alamat.'</a>
    </td>
  </tr>
  <tr>
    <td colspan="3"><b><hr width="100%"></b></td>
  </tr>
  <tr>
    <td colspan="3">&nbsp;</td>
  </tr>
</table>

<table width="100%" border="0" cellspacing="0" cellpadding="0" align="center" style="font-family:Arial, Helvetica, sans-serif">
    <tr>
      <td align="center" style="font-size:16px"><b>SURAT TUGAS PRA-VISITASI</b></td>
    </tr>
    <tr>
      <td align="center" style="font-size:16px"><b>Nomor : '.$row["no_surat_tugas_pra_visitasi"].' </b></td>
    </tr>
</table>

<table width="90%" border="0" cellspacing="0" cellpadding="0" align="center" style="font-family:Arial, Helvetica, sans-serif; font-size:14px">
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr>
        <td colspan="5">Badan Akreditasi Nasional Pendidikan Anak Usia Dini, Pendidikan Dasar dan Pendidikan Menengah (BAN PDM) Provinsi '.$nama_provinsi.' menugaskan kepada :</td>
    </tr>
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr>
        <td width="1%">&nbsp;</td>
        <td width="10%">Nama</td>
        <td width="1%">:&nbsp;</td>
        <td colspan="2">1.&nbsp;'.$row["nm_asesor1"].'</td>
    </tr>
    <tr>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td colspan="2">2.&nbsp;'.$row["nm_asesor2"].'</td>
    </tr>
    <tr>
        <td>&nbsp;</td>
        <td>Jabatan</td>
        <td>:&nbsp;</td>
        <td colspan="2">Asesor</td>
    </tr>
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr>
        <td colspan="5">Untuk melaksanakan pra-visitasi sekolah/madrasah/program pendidikan kesetaraan melalui Sispena pada :</td>
    </tr>
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr>
        <td>&nbsp;</td>
        <td>Nama</td>
        <td>:&nbsp;</td>
        <td colspan="2">'.$row["nama_sekolah"].' </td>
    </tr>
    <tr>
        <td>&nbsp;</td>
        <td>NPSN</td>
        <td>:&nbsp;</td>
        <td colspan="2">'.$row["npsn"].' </td>
    </tr>
    <tr>
        <td>&nbsp;</td>
        <td>Alamat</td>
        <td>:&nbsp;</td>
        <td colspan="2">'.$row["alamat"].' </td>
    </tr>
    <tr>
        <td>&nbsp;</td>
        <td>Kecamatan</td>
        <td>:&nbsp;</td>
        <td colspan="2">'.$row["kecamatan"].' </td>
    </tr>
    <tr>
        <td>&nbsp;</td>
        <td>Kabupaten/Kota</td>
        <td>:&nbsp;</td>
        <td colspan="2">'.$row["nm_kota"].' </td>
    </tr>
    <tr>
        <td>&nbsp;</td>
        <td>Waktu Pelaksanaan</td>
        <td>:&nbsp;</td>
        <td colspan="2">Tanggal '.format_tgl_saja($row["tgl_pra_visitasi"]).' </td>
    </tr>
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr>
        <td colspan="5">Masing-masing asesor melaksanakan tugas sebagai berikut :</td>
    </tr>
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr>
        <td colspan="5">
            <table width="100%">
                <tr valign="top">
                    <td width="6%">&nbsp;</td>
                    <td width="2%">1.&nbsp;</td>
                    <td width="92%">
                        Melakukan pemeriksaan dan telaah dokumentasi dan deskripsi kinerja asesi melalui aplikasi Sispena.
                    </td>
                </tr>
                <tr valign="top">
                    <td>&nbsp;</td>
                    <td>2.&nbsp;</td>
                    <td>
                        Melaporkan hasil kegiatan pra-visitasi kepada Ketua BAN-PDM Provinsi '.$nama_provinsi.'..
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr>
        <td colspan="5">Surat Tugas ini diberikan untuk dilaksanakan sebagaimana mestinya.</td>
    </tr>
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr>
        <td colspan="5">
            <table width="100%">
                <tr>
                    <td width="45%">&nbsp;</td>
                    <td width="55%" align="left">
                        <div>'.$kota_kantor.', '.format_tgl_saja($row["tgl_surat_tugas_pra_visitasi"]).' <br><br></div></td>
                </tr>
                <tr>
                    <td width="45%">&nbsp;</td>
                    <td width="55%" align="left">Ketua Badan Akreditasi Nasional</td>
                </tr>
                <tr>
                    <td width="45%">&nbsp;</td>
                    <td width="55%" align="left">Pendidikan Anak Usia Dini, Pendidikan Dasar,</td>
                </tr>
                <tr>
                    <td width="45%">&nbsp;</td>
                    <td width="55%" align="left">dan Pendidikan Menengah</td>
                </tr>
                <tr>
                    <td width="45%">&nbsp;</td>
                    <td width="55%" align="left">Provinsi '.$nama_provinsi.'.</td>
                </tr>
                <tr>
                    <td width="45%">&nbsp;</td>
                    <td width="55%" align="left">&nbsp;</td>
                </tr>
                <tr>
                    <td width="45%">&nbsp;</td>
                    <td width="55%" align="left">&nbsp;</td>
                </tr>
                <tr>
                    <td width="45%">&nbsp;</td>
                    <td width="55%" align="left">&nbsp;</td>
                </tr>
                    <img src="../../../simak/files/ttd_ketua_stempel/'.$ttd_ketua_banp.'" height="150" width="285" style="z-index:-2; position:absolute; left:220px; top:70%" >
                <tr>
                    <td width="45%">&nbsp;</td>
                    <td width="55%" align="left">&nbsp;</td>
                </tr>
                <tr>
                    <td width="45%">&nbsp;</td>
                    <td width="55%" align="left"><b>'.$nama_ketua_banp.'</b></td>
                </tr>
            </table>
        </td>
    </tr>
</table>
</body></html>;
    ');
    // batas bawah load html ke pdf

    
    // setup paper size
    $dompdf->setPaper('A4','portrait');
    
    // Render the HTML as PDF
    $dompdf->render();
    
    // Output the generate PDF
    $dompdf->stream($npsn .' ' .$sekolah, array("Attachment"=>0));

    } // End while($baris)
    } // End if($hasil->num_rows > 0)
} // End while($row)
} else {
    die('Data mapping tidak ditemukan atau Anda tidak memiliki akses');
} // End if($result->num_rows > 0)
?>