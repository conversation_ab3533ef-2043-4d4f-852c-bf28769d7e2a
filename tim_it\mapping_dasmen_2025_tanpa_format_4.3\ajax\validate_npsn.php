<?php
/**
 * AJAX handler untuk validasi NPSN sekolah
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $npsn = trim($_POST['npsn'] ?? '');
    
    if (empty($npsn)) {
        echo json_encode([
            'success' => false,
            'message' => 'NPSN tidak boleh kosong'
        ]);
        exit;
    }
    
    // Escape input dan ambil provinsi_id dari session
    $npsn = $conn->real_escape_string($npsn);
    $provinsi_id = intval($_SESSION['provinsi_id']);

    // Query untuk validasi NPSN dengan filter provinsi dan rumpun dasmen
    $query = "SELECT
                s.sekolah_id,
                s.npsn,
                s.nama_sekolah,
                j.nm_jenjang,
                k.nm_kota
              FROM sekolah s
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
              WHERE s.npsn = '$npsn'
                AND s.provinsi_id = $provinsi_id
                AND s.rumpun = 'dasmen'";

    $result = $conn->query($query);
    
    if ($result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'NPSN tidak ditemukan atau bukan sekolah dasmen di provinsi ini'
        ]);
        exit;
    }
    
    $sekolah = $result->fetch_assoc();
    
    // Cek apakah sekolah sudah ada di mapping untuk tahun yang sama
    $sekolah_id = intval($sekolah['sekolah_id']);
    $check_query = "SELECT id_mapping FROM mapping_2025
                   WHERE sekolah_id = $sekolah_id AND provinsi_id = $provinsi_id";
    $check_result = $conn->query($check_query);

    if (!$check_result) {
        echo json_encode([
            'success' => false,
            'message' => 'Error checking mapping'
        ]);
        exit;
    }

    if ($check_result->num_rows > 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Sekolah ini sudah ada dalam mapping visitasi'
        ]);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'sekolah_id' => $sekolah['sekolah_id'],
            'npsn' => $sekolah['npsn'],
            'nama_sekolah' => $sekolah['nama_sekolah'],
            'jenjang' => $sekolah['nm_jenjang'],
            'kota' => $sekolah['nm_kota']
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Validate NPSN Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan saat validasi NPSN'
    ]);
}

$conn->close();
?>
