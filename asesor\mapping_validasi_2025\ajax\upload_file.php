<?php
require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Asesor', '../../../login.php');

// Set response header
header('Content-Type: application/json');

// Initialize response
$response = array(
    'success' => false,
    'message' => '',
    'data' => array()
);

try {
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }
    
    // Get session variables
    $kd_user = $_SESSION['kd_user'];
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Validate required parameters
    if (!isset($_POST['id_mapping']) || !isset($_POST['file_type'])) {
        throw new Exception('Parameter tidak lengkap');
    }
    
    $id_mapping = mysqli_real_escape_string($conn, $_POST['id_mapping']);
    $file_type = mysqli_real_escape_string($conn, $_POST['file_type']);
    
    // Validate file upload
    if (!isset($_FILES['file_validasi']) || $_FILES['file_validasi']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File tidak ditemukan atau terjadi error saat upload');
    }
    
    $file = $_FILES['file_validasi'];
    
    // Validate file type (PDF only) - Alternative method tanpa finfo
    $allowed_extensions = array('pdf');
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    if (!in_array($file_extension, $allowed_extensions)) {
        throw new Exception('Hanya file PDF yang diizinkan');
    }

    // Additional validation: Check file signature (PDF magic bytes)
    $handle = fopen($file['tmp_name'], 'r');
    $first_bytes = fread($handle, 4);
    fclose($handle);

    if (substr($first_bytes, 0, 4) !== '%PDF') {
        throw new Exception('File bukan format PDF yang valid');
    }
    
    // Validate file size (max 10MB)
    $max_size = 10 * 1024 * 1024; // 10MB in bytes
    if ($file['size'] > $max_size) {
        throw new Exception('Ukuran file maksimal 10MB');
    }
    
    // Verify user access to this mapping
    $access_check = "SELECT mv.id_mapping, mv.kd_asesor1, mv.kd_asesor2 
                     FROM mapping_validasi_2025 mv 
                     WHERE mv.id_mapping = '$id_mapping' 
                       AND (mv.kd_asesor1 = '$kd_user' OR mv.kd_asesor2 = '$kd_user')
                       AND mv.provinsi_id = '$provinsi_id'";
    
    $access_result = $conn->query($access_check);
    if ($access_result->num_rows === 0) {
        throw new Exception('Anda tidak memiliki akses untuk mapping ini');
    }
    
    $access_data = $access_result->fetch_assoc();
    
    // Validate file type permission based on user role
    if ($file_type === 'format_5_1_berita_acara_hasil_validasi_1' && $kd_user !== $access_data['kd_asesor1']) {
        throw new Exception('Anda tidak memiliki akses untuk upload file ini');
    }
    
    if ($file_type === 'format_5_1_berita_acara_hasil_validasi_2' && $kd_user !== $access_data['kd_asesor2']) {
        throw new Exception('Anda tidak memiliki akses untuk upload file ini');
    }
    
    // Determine upload directory based on file type
    $upload_directories = array(
        'format_5_1_berita_acara_hasil_validasi_1' => '../../../../simak/files/upload_file_format_5_1_berita_acara_hasil_validasi_1/',
        'format_5_1_berita_acara_hasil_validasi_2' => '../../../../simak/files/upload_file_format_5_1_berita_acara_hasil_validasi_2/'
    );
    
    if (!isset($upload_directories[$file_type])) {
        throw new Exception('Tipe file tidak valid');
    }
    
    $upload_dir = $upload_directories[$file_type];

    // Directory validation - mengikuti pola KPA PAUD
    if (!is_dir($upload_dir) || !is_writable($upload_dir)) {
        // Try to create directory if not exists
        if (!is_dir($upload_dir)) {
            if (!mkdir($upload_dir, 0755, true)) {
                throw new Exception('Gagal membuat direktori upload: ' . $upload_dir);
            }
        }

        if (!is_writable($upload_dir)) {
            throw new Exception('Direktori upload tidak dapat ditulis: ' . $upload_dir);
        }
    }
    
    // Generate nama file baru - mengikuti pola KPA PAUD
    $temp = explode('.', $file['name']);
    $filename = round(microtime(true)) . '.' . end($temp);
    $filename_escaped = mysqli_real_escape_string($conn, $filename);
    $upload_path = $upload_dir . $filename;
    
    // Upload file baru - mengikuti pola KPA PAUD
    if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
        throw new Exception('Gagal mengupload file ke server');
    }
    
    // Update database
    $field_mapping = array(
        'format_5_1_berita_acara_hasil_validasi_1' => 'file_format_5_1_berita_acara_hasil_validasi_1',
        'format_5_1_berita_acara_hasil_validasi_2' => 'file_format_5_1_berita_acara_hasil_validasi_2'
    );
    
    $db_field = $field_mapping[$file_type];
    
    // Hapus file lama jika ada - mengikuti pola KPA PAUD
    $old_file_query = "SELECT $db_field FROM mapping_validasi_2025 WHERE id_mapping = '$id_mapping'";
    $old_file_result = $conn->query($old_file_query);
    if ($old_file_result->num_rows > 0) {
        $old_file_data = $old_file_result->fetch_assoc();
        $old_filename = $old_file_data[$db_field];
        if (!empty($old_filename)) {
            $old_file_path = $upload_dir . $old_filename;
            if (file_exists($old_file_path)) {
                unlink($old_file_path);
            }
        }
    }
    
    // Update database with new filename - menggunakan escaped filename
    $update_sql = "UPDATE mapping_validasi_2025
                   SET $db_field = '$filename_escaped'
                   WHERE id_mapping = '$id_mapping'";

    if (!$conn->query($update_sql)) {
        // If database update fails, delete uploaded file
        unlink($upload_path);
        throw new Exception('Gagal menyimpan data ke database: ' . $conn->error);
    }
    
    // Success response - mengikuti pola KPA PAUD
    $response['success'] = true;
    $response['message'] = 'File berhasil di-upload';
    $response['data'] = array(
        'id_mapping' => $id_mapping,
        'filename' => $filename,
        'file_type' => $file_type,
        'upload_date' => date('Y-m-d'),
        'upload_time' => date('H:i:s')
    );
    
} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = $e->getMessage();
}

// Return JSON response
echo json_encode($response);
?>
