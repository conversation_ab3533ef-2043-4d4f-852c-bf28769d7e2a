<?php
/**
 * AJAX handler untuk validasi asesor berdasarkan NIA
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $nia = trim($_POST['nia'] ?? '');
    $asesor_type = intval($_POST['asesor_type'] ?? 0);
    
    if (empty($nia)) {
        echo json_encode([
            'success' => false,
            'message' => 'NIA tidak boleh kosong'
        ]);
        exit;
    }
    
    if (!in_array($asesor_type, [1, 2])) {
        echo json_encode([
            'success' => false,
            'message' => 'Tipe asesor tidak valid'
        ]);
        exit;
    }
    
    // Ambil provinsi_id dari session
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Tentukan table dan field berdasarkan tipe asesor
    if ($asesor_type === 1) {
        $table = 'asesor_1';
        $nia_field = 'nia1';
        $kd_field = 'kd_asesor1';
        $nama_field = 'nm_asesor1';
        $kota_field = 'kota_id1';
    } else {
        $table = 'asesor_2';
        $nia_field = 'nia2';
        $kd_field = 'kd_asesor2';
        $nama_field = 'nm_asesor2';
        $kota_field = 'kota_id2';
    }
    
    // Escape input untuk keamanan
    $nia = $conn->real_escape_string($nia);
    $provinsi_id = intval($provinsi_id);

    // Query untuk validasi asesor dengan filter provinsi (tanpa filter rumpun)
    $query = "SELECT
                a.$kd_field as kd_asesor,
                a.$nia_field as nia,
                a.$nama_field as nama,
                a.$kota_field as kota_id,
                k.nm_kota
              FROM $table a
              LEFT JOIN kab_kota k ON a.$kota_field = k.kota_id
              WHERE a.$nia_field = '$nia'
                AND a.provinsi_id = $provinsi_id
                AND a.status_keaktifan_id = '1'
                AND a.soft_delete = '1'";

    $result = $conn->query($query);
    
    if ($result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'NIA tidak ditemukan atau asesor tidak aktif untuk rumpun kesetaraan'
        ]);
        exit;
    }
    
    $asesor = $result->fetch_assoc();
    
    echo json_encode([
        'success' => true,
        'data' => [
            'kd_asesor' => $asesor['kd_asesor'],
            'nia' => $asesor['nia'],
            'nama' => $asesor['nama'],
            'kota' => $asesor['nm_kota'] ?? 'Tidak diketahui'
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Validate Asesor Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan saat validasi asesor'
    ]);
}

$conn->close();
?>
