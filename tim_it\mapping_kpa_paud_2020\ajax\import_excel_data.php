<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Check if data was sent
    if (!isset($_POST['excel_data']) || empty($_POST['excel_data'])) {
        throw new Exception('Data Excel tidak ditemukan');
    }
    
    // Parse JSON data
    $rows = json_decode($_POST['excel_data'], true);
    
    if (!is_array($rows)) {
        throw new Exception('Format data tidak valid');
    }
    
    if (empty($rows)) {
        throw new Exception('Tidak ada data untuk diimport');
    }
    
    // Check max rows (100)
    if (count($rows) > 100) {
        throw new Exception('Maksimal 100 baris data. Data Anda memiliki ' . count($rows) . ' baris');
    }
    
    // Get session data
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Process and Insert
    $successCount = 0;
    $errorCount = 0;
    $errors = [];
    
    // Begin transaction
    $conn->autocommit(false);
    
    foreach ($rows as $index => $row) {
        $rowNumber = $index + 2; // +2 because we removed header and array is 0-based
        
        try {
            // Extract data from row
            $npsn = trim($row[0] ?? '');
            $nia = trim($row[1] ?? '');
            $tgl_penetapan = trim($row[2] ?? '');
            $tahap = trim($row[3] ?? '');
            $tahun_akreditasi = trim($row[4] ?? '');
            
            // Validate required fields
            if (empty($npsn) || empty($nia) || empty($tahap) || empty($tahun_akreditasi)) {
                throw new Exception("Baris $rowNumber: Data tidak lengkap");
            }
            
            // Validate tahun format
            if (!preg_match('/^\d{4}$/', $tahun_akreditasi)) {
                throw new Exception("Baris $rowNumber: Format tahun tidak valid");
            }
            
            // Validate tahap
            if (!in_array($tahap, ['1', '2', '3'])) {
                throw new Exception("Baris $rowNumber: Tahap harus 1, 2, atau 3");
            }
            
            // Validate date format if provided
            if (!empty($tgl_penetapan)) {
                $date = DateTime::createFromFormat('Y-m-d', $tgl_penetapan);
                if (!$date || $date->format('Y-m-d') !== $tgl_penetapan) {
                    throw new Exception("Baris $rowNumber: Format tanggal tidak valid (gunakan yyyy-mm-dd)");
                }
            } else {
                $tgl_penetapan = '0000-00-00';
            }
            
            // Lookup NPSN to get sekolah_id
            $sekolah_query = "SELECT sekolah_id FROM sekolah 
                             WHERE npsn = ? AND provinsi_id = ? AND rumpun = 'paud' 
                             AND soft_delete = '1' AND status_keaktifan_id = '1'";
            $sekolah_stmt = $conn->prepare($sekolah_query);
            $sekolah_stmt->bind_param("si", $npsn, $provinsi_id);
            $sekolah_stmt->execute();
            $sekolah_result = $sekolah_stmt->get_result();
            
            if ($sekolah_result->num_rows === 0) {
                throw new Exception("Baris $rowNumber: NPSN $npsn tidak ditemukan");
            }
            
            $sekolah_data = $sekolah_result->fetch_assoc();
            $sekolah_id = $sekolah_data['sekolah_id'];
            
            // Lookup NIA to get kd_asesor
            $asesor_query = "SELECT kd_asesor FROM asesor 
                            WHERE nia = ? AND provinsi_id = ? 
                            AND soft_delete = '1' AND status_keaktifan_id = '1'";
            $asesor_stmt = $conn->prepare($asesor_query);
            $asesor_stmt->bind_param("si", $nia, $provinsi_id);
            $asesor_stmt->execute();
            $asesor_result = $asesor_stmt->get_result();
            
            if ($asesor_result->num_rows === 0) {
                throw new Exception("Baris $rowNumber: NIA $nia tidak ditemukan");
            }
            
            $asesor_data = $asesor_result->fetch_assoc();
            $kd_asesor = $asesor_data['kd_asesor'];
            
            // Check for duplicate (same sekolah_id and tahun_akreditasi)
            $duplicate_query = "SELECT COUNT(*) as count FROM mapping_paud_kpa 
                               WHERE sekolah_id = ? AND tahun_akreditasi = ? AND provinsi_id = ?";
            $duplicate_stmt = $conn->prepare($duplicate_query);
            $duplicate_stmt->bind_param("isi", $sekolah_id, $tahun_akreditasi, $provinsi_id);
            $duplicate_stmt->execute();
            $duplicate_result = $duplicate_stmt->get_result();
            $duplicate_data = $duplicate_result->fetch_assoc();
            
            if ($duplicate_data['count'] > 0) {
                // Skip duplicate
                continue;
            }
            
            // Insert data
            $insert_query = "INSERT INTO mapping_paud_kpa 
                           (sekolah_id, kd_asesor, tgl_penetapan_kpa, tahap, tahun_akreditasi, provinsi_id) 
                           VALUES (?, ?, ?, ?, ?, ?)";
            $insert_stmt = $conn->prepare($insert_query);
            $insert_stmt->bind_param("issssi", $sekolah_id, $kd_asesor, $tgl_penetapan, $tahap, $tahun_akreditasi, $provinsi_id);
            
            if ($insert_stmt->execute()) {
                $successCount++;
            } else {
                throw new Exception("Baris $rowNumber: Gagal menyimpan data");
            }
            
        } catch (Exception $e) {
            $errorCount++;
            $errors[] = $e->getMessage();
        }
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Success response
    $response = [
        'success' => true,
        'message' => 'Import selesai',
        'data' => [
            'success_count' => $successCount,
            'error_count' => $errorCount,
            'errors' => $errors,
            'total_processed' => count($rows)
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
