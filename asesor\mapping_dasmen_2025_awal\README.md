# Mapping Visitasi Dasmen & Kesetaraan IASP 2025

## 📋 Overview

Modul **Mapping Visitasi Dasmen & Kesetaraan IASP 2025** adalah sistem manajemen dokumen visitasi untuk akreditasi sekolah Dasmen (Pendidikan Dasar da<PERSON>) dan <PERSON> tahun 2025. Modul ini memungkinkan asesor untuk mengelola 15 jenis dokumen visitasi dengan role-based access control yang ketat.

## 🏗️ Struktur File

```
mapping_dasmen_2025/
├── mapping.php              # File utama dengan tabel 5 kolom
├── preview_file.php         # Handler preview file dengan security
├── ajax/
│   ├── upload_file.php      # Handler upload file dengan validation
│   └── save_tanggal.php     # Handler save tanggal visitasi
├── js/
│   └── mapping_visitasi.js  # JavaScript functionality
└── README.md               # Dokumentasi ini
```

## 🎯 Fitur Utama

### 1. **Role-Based Access Control**
- **Asesor 1 (Ke<PERSON><PERSON> Tim)**: Aks<PERSON> ke 11 jenis file + input tanggal
- **Asesor 2 (Anggot<PERSON> Tim)**: Aks<PERSON> ke 4 jenis file saja

### 2. **15 Jenis File Upload**

#### **Kategori Pra Visitasi (4 files):**
1. File Format 3.1 Hasil Penilaian Pra-Visitasi 1 (Asesor 1)
2. File Format 3.1 Hasil Penilaian Pra-Visitasi 2 (Asesor 2)
3. File Format 3.2 LK Penggalian Data Pra-Visitasi 1 (Asesor 1)
4. File Format 3.2 LK Penggalian Data Pra-Visitasi 2 (Asesor 2)

#### **Kategori Visitasi (11 files):**
5. File Format 4.1 Surat Tugas Visitasi (Asesor 1)
6. File Format 4.2 Pakta Integritas 1 (Asesor 1)
7. File Format 4.2 Pakta Integritas 2 (Asesor 2)
8. File File Lembar Kerja Penilaian 1 (Asesor 1)
9. File File Lembar Kerja Penilaian 2 (Asesor 2)
10. File Format 4.4 Berita Acara Visitasi (Asesor 1)
11. File Format 4.5 Laporan Individu 1 (Asesor 1)
12. File Format 4.5 Laporan Individu 2 (Asesor 2)
13. File Format 4.5 Laporan Kelompok (Asesor 1)
14. File Format 4.5 Catatan Dan Saran (Asesor 1)
15. File Foto Visitasi (Asesor 1)

### 3. **Tabel 5 Kolom**
1. **NO** - Auto increment
2. **SEKOLAH** - Data lengkap sekolah (NPSN, Nama, Jenjang, Kab/Kota, Kepsek, dll)
3. **ASESOR VISITASI** - Data Asesor 1 & 2 (NIA, Nama, Kab/Kota, No HP)
4. **FORM UPLOAD DOKUMEN** - Tombol upload conditional berdasarkan role
5. **DOKUMEN UNGGAHAN** - Status upload dengan badge clickable untuk preview
6. **JADWAL DAN AKSI** - Tanggal visitasi dan tombol download surat

### 4. **File Management**
- **Format**: PDF only (maksimal 10MB)
- **Security**: Path traversal prevention, access control
- **Preview**: Clickable badge untuk preview file
- **Upload**: Real-time update tanpa reload browser

### 5. **Tanggal Management**
- Input tanggal mulai dan akhir visitasi (Asesor 1 only)
- Validation: tanggal mulai ≤ tanggal akhir
- Auto-suggestion: tanggal akhir = tanggal mulai + 1 hari
- Smart conditional display berdasarkan status tanggal
- Dynamic button visibility (Input/Download)

### 6. **Smart Conditional Display**
- **Empty Date Display**: "Tanggal visitasi belum diisi oleh Asesor" untuk tanggal kosong (0000-00-00)
- **Button Logic**:
  - Tombol "Input Tanggal" hilang setelah tanggal diisi
  - Tombol "Download Surat Tugas" muncul setelah tanggal diisi
- **Protected Elements**: Tanggal Pra-Akreditasi dan tombol Download Pra-Akreditasi tidak terpengaruh
- **Real-time Updates**: Visibility berubah langsung setelah save tanggal dengan animasi

### 7. **Document Generation**
- **Surat Pra-Akreditasi**: `mapping_2025_st_pra_akreditasi.php` (selalu tersedia)
- **Surat Tugas Visitasi**: `mapping_2025_st_visitasi.php` (muncul setelah tanggal diisi)
- **PDF Generation**: Menggunakan DomPDF untuk generate surat
- **New Tab Opening**: Surat dibuka di tab baru untuk kemudahan akses
- **Parameter Standard**: Menggunakan `id_mapping` sesuai pattern PAUD
- **Clean Architecture**: Direct file approach, no redundant handlers

## 🗄️ Database Schema

### Tabel Utama: `mapping_2025`
```sql
- id_mapping (int) - Primary key
- sekolah_id (int) - Foreign key ke tabel sekolah
- kd_asesor1, kd_asesor2 (varchar) - Kode asesor
- tgl_pra_visitasi, tgl_mulai_visitasi, tgl_akhir_visitasi (date)
- file_format_3_1_hasil_penilaian_pra_visitasi_1 (varchar)
- file_format_3_1_hasil_penilaian_pra_visitasi_2 (varchar)
- ... (13 field file lainnya)
- provinsi_id (int) - Filter berdasarkan provinsi
```

### Query Optimization
Menggunakan **LEFT JOIN** untuk performa optimal:
```sql
FROM mapping_2025 m
LEFT JOIN sekolah s ON m.sekolah_id = s.sekolah_id
LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
LEFT JOIN asesor_1 a1 ON m.kd_asesor1 = a1.kd_asesor1
LEFT JOIN asesor_2 a2 ON m.kd_asesor2 = a2.kd_asesor2
```

## 🔒 Security Features

### 1. **Authentication & Authorization**
- Session-based authentication
- Role-based access control (Asesor level only)
- Ownership validation untuk setiap operasi

### 2. **File Upload Security**
- Extension validation (PDF only)
- File size limit (10MB)
- Path traversal prevention
- Unique filename generation

### 3. **Database Security**
- Prepared statements untuk semua query
- Input sanitization dengan `mysqli_real_escape_string`
- SQL injection prevention

### 4. **File Preview Security**
- Access control berdasarkan ownership
- Filename validation (no ../, /, \)
- MIME type validation

## 🎨 UI Components

### 1. **Responsive Design**
- Mobile-friendly dengan AdminLTE 3
- Readable table design (font 13px untuk data, 12px untuk status, 10px untuk tombol)
- Consistent alignment (left-aligned, top-aligned untuk readability optimal)
- Horizontal scroll untuk tabel besar

### 2. **Interactive Elements**
- Modal upload dengan instant feedback
- Color-coded status badges dengan real-time update
- Real-time file preview
- AJAX form submissions tanpa reload

### 3. **Visual Indicators**
- **Green Badge**: "Sudah Upload" (clickable)
- **Red Badge**: "Belum Upload"
- **Color-coded Buttons**: Berbeda per jenis file

## 📱 JavaScript Functionality

### 1. **Upload Management**
```javascript
- File validation (PDF, 10MB max)
- Real-time badge update tanpa reload (spesifik per file)
- Error handling & user feedback
- Instant visual feedback dengan animasi
- Unique badge targeting dengan ID system
```

### 2. **Date Management**
```javascript
- Date validation (tidak boleh masa lalu)
- Auto-suggestion tanggal akhir
- Form validation sebelum submit
- Smart conditional display berdasarkan status
- Dynamic button show/hide dengan animasi
```

### 3. **User Experience**
```javascript
- Real-time updates tanpa reload
- Keyboard shortcuts (ESC, Ctrl+R)
- Visual feedback dengan animasi
- Minimal notification system (error only)
- Document download di tab baru
- Production ready - clean code tanpa debugging
```

## 🚀 Installation & Setup

### 1. **File Placement**
```bash
# Copy files ke direktori asesor
cp -r mapping_dasmen_2025/ /path/to/asesor/
```

### 2. **Directory Permissions**
```bash
# Pastikan direktori upload writable
chmod 755 /path/to/simak/files/upload_file_*
```

### 3. **Database Setup**
- Pastikan tabel `mapping_2025` sudah ada
- Pastikan relasi dengan tabel `sekolah`, `asesor_1`, `asesor_2` sudah benar

### 4. **Navigation Menu**
Menu sudah otomatis tersedia di sidebar:
```
Data Mapping Dasmen > Mapping Visitasi
```

## 🧪 Testing Guide

### 1. **Role Testing**
- Login sebagai Asesor 1: Cek akses 11 files + tanggal
- Login sebagai Asesor 2: Cek akses 4 files only

### 2. **Upload Testing**
- Test upload PDF valid
- Test upload non-PDF (harus ditolak)
- Test upload file > 10MB (harus ditolak)

### 3. **Security Testing**
- Test akses file milik asesor lain (harus ditolak)
- Test path traversal di preview (harus ditolak)
- Test SQL injection di form (harus aman)

### 4. **Date Testing**
- Test input tanggal masa lalu (harus ditolak)
- Test tanggal mulai > tanggal akhir (harus ditolak)

## 📊 Performance Optimization

### 1. **Database**
- Optimized JOIN queries
- Indexed fields untuk filtering
- Prepared statements

### 2. **File Handling**
- Efficient file operations
- Old file cleanup
- Memory management

### 3. **Frontend**
- Minified JavaScript
- Compressed images
- Cached resources

## 🔧 Maintenance

### 1. **Regular Tasks**
- Monitor upload directory size
- Clean up orphaned files
- Check database performance

### 2. **Backup Strategy**
- Daily database backup
- Weekly file backup
- Monthly full system backup

## 📞 Support

Untuk pertanyaan atau issue terkait modul ini:
- **Developer**: Augment Agent
- **Created**: 2025
- **Version**: 1.0.0

---

**Status**: ✅ **Production Ready** dengan dokumentasi lengkap dan testing support.
