Berikut ini adalah struktur tabel "mapping_validasi_2024" :
id_mapping  int(11) 
sekolah_id  int(11)
kd_asesor1  varchar(25)
kd_asesor2  varchar(25)
tgl_mulai_validasi  date
tgl_akhir_validasi  date
no_surat_validasi varchar(30)
tgl_surat_validasi  date
tahap int(11)
tahun_akreditasi  varchar(4)
file_format_5_1_berita_acara_hasil_validasi_1 varchar(50)
file_format_5_1_berita_acara_hasil_validasi_2 varchar(50)
file_pakta_integritas_1 varchar(50)
file_pakta_integritas_2 varchar(50)
file_st_validasi  varchar(50)
provinsi_id int(11)


Berikut ini struktur tabel "sekolah" :
sekolah_id int(11)
nama_sekolah varchar(100)
npsn varchar(30)
jenjang_id int(1)
rumpun varchar(20)
alamat text
tipe_sekolah_id int(1)
status_sekolah_id int(1)
provinsi_id int(2)
kota_id int(6)
desa_kel<PERSON>han varchar(200)
kecamatan varchar(100)
nama_kepsek varchar(100)
no_hp_kepsek varchar(50)
no_wa_kepsek varchar(50)
nama_operator varchar(50)
no_hp_operator varchar(20)
no_wa_operator varchar(20)
email varchar(100)
nama_yayasan varchar(200)
no_akte varchar(50)
tahun_berdiri varchar(4)
status_keaktifan_id varchar(1)
soft_delete rchar(1)

Berikut ini adalah struktur tabel "jenjang" :
id_jenjang int(11)
jenjang_id varchar(2)
nm_jenjang varchar(15)

Berikut ini struktur tabel "kab_kota" :
id_kota int(11) 
kota_id varchar(10)
nm_kota varchar(50)
provinsi_id int(11)
kd_user varchar(25)

Berikut ini struktur tabel "asesor_1" :
id_asesor1 int(11)
kd_asesor1 varchar(25)
nia1 varchar(20) 
nm_asesor1  varchar(100) 
ktp varchar(20) 
unit_kerja  varchar(300) 
kota_id1 varchar(10)
provinsi_id int(11)
no_sertifikat varchar(30)
no_hp varchar(50)
no_wa varchar(50)
tempat_lahir varchar(30)
tgl_lahir date
jabatan varchar(50)
jabatan_struktural varchar(20)
pendidikan varchar(15)
jenjang_id int(11)
rumpun varchar(7)
grade varchar(1)
jk varchar(10)
alamat_kantor varchar(300)
alamat_rumah varchar(300)
email varchar(50)
thn_terbit_sertifikat date
kegiatan varchar(50)
status_keaktifan_id varchar(1)
sebab text
kd_user varchar(50)
soft_delete varchar(1)

Berikut ini tabel "asesor_2" :
id_asesor2 int(11)
kd_asesor2 varchar(25)
nia2 varchar(20)
nm_asesor2 varchar(100)
ktp varchar(20)
unit_kerja varchar(300)
kota_id2 varchar(10)
provinsi_id int(11)
no_sertifikat varchar(30)
no_hp varchar(50)
no_wa varchar(50)
tempat_lahir varchar(30)
tgl_lahir date
jabatan varchar(50)
jabatan_struktural  varchar(20)
pendidikan varchar(15)
jenjang_id int(11)
rumpun varchar(7)
grade varchar(1)
jk varchar(10)
alamat_kantor varchar(300)
alamat_rumah varchar(300)
email varchar(50)
thn_terbit_sertifikat date
kegiatan varchar(50)
status_keaktifan_id varchar(1)
sebab text
kd_user varchar(50)
soft_delete varchar(1)

Berikut ini struktur tabel "mapping_validasi_tahun_2024" :
id_mapping_tahun  int(11)
nama_tahun  int(4)
provinsi_id int(11)

==============================================================================================================================

sebenarnya modul yang akan kita implementasikan ini mirip dengan modul-mudul sebelumnya,
buatlah modul "Mapping Validasi Dasmen IASP 2024" pada direktori "asesor/mapping_validasi_2024/validasi.php", juga tersedia sub direktori ajax dan js, terdapat 5 kolom dengan tabel header yang digunakan adalah :

- NO autoincrement;

- SEKOLAH, pada tabel body berisi data sebagai berikut : 
  NPSN : sekolah.npsn,
  NAMA : sekolah.nama_sekolah,
  JENJANG : jenjang.jenjang_id=sekolah.jenjang_id (jenjang.nm_jenjang),
  KAB/KOTA : sekolah.kota_id = kab_kota.kota_id (kab_kota.nm_kota),
  NAMA KEPSEK : sekolah.nama_kepsek,
  NO HP KEPSEK : sekolah.no_hp_kepsek,
  TAHAP VISITASI : mapping_validasi_2024.tahap

- VALIDATOR, pada tabel body berisi 
  VALIDATOR 1 (colspan =2), NIA : asesor_1.nia1, NAMA : asesor_1.nm_asesor1, KAB/KOTA : asesor_1.kota_id1 = kab_kota.kota_id (kab_kota.nm_kota), asesor_1.no_hp
  VALIDATOR 2 (colspan =2), NIA : asesor_2.nia2, NAMA : asesor_2.nm_asesor2, KAB/KOTA : asesor_2.kota_id2 = kab_kota.kota_id (kab_kota.nm_kota), asesor_1.no_hp

- FORM UPLOAD DOKUMEN
  Tombol "Upload File Format 5.1 Berita Acara Hasil Validasi 1" hanya tampil jika session login $kd_user==$asesor1, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_format_5_1_berita_acara_hasil_validasi_1/"

  Tombol "Upload File Format 5.1 Berita Acara Hasil Validasi 2" hanya tampil jika session login $kd_user==$asesor2, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_format_5_1_berita_acara_hasil_validasi_2/"


- DOKUMEN UNGGAHAN pada tabel body berisi

  label "File Format 5.1 Berita Acara Hasil Validasi 1 :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_validasi_2024.file_format_5_1_berita_acara_hasil_validasi_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah, hanya tampil jika session login $kd_user==$asesor1
  
  label "File Format 5.1 Berita Acara Hasil Validasi 2 :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_validasi_2024.file_format_5_1_berita_acara_hasil_validasi_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah, hanya tampil jika session login $kd_user==$asesor2
  
  
- JADWAL DAN AKSI pada tabel body berisi 
  label "Tanggal Mulai Validasi : mapping_validasi_2024.tgl_mulai_validasi,
  label "Tanggal Mulai Validasi : mapping_validasi_2024.tgl_akhir_validasi,
  tombol "Download Surat Tugas Validasi" dimana ketika tombol tersebut diklik akan membuka tab baru yang akan membuka file "asesor/mapping_validasi_2024/mapping_validasi_st_validasi_2024.php"


oh iya lupa, tambahkan
<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Asesor
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');
?>

// Include header
<?php include '../header.php'; ?>


<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

terakhir di paling bawah <!-- Include footer -->
<?php include '../footer.php'; ?>

Berikut ini contoh query yang bisa anda gunakan untuk modul ini:

$tahun = "SELECT mapping_validasi_tahun_2024.nama_tahun FROM mapping_validasi_tahun_2024 WHERE mapping_validasi_tahun_2024.provinsi_id='$provinsi_id' ";
$result_tahun = $conn->query($tahun);
if($result_tahun->num_rows > 0) {
while($row_tahun = $result_tahun->fetch_assoc()) {
    $nama_tahun = $row_tahun['nama_tahun'];


$sql = "SELECT mapping_validasi_2024.id_mapping, sekolah.sekolah_id, sekolah.npsn, sekolah.nama_sekolah,
        sekolah.nama_kepsek, sekolah.no_hp_kepsek, sekolah.no_wa_kepsek, sekolah.jenjang_id, jenjang.nm_jenjang,
        sekolah.kota_id, kab_kota.nm_kota, mapping_validasi_2024.kd_asesor1, mapping_validasi_2024.kd_asesor2,
        mapping_validasi_2024.tahap,
        mapping_validasi_2024.file_format_5_1_berita_acara_hasil_validasi_1,
        mapping_validasi_2024.file_format_5_1_berita_acara_hasil_validasi_2,
        (SELECT asesor.nia from asesor WHERE mapping_validasi_2024.kd_asesor1=asesor.kd_asesor) as nia1,
        (SELECT asesor.nm_asesor from asesor WHERE mapping_validasi_2024.kd_asesor1=asesor.kd_asesor) as nama1,
        (SELECT asesor.no_hp from asesor WHERE mapping_validasi_2024.kd_asesor1=asesor.kd_asesor) as hp1,
        (SELECT kab_kota.nm_kota from asesor LEFT JOIN kab_kota ON asesor.kota_id=kab_kota.kota_id
            WHERE mapping_validasi_2024.kd_asesor1=asesor.kd_asesor) as kota1,
        (SELECT asesor.nia from asesor WHERE mapping_validasi_2024.kd_asesor2=asesor.kd_asesor) as nia2,
        (SELECT asesor.nm_asesor from asesor WHERE mapping_validasi_2024.kd_asesor2=asesor.kd_asesor) as nama2,
        (SELECT asesor.no_hp from asesor WHERE mapping_validasi_2024.kd_asesor2=asesor.kd_asesor) as hp2,
        (SELECT kab_kota.nm_kota from asesor LEFT JOIN kab_kota ON asesor.kota_id=kab_kota.kota_id
            WHERE mapping_validasi_2024.kd_asesor2=asesor.kd_asesor) as kota2, 

        (SELECT asesor_1.nm_asesor1 from asesor_1 LEFT JOIN mapping_2024 ON mapping_validasi_2024.sekolah_id=mapping_2024.sekolah_id WHERE mapping_2024.kd_asesor1=asesor_1.kd_asesor1 AND mapping_2024.sekolah_id=mapping_validasi_2024.sekolah_id AND mapping_2024.tahun_akreditasi='$nama_tahun') as nama_1,

        (SELECT asesor_1.no_hp from asesor_1 LEFT JOIN mapping_2024 ON mapping_validasi_2024.sekolah_id=mapping_2024.sekolah_id WHERE mapping_2024.kd_asesor1=asesor_1.kd_asesor1 AND mapping_2024.sekolah_id=mapping_validasi_2024.sekolah_id AND mapping_2024.tahun_akreditasi='$nama_tahun') as hape_1,

        (SELECT asesor_2.nm_asesor2 from asesor_2 LEFT JOIN mapping_2024 ON mapping_validasi_2024.sekolah_id=mapping_2024.sekolah_id WHERE mapping_2024.kd_asesor2=asesor_2.kd_asesor2 AND mapping_2024.sekolah_id=mapping_validasi_2024.sekolah_id AND mapping_2024.tahun_akreditasi='$nama_tahun') as nama_2,

        (SELECT asesor_2.no_hp from asesor_2 
          LEFT JOIN mapping_2024 ON mapping_validasi_2024.sekolah_id=mapping_2024.sekolah_id 
          WHERE mapping_2024.kd_asesor2=asesor_2.kd_asesor2 
          AND mapping_2024.sekolah_id=mapping_validasi_2024.sekolah_id 
          AND mapping_2024.tahun_akreditasi='$nama_tahun') as hape_2,

        

        mapping_validasi_2024.tahun_akreditasi FROM mapping_validasi_2024
        LEFT JOIN sekolah ON mapping_validasi_2024.sekolah_id=sekolah.sekolah_id
        LEFT JOIN jenjang ON sekolah.jenjang_id=jenjang.jenjang_id
        LEFT JOIN kab_kota ON sekolah.kota_id=kab_kota.kota_id
        WHERE (kd_asesor1='$kd_user' OR kd_asesor2='$kd_user')
        AND mapping_validasi_2024.tahun_akreditasi='$nama_tahun'
        AND mapping_validasi_2024.provinsi_id='$provinsi_id'  ";
$result = $conn->query($sql);
if($result->num_rows > 0) {
while($row = $result->fetch_assoc()) {
$nomor++;
$asesor1                                        = $row['kd_asesor1'];
$asesor2                                        = $row['kd_asesor2'];
$file_format_5_1_berita_acara_hasil_validasi_1  = $row['file_format_5_1_berita_acara_hasil_validasi_1'];
$file_format_5_1_berita_acara_hasil_validasi_2  = $row['file_format_5_1_berita_acara_hasil_validasi_2'];

jika menurut anda query sql tersebut belum maksimal, silahkan diperbaiki agar maksimal kinerjanya

sampai disini apakah anda mengerti dengan apa yang saya maksudkan?
jika ada pertanyaan yang ingin ditanyakan dipersilahkan

==============================================================================================================================

1. File Upload Types: 2 file saja yaitu : file_format_5_1_berita_acara_hasil_validasi_1 dan file_format_5_1_berita_acara_hasil_validasi_2
2. Tanggal Validasi: tidak perlu ada fitur input tanggal karena sudah ada di database, hanya tinggal ditampilkans saja
3. Silahkan baca dan analisis kode sumber file "asesor/mapping_validasi_2024/mapping_validasi_st_validasi_2024.php", sama halnya dengan modul "Mapping Visitasi PAUD" yang ada di direktori "asesor/mapping_paud_visitasi_2020/"
4. gunakan saja tabel asesor_1/asesor_2 sesuai struktur tabel sebagaimana modul "Mapping Visitasi Dasmen IASP 2024" yang ada di direktori "asesor/mapping_dasmen_2024/mapping.php"

==============================================================================================================================