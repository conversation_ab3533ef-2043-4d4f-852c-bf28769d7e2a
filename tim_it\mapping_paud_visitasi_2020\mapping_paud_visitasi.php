<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');
?>

<!-- Include header -->
<?php include '../header.php'; ?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Content Wrapper -->
<div class="content-wrapper">
    <!-- Content Header -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><i class="fas fa-users-cog"></i> Mapping Asesor Visitasi PAUD</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="#">IASP 2020</a></li>
                        <li class="breadcrumb-item"><a href="#">Mapping PAUD</a></li>
                        <li class="breadcrumb-item active">Visitasi</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-list"></i> Data Mapping Asesor Visitasi PAUD
                            </h3>
                            <div class="card-tools">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-success" id="btn-input-mapping-visitasi">
                                        <i class="fas fa-plus"></i> Input Data Mapping
                                    </button>
                                    <button type="button" class="btn btn-info" id="btn-export-excel">
                                        <i class="fas fa-file-excel"></i> Export Excel
                                    </button>
                                    <button type="button" class="btn btn-warning" id="btn-import-excel">
                                        <i class="fas fa-file-upload"></i> Import Excel
                                    </button>
                                    <button type="button" class="btn btn-secondary" id="btn-tahun-akreditasi">
                                        <i class="fas fa-calendar-alt"></i> Tahun Akreditasi
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="table-mapping-visitasi" class="table table-bordered table-striped table-hover" width="100%">
                                    <thead>
                                        <tr>
                                            <th>NO <br> &nbsp; </th>
                                            <th>NPSN <br> &nbsp; </th>
                                            <th>NAMA <br> SEKOLAH</th>
                                            <th>JENJANG <br> &nbsp; </th>
                                            <th>KAB/KOTA <br> &nbsp; </th>
                                            <th>NIA <br> ASESOR A</th>
                                            <th>NAMA <br> ASESOR A</th>
                                            <th>NIA <br> ASESOR B</th>
                                            <th>NAMA <br> ASESOR B</th>
                                            <th>TAHUN <br> AKREDITASI</th>
                                            <th>TAHAP <br> VISITASI</th>
                                            <th>AKSI <br> &nbsp; </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data akan dimuat via AJAX -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Modal Input Data Mapping Visitasi PAUD -->
<div class="modal fade" id="modal-input-mapping-visitasi" tabindex="-1" role="dialog" aria-labelledby="modalInputMappingVisitasiLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="modalInputMappingVisitasiLabel">
                    <i class="fas fa-plus"></i> Input Data Mapping Visitasi PAUD
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-input-mapping-visitasi">
                <div class="modal-body">
                    <div class="row">
                        <!-- Kolom Kiri -->
                        <div class="col-md-6">
                            <!-- NPSN Sekolah -->
                            <div class="form-group">
                                <label for="npsn_sekolah">NPSN Sekolah <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="npsn_sekolah" name="npsn_sekolah" required placeholder="Masukkan NPSN">
                                <small class="form-text text-muted">NPSN akan dicari otomatis</small>

                                <!-- Info Sekolah -->
                                <div class="mt-2" id="info-sekolah" style="display: none;">
                                    <div class="card card-outline card-success">
                                        <div class="card-body p-2">
                                            <small class="text-success">
                                                <i class="fas fa-check-circle"></i> <strong>Sekolah ditemukan:</strong><br>
                                                <strong>Nama:</strong> <span id="info-nama-sekolah">-</span><br>
                                                <strong>Jenjang:</strong> <span id="info-jenjang-sekolah">-</span><br>
                                                <strong>Kota:</strong> <span id="info-kota-sekolah">-</span>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" id="sekolah_id" name="sekolah_id">
                            </div>

                            <!-- NIA Asesor A -->
                            <div class="form-group">
                                <label for="nia_asesor_a">NIA Asesor A <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nia_asesor_a" name="nia_asesor_a" required placeholder="Masukkan NIA Asesor A">
                                <small class="form-text text-muted">NIA akan dicari otomatis</small>

                                <!-- Info Asesor A -->
                                <div class="mt-2" id="info-asesor-a" style="display: none;">
                                    <div class="card card-outline card-primary">
                                        <div class="card-body p-2">
                                            <small class="text-primary">
                                                <i class="fas fa-check-circle"></i> <strong>Asesor A ditemukan:</strong><br>
                                                <strong>Nama:</strong> <span id="info-nama-asesor-a">-</span><br>
                                                <strong>Kota & Rumpun:</strong> <span id="info-kota-asesor-a">-</span>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" id="kd_asesor_a" name="kd_asesor1">
                            </div>

                            <!-- NIA Asesor B -->
                            <div class="form-group">
                                <label for="nia_asesor_b">NIA Asesor B <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nia_asesor_b" name="nia_asesor_b" required placeholder="Masukkan NIA Asesor B">
                                <small class="form-text text-muted">NIA akan dicari otomatis</small>

                                <!-- Info Asesor B -->
                                <div class="mt-2" id="info-asesor-b" style="display: none;">
                                    <div class="card card-outline card-warning">
                                        <div class="card-body p-2">
                                            <small class="text-warning">
                                                <i class="fas fa-check-circle"></i> <strong>Asesor B ditemukan:</strong><br>
                                                <strong>Nama:</strong> <span id="info-nama-asesor-b">-</span><br>
                                                <strong>Kota & Rumpun:</strong> <span id="info-kota-asesor-b">-</span>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" id="kd_asesor_b" name="kd_asesor2">
                            </div>

                            <!-- Tahun Akreditasi -->
                            <div class="form-group">
                                <label for="tahun_akreditasi">Tahun Akreditasi <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="tahun_akreditasi" name="tahun_akreditasi" required placeholder="Contoh: 2024">
                                <small class="form-text text-muted">Tahun akreditasi</small>
                            </div>
                        </div>

                        <!-- Kolom Kanan -->
                        <div class="col-md-6">
                            <!-- Tanggal Mulai Visitasi -->
                            <div class="form-group">
                                <label for="tgl_mulai_visitasi">Tanggal Mulai Visitasi</label>
                                <input type="date" class="form-control" id="tgl_mulai_visitasi" name="tgl_mulai_visitasi">
                                <small class="form-text text-muted">Tanggal mulai visitasi (opsional)</small>
                            </div>

                            <!-- Tanggal Akhir Visitasi -->
                            <div class="form-group">
                                <label for="tgl_akhir_visitasi">Tanggal Akhir Visitasi</label>
                                <input type="date" class="form-control" id="tgl_akhir_visitasi" name="tgl_akhir_visitasi">
                                <small class="form-text text-muted">Tanggal akhir visitasi (opsional)</small>
                            </div>

                            <!-- Tahap Ke -->
                            <div class="form-group">
                                <label for="tahap">Tahap Ke <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="tahap" name="tahap" required placeholder="Contoh: 1, 2, atau 3">
                                <small class="form-text text-muted">Tahap visitasi</small>
                            </div>

                            <!-- Nomor Surat Tugas Visitasi -->
                            <div class="form-group">
                                <label for="no_surat">Nomor Surat Tugas Visitasi</label>
                                <input type="text" class="form-control" id="no_surat" name="no_surat" placeholder="Nomor surat tugas">
                                <small class="form-text text-muted">Nomor surat tugas visitasi (opsional)</small>
                            </div>

                            <!-- Tanggal Surat Tugas Visitasi -->
                            <div class="form-group">
                                <label for="tgl_surat">Tanggal Surat Tugas Visitasi</label>
                                <input type="date" class="form-control" id="tgl_surat" name="tgl_surat">
                                <small class="form-text text-muted">Tanggal surat tugas visitasi (opsional)</small>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden field untuk provinsi_id -->
                    <input type="hidden" name="provinsi_id" value="<?php echo $_SESSION['provinsi_id']; ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Simpan Data
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Filter Tahun Akreditasi -->
<div class="modal fade" id="modal-tahun-akreditasi" tabindex="-1" role="dialog" aria-labelledby="modalTahunAkreditasiLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-secondary text-white">
                <h5 class="modal-title" id="modalTahunAkreditasiLabel">
                    <i class="fas fa-calendar-alt"></i> Filter Tahun Akreditasi
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-tahun-akreditasi">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="nama_tahun">Tahun Akreditasi Aktif</label>
                        <input type="text" class="form-control" id="nama_tahun" name="nama_tahun" required placeholder="Contoh: 2024">
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle text-info"></i>
                            Tahun yang akan digunakan untuk filter data mapping visitasi
                        </small>
                    </div>

                    <!-- Info Current Year -->
                    <div class="alert alert-info" id="info-current-year" style="display: none;">
                        <small>
                            <i class="fas fa-calendar"></i> <strong>Tahun saat ini:</strong>
                            <span id="current-year-display">-</span>
                        </small>
                    </div>

                    <!-- Hidden field untuk provinsi_id -->
                    <input type="hidden" name="provinsi_id" value="<?php echo $_SESSION['provinsi_id']; ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="fas fa-save"></i> Update Filter
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Detail Mapping Visitasi PAUD -->
<div class="modal fade" id="modal-detail-mapping-visitasi" tabindex="-1" role="dialog" aria-labelledby="modalDetailMappingVisitasiLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="modalDetailMappingVisitasiLabel">
                    <i class="fas fa-eye"></i> Detail Mapping Visitasi PAUD
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- Kolom 1: Data Sekolah -->
                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white text-center">
                                <h6 class="mb-0"><i class="fas fa-school"></i> Data Sekolah</h6>
                            </div>
                            <div class="card-body p-0">
                                <table class="table table-sm table-bordered mb-0">
                                    <tbody>
                                        <tr>
                                            <td class="font-weight-bold" style="width: 40%;">NPSN</td>
                                            <td id="detail-npsn">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">Nama Sekolah</td>
                                            <td id="detail-nama-sekolah">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">Jenjang</td>
                                            <td id="detail-jenjang">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">Kab/Kota</td>
                                            <td id="detail-kab-kota-sekolah">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">Nama Kepala Sekolah</td>
                                            <td id="detail-nama-kepsek">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">HP Kepala Sekolah</td>
                                            <td id="detail-hp-kepsek">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">No WA Kepala Sekolah</td>
                                            <td id="detail-wa-kepsek">-</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Kolom 2: Data Asesor -->
                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white text-center">
                                <h6 class="mb-0"><i class="fas fa-users"></i> Data Asesor</h6>
                            </div>
                            <div class="card-body p-0">
                                <table class="table table-sm table-bordered mb-0">
                                    <tbody>
                                        <tr class="bg-light">
                                            <td colspan="2" class="font-weight-bold text-center">
                                                <i class="fas fa-user text-primary"></i> Asesor A
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold" style="width: 40%;">NIA</td>
                                            <td id="detail-nia-asesor-a">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">Nama</td>
                                            <td id="detail-nama-asesor-a">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">No HP</td>
                                            <td id="detail-hp-asesor-a">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">Kab/Kota</td>
                                            <td id="detail-kab-kota-asesor-a">-</td>
                                        </tr>
                                        <tr class="bg-light">
                                            <td colspan="2" class="font-weight-bold text-center">
                                                <i class="fas fa-user text-warning"></i> Asesor B
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">NIA</td>
                                            <td id="detail-nia-asesor-b">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">Nama</td>
                                            <td id="detail-nama-asesor-b">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">No HP</td>
                                            <td id="detail-hp-asesor-b">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">Kab/Kota</td>
                                            <td id="detail-kab-kota-asesor-b">-</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Kolom 3: Dokumen Unggahan -->
                    <div class="col-xl-2 col-lg-2 col-md-6 col-sm-12 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white text-center">
                                <h6 class="mb-0"><i class="fas fa-file-upload"></i> Dokumen Unggahan</h6>
                            </div>
                            <div class="card-body p-0">
                                <table class="table table-sm table-bordered mb-0">
                                    <tbody>
                                        <tr>
                                            <td class="font-weight-bold" style="width: 60%;">File Pakta Integritas 1</td>
                                            <td id="detail-file-pakta-1" class="text-center">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">File Pakta Integritas 2</td>
                                            <td id="detail-file-pakta-2" class="text-center">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">File Berita Acara Visitasi</td>
                                            <td id="detail-file-berita-acara" class="text-center">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">File Temuan Hasil Visitasi</td>
                                            <td id="detail-file-temuan-hasil" class="text-center">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">File Absen Pembuka</td>
                                            <td id="detail-file-absen-pembuka" class="text-center">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">File Absen Penutup</td>
                                            <td id="detail-file-absen-penutup" class="text-center">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">File Foto Visitasi</td>
                                            <td id="detail-file-foto-visitasi" class="text-center">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">File Laporan Individu 1</td>
                                            <td id="detail-file-laporan-individu-1" class="text-center">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">File Laporan Individu 2</td>
                                            <td id="detail-file-laporan-individu-2" class="text-center">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">File Laporan Kelompok</td>
                                            <td id="detail-file-laporan-kelompok" class="text-center">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">File Penjelasan Hasil Akreditasi</td>
                                            <td id="detail-file-penjelasan-hasil" class="text-center">-</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Kolom 4: Pelaksanaan Kegiatan -->
                    <div class="col-xl-2 col-lg-2 col-md-6 col-sm-12 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white text-center">
                                <h6 class="mb-0"><i class="fas fa-calendar-check"></i> Pelaksanaan Kegiatan</h6>
                            </div>
                            <div class="card-body p-0">
                                <table class="table table-sm table-bordered mb-0">
                                    <tbody>
                                        <tr>
                                            <td class="font-weight-bold" style="width: 50%;">Tanggal Visitasi Dimulai</td>
                                            <td id="detail-tgl-mulai-visitasi">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">Tanggal Visitasi Berakhir</td>
                                            <td id="detail-tgl-akhir-visitasi">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">No Surat Tugas Visitasi</td>
                                            <td id="detail-no-surat">-</td>
                                        </tr>
                                        <tr>
                                            <td class="font-weight-bold">Tanggal Surat Tugas Visitasi</td>
                                            <td id="detail-tgl-surat">-</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Kolom 5: Aksi -->
                    <div class="col-xl-2 col-lg-2 col-md-6 col-sm-12 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white text-center">
                                <h6 class="mb-0"><i class="fas fa-cogs"></i> Aksi</h6>
                            </div>
                            <div class="card-body p-0">
                                <table class="table table-sm table-bordered mb-0">
                                    <tbody>
                                        <tr>
                                            <td class="text-center">
                                                <button type="button" class="btn btn-warning btn-sm btn-block" id="btn-edit-tanggal-kegiatan">
                                                    <i class="fas fa-calendar-edit"></i> Edit Tanggal Kegiatan
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-center">
                                                <button type="button" class="btn btn-info btn-sm btn-block" id="btn-edit-asesor-perubahan">
                                                    <i class="fas fa-user-edit"></i> Edit Asesor Perubahan
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-center">
                                                <button type="button" class="btn btn-success btn-sm btn-block" id="btn-download-surat-tugas">
                                                    <i class="fas fa-download"></i> Download Surat Tugas Visitasi
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-center">
                                                <button type="button" class="btn btn-danger btn-sm btn-block" id="btn-hapus-mapping">
                                                    <i class="fas fa-trash"></i> Hapus
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Tanggal Kegiatan (Nested Modal) -->
<div class="modal fade" id="modal-edit-tanggal-kegiatan" tabindex="-1" role="dialog" aria-labelledby="modalEditTanggalKegiatanLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="modalEditTanggalKegiatanLabel">
                    <i class="fas fa-calendar-edit"></i> Edit Tanggal Kegiatan
                </h5>
                <button type="button" class="close text-dark" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-edit-tanggal-kegiatan">
                <div class="modal-body">
                    <!-- Tanggal Visitasi Dimulai -->
                    <div class="form-group">
                        <label for="edit_tgl_mulai_visitasi">Tanggal Visitasi Dimulai</label>
                        <input type="date" class="form-control" id="edit_tgl_mulai_visitasi" name="tgl_mulai_visitasi">
                        <small class="form-text text-muted">Tanggal mulai pelaksanaan visitasi (opsional)</small>
                    </div>

                    <!-- Tanggal Visitasi Berakhir -->
                    <div class="form-group">
                        <label for="edit_tgl_akhir_visitasi">Tanggal Visitasi Berakhir</label>
                        <input type="date" class="form-control" id="edit_tgl_akhir_visitasi" name="tgl_akhir_visitasi">
                        <small class="form-text text-muted">Tanggal akhir pelaksanaan visitasi (opsional)</small>
                    </div>

                    <!-- No Surat Tugas Visitasi -->
                    <div class="form-group">
                        <label for="edit_no_surat">No Surat Tugas Visitasi</label>
                        <input type="text" class="form-control" id="edit_no_surat" name="no_surat" placeholder="Nomor surat tugas">
                        <small class="form-text text-muted">Nomor surat tugas visitasi (opsional)</small>
                    </div>

                    <!-- Tanggal Surat Tugas Visitasi -->
                    <div class="form-group">
                        <label for="edit_tgl_surat">Tanggal Surat Tugas Visitasi</label>
                        <input type="date" class="form-control" id="edit_tgl_surat" name="tgl_surat">
                        <small class="form-text text-muted">Tanggal surat tugas visitasi (opsional)</small>
                    </div>

                    <!-- Hidden field untuk ID mapping -->
                    <input type="hidden" id="edit_id_mapping" name="id_mapping">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-warning btn-sm">
                        <i class="fas fa-save"></i> Update Perubahan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit Asesor Perubahan (Nested Modal) -->
<div class="modal fade" id="modal-edit-asesor-perubahan" tabindex="-1" role="dialog" aria-labelledby="modalEditAsesorPerubahanLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="modalEditAsesorPerubahanLabel">
                    <i class="fas fa-user-edit"></i> Edit Asesor Perubahan
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-edit-asesor-perubahan">
                <div class="modal-body">
                    <!-- NIA Asesor A -->
                    <div class="form-group">
                        <label for="edit_nia_asesor_a">NIA Asesor A</label>
                        <input type="text" class="form-control" id="edit_nia_asesor_a" name="nia_asesor_a" placeholder="Masukkan NIA Asesor A">
                        <small class="form-text text-muted">NIA akan dicari otomatis (opsional)</small>

                        <!-- Info Asesor A -->
                        <div class="mt-2" id="edit-info-asesor-a" style="display: none;">
                            <div class="card card-outline card-primary">
                                <div class="card-body p-2">
                                    <small class="text-primary">
                                        <i class="fas fa-check-circle"></i> <strong>Asesor A ditemukan:</strong><br>
                                        <strong>Nama:</strong> <span id="edit-info-nama-asesor-a">-</span><br>
                                        <strong>Kota & Rumpun:</strong> <span id="edit-info-kota-asesor-a">-</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" id="edit_kd_asesor_a" name="kd_asesor1">
                    </div>

                    <!-- NIA Asesor B -->
                    <div class="form-group">
                        <label for="edit_nia_asesor_b">NIA Asesor B</label>
                        <input type="text" class="form-control" id="edit_nia_asesor_b" name="nia_asesor_b" placeholder="Masukkan NIA Asesor B">
                        <small class="form-text text-muted">NIA akan dicari otomatis (opsional)</small>

                        <!-- Info Asesor B -->
                        <div class="mt-2" id="edit-info-asesor-b" style="display: none;">
                            <div class="card card-outline card-warning">
                                <div class="card-body p-2">
                                    <small class="text-warning">
                                        <i class="fas fa-check-circle"></i> <strong>Asesor B ditemukan:</strong><br>
                                        <strong>Nama:</strong> <span id="edit-info-nama-asesor-b">-</span><br>
                                        <strong>Kota & Rumpun:</strong> <span id="edit-info-kota-asesor-b">-</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" id="edit_kd_asesor_b" name="kd_asesor2">
                    </div>

                    <!-- Hidden field untuk ID mapping -->
                    <input type="hidden" id="edit_asesor_id_mapping" name="id_mapping">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-info btn-sm">
                        <i class="fas fa-save"></i> Update Perubahan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Import Excel -->
<div class="modal fade" id="modal-import-excel-visitasi" tabindex="-1" role="dialog" aria-labelledby="modal-import-excel-visitasi-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="modal-import-excel-visitasi-label">
                    <i class="fas fa-file-upload"></i> Import Data Mapping Visitasi PAUD
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- File Upload Section -->
                <div id="upload-section-visitasi">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="excel-file-visitasi">Pilih File Excel <span class="text-danger">*</span></label>
                                <input type="file" class="form-control-file" id="excel-file-visitasi" name="excel_file_visitasi"
                                       accept=".xlsx,.xls" required>
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    Format yang didukung: .xlsx, .xls (maksimal 100 baris)
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-info" id="petunjuk-import-visitasi">
                                <h6><i class="fas fa-lightbulb"></i> Petunjuk Import:</h6>
                                <ul class="mb-0">
                                    <li>Download template Excel terlebih dahulu</li>
                                    <li>Isi data sesuai format yang tersedia</li>
                                    <li>Pastikan Asesor A dan Asesor B berbeda</li>
                                    <li>Hapus baris contoh sebelum import</li>
                                    <li>Pastikan format tanggal: yyyy-mm-dd</li>
                                    <li>NPSN yang sudah ada di tahun yang sama akan di-skip</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Section (hidden initially) -->
                <div id="progress-section-visitasi" style="display: none;">
                    <div class="row">
                        <div class="col-md-12">
                            <h6 id="progress-title-visitasi">Memproses import...</h6>
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     id="import-progress-bar-visitasi" role="progressbar"
                                     style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                    0%
                                </div>
                            </div>
                            <p id="progress-message-visitasi" class="text-muted">Memulai proses import...</p>
                        </div>
                    </div>
                </div>

                <!-- Results Section (hidden initially) -->
                <div id="results-section-visitasi" style="display: none;">
                    <div class="row">
                        <div class="col-md-12">
                            <div id="import-results-visitasi"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="btn-cancel-import-visitasi">
                    <i class="fas fa-times"></i> Batal
                </button>
                <button type="button" class="btn btn-outline-warning" id="btn-download-template-visitasi">
                    <i class="fas fa-download"></i> Download Template
                </button>
                <button type="button" class="btn btn-warning" id="btn-start-import-visitasi">
                    <i class="fas fa-upload"></i> Mulai Import
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Konfirmasi Hapus Mapping Visitasi -->
<div class="modal fade" id="modal-konfirmasi-hapus-visitasi" tabindex="-1" role="dialog" aria-labelledby="modalKonfirmasiHapusVisitasiLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="modalKonfirmasiHapusVisitasiLabel">
                    <i class="fas fa-exclamation-triangle"></i> Konfirmasi Hapus
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3">
                    <i class="fas fa-trash-alt text-danger" style="font-size: 48px;"></i>
                </div>
                <h6 class="mb-3">
                    Apakah anda yakin menghapus mapping visitasi <strong id="nama-sekolah-hapus-visitasi">-</strong>?
                </h6>

                <!-- Detail Data yang akan dihapus -->
                <div class="alert alert-warning text-left" style="font-size: 13px;">
                    <div class="row">
                        <div class="col-4"><strong>NPSN:</strong></div>
                        <div class="col-8" id="hapus-npsn-visitasi">-</div>
                    </div>
                    <div class="row">
                        <div class="col-4"><strong>Sekolah:</strong></div>
                        <div class="col-8" id="hapus-sekolah-visitasi">-</div>
                    </div>
                    <div class="row">
                        <div class="col-4"><strong>Asesor A:</strong></div>
                        <div class="col-8" id="hapus-asesor1-visitasi">-</div>
                    </div>
                    <div class="row">
                        <div class="col-4"><strong>Asesor B:</strong></div>
                        <div class="col-8" id="hapus-asesor2-visitasi">-</div>
                    </div>
                    <div class="row">
                        <div class="col-4"><strong>Tahun:</strong></div>
                        <div class="col-8" id="hapus-tahun-visitasi">-</div>
                    </div>
                    <div class="row">
                        <div class="col-4"><strong>Tahap:</strong></div>
                        <div class="col-8" id="hapus-tahap-visitasi">-</div>
                    </div>
                </div>

                <p class="text-muted mb-0">
                    <i class="fas fa-exclamation-circle text-danger"></i>
                    <strong>PERINGATAN:</strong> Data akan dihapus permanen!
                </p>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">
                    <i class="fas fa-times"></i> Batal
                </button>
                <button type="button" class="btn btn-danger btn-sm" id="btn-konfirmasi-hapus-visitasi">
                    <i class="fas fa-trash"></i> Ya, Hapus
                </button>
            </div>
            <!-- Hidden field untuk menyimpan ID mapping yang akan dihapus -->
            <input type="hidden" id="id-mapping-visitasi-akan-dihapus">
        </div>
    </div>
</div>

<!-- Include footer -->
<?php include '../footer.php'; ?>

<!-- DataTables & plugins -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap4.min.js"></script>

<!-- Custom JS -->
<script src="js/mapping_visitasi.js"></script>

<style>
.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    font-size: 12px;
}

.table td {
    vertical-align: middle;
    font-size: 12px;
}

.btn-action {
    margin: 0 2px;
}

.card-tools .btn-group .btn {
    margin-left: 5px;
}

.card-tools .btn-group .btn:first-child {
    margin-left: 0;
}

/* Responsive table adjustments */
@media (max-width: 768px) {
    .table th, .table td {
        font-size: 10px;
        padding: 4px;
    }
    
    .card-tools .btn-group {
        flex-direction: column;
    }
    
    .card-tools .btn-group .btn {
        margin: 2px 0;
        font-size: 12px;
    }
}

/* Custom styling for full text display */
.table td {
    word-wrap: break-word;
    white-space: normal;
}

/* Status badges */
.badge-tahap {
    font-size: 11px;
    padding: 4px 8px;
}

/* Button styling */
.btn-group .btn {
    border-radius: 4px;
}

.btn-group .btn + .btn {
    margin-left: 5px;
}

/* Modal Detail Mapping Visitasi Styles */
#modal-detail-mapping-visitasi .modal-dialog {
    max-width: 95%;
}

#modal-detail-mapping-visitasi .card {
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#modal-detail-mapping-visitasi .card-header {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    border-bottom: 1px solid #0056b3;
    font-weight: 600;
}

#modal-detail-mapping-visitasi .table {
    margin-bottom: 0;
    font-size: 12px;
}

#modal-detail-mapping-visitasi .table td {
    padding: 6px 8px;
    border: 1px solid #dee2e6;
    vertical-align: middle;
}

#modal-detail-mapping-visitasi .table .font-weight-bold {
    background-color: #f8f9fa;
    color: #495057;
}

#modal-detail-mapping-visitasi .badge {
    font-size: 10px;
    padding: 4px 8px;
    font-weight: 600;
}

#modal-detail-mapping-visitasi .badge-success {
    background-color: #28a745;
    color: #ffffff;
}

#modal-detail-mapping-visitasi .badge-danger {
    background-color: #dc3545;
    color: #ffffff;
}

#modal-detail-mapping-visitasi .btn-sm {
    font-size: 11px;
    padding: 4px 8px;
}

/* Ensure columns stay aligned */
#modal-detail-mapping-visitasi .row {
    margin-left: -7.5px;
    margin-right: -7.5px;
}

#modal-detail-mapping-visitasi .row > [class*="col-"] {
    padding-left: 7.5px;
    padding-right: 7.5px;
}

/* Responsive adjustments untuk layout sejajar */
@media (min-width: 1200px) {
    /* XL screens: Semua kolom sejajar dalam 1 baris */
    #modal-detail-mapping-visitasi .row {
        display: flex;
        flex-wrap: nowrap;
    }
}

@media (max-width: 1199px) and (min-width: 992px) {
    /* Large screens: 5 kolom dalam 1 baris dengan scroll horizontal jika perlu */
    #modal-detail-mapping-visitasi .row {
        display: flex;
        flex-wrap: nowrap;
        overflow-x: auto;
    }

    #modal-detail-mapping-visitasi .col-lg-2,
    #modal-detail-mapping-visitasi .col-lg-3 {
        flex: 0 0 auto;
        min-width: 200px;
    }
}

@media (max-width: 991px) {
    /* Medium screens dan ke bawah: 2 kolom per baris */
    #modal-detail-mapping-visitasi .row {
        display: flex;
        flex-wrap: wrap;
    }
}

@media (max-width: 575px) {
    /* Small screens: 1 kolom per baris */
    #modal-detail-mapping-visitasi .col-sm-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    #modal-detail-mapping-visitasi .modal-dialog {
        max-width: 98%;
        margin: 10px auto;
    }

    #modal-detail-mapping-visitasi .table {
        font-size: 10px;
    }

    #modal-detail-mapping-visitasi .table td {
        padding: 4px 6px;
    }

    #modal-detail-mapping-visitasi .btn-sm {
        font-size: 10px;
        padding: 3px 6px;
    }
}

/* File Download Links Styling */
.file-download-link {
    cursor: pointer !important;
    transition: all 0.2s ease-in-out;
    text-decoration: none !important;
}

.file-download-link:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    text-decoration: none !important;
    color: #ffffff !important;
}

.file-download-link:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    text-decoration: none !important;
}

.file-download-link .fas {
    margin-right: 4px;
}

/* Non-clickable file status */
.badge[style*="cursor: default"] {
    cursor: default !important;
}

/* Hover effect untuk clickable badges */
.badge.file-download-link:hover {
    background-color: #218838 !important;
}

/* Nested Modal Z-index Management */
#modal-edit-tanggal-kegiatan {
    z-index: 1060; /* Higher than detail modal (1050) */
}

#modal-edit-tanggal-kegiatan .modal-backdrop {
    z-index: 1055; /* Between detail modal and edit modal */
}

/* Ensure detail modal stays visible behind edit modal */
#modal-detail-mapping-visitasi {
    z-index: 1050;
}

/* Edit modal styling */
#modal-edit-tanggal-kegiatan .modal-content {
    border: 2px solid #ffc107;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

#modal-edit-tanggal-kegiatan .modal-header {
    background: linear-gradient(135deg, #ffc107, #e0a800) !important;
    border-bottom: 1px solid #e0a800;
}

#modal-edit-tanggal-kegiatan .form-control {
    font-size: 14px;
}

#modal-edit-tanggal-kegiatan .btn-sm {
    font-size: 12px;
    padding: 4px 12px;
}

/* Edit Asesor Perubahan Modal Styling */
#modal-edit-asesor-perubahan {
    z-index: 1065; /* Higher than edit tanggal modal */
}

#modal-edit-asesor-perubahan .modal-backdrop {
    z-index: 1060;
}

#modal-edit-asesor-perubahan .modal-content {
    border: 2px solid #17a2b8;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

#modal-edit-asesor-perubahan .modal-header {
    background: linear-gradient(135deg, #17a2b8, #138496) !important;
    border-bottom: 1px solid #138496;
}

#modal-edit-asesor-perubahan .form-control {
    font-size: 14px;
}

#modal-edit-asesor-perubahan .btn-sm {
    font-size: 12px;
    padding: 4px 12px;
}

#modal-edit-asesor-perubahan .card-outline {
    border-width: 1px;
}

#modal-edit-asesor-perubahan .card-body {
    font-size: 11px;
}

/* Download Surat Tugas Button Styling */
#btn-download-surat-tugas {
    cursor: pointer !important;
    transition: all 0.2s ease-in-out;
}

#btn-download-surat-tugas:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

#btn-download-surat-tugas .fas {
    margin-right: 5px;
}

/* Hapus Mapping Button Styling */
#btn-hapus-mapping {
    cursor: pointer !important;
    transition: all 0.2s ease-in-out;
}

#btn-hapus-mapping:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
}

#btn-hapus-mapping:focus {
    outline: 2px solid #dc3545;
    outline-offset: 2px;
}

#btn-hapus-mapping .fas {
    margin-right: 5px;
}

#btn-hapus-mapping:disabled {
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
}

/* Modal Konfirmasi Hapus Visitasi Styles */
#modal-konfirmasi-hapus-visitasi {
    z-index: 1080 !important;
}

#modal-konfirmasi-hapus-visitasi .modal-backdrop {
    z-index: 1075 !important;
}

#modal-konfirmasi-hapus-visitasi .modal-body {
    padding: 2rem 1.5rem;
}

#modal-konfirmasi-hapus-visitasi .fas.fa-trash-alt {
    animation: shake 0.5s ease-in-out;
}

#modal-konfirmasi-hapus-visitasi .alert-warning {
    border-left: 4px solid #ffc107;
    background-color: #fff3cd;
    border-color: #ffeaa7;
    font-size: 13px;
    margin: 1rem 0;
}

#modal-konfirmasi-hapus-visitasi .modal-footer {
    border-top: none;
    padding: 1rem 2rem 2rem;
}

#modal-konfirmasi-hapus-visitasi .modal-content {
    border: 2px solid #dc3545;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

#modal-konfirmasi-hapus-visitasi .modal-header {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
    border-bottom: 1px solid #c82333;
}

#modal-konfirmasi-hapus-visitasi .btn-danger:hover {
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
    transform: scale(1.05);
}

#modal-konfirmasi-hapus-visitasi .btn-secondary:hover {
    transform: scale(1.05);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
</style>

<!-- Include XLSX library untuk export Excel -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
