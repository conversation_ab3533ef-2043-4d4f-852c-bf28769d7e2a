<?php
/**
 * AJAX handler untuk mengelola tahun akreditasi
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

header('Content-Type: application/json');

$action = $_POST['action'] ?? $_GET['action'] ?? '';
$provinsi_id = intval($_SESSION['provinsi_id']);

try {
    switch ($action) {
        case 'get':
            // Ambil tahun akreditasi yang ada
            $query = "SELECT id_mapping_tahun, nama_tahun
                     FROM mapping_validasi_tahun_2025
                     WHERE provinsi_id = $provinsi_id
                     ORDER BY nama_tahun DESC";

            $result = $conn->query($query);
            $tahun_list = [];

            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $tahun_list[] = [
                        'id' => $row['id_mapping_tahun'],
                        'tahun' => $row['nama_tahun']
                    ];
                }
            }

            echo json_encode([
                'success' => true,
                'data' => $tahun_list,
                'current_tahun' => !empty($tahun_list) ? $tahun_list[0]['tahun'] : date('Y')
            ]);
            break;

        case 'update':
            // Update tahun akreditasi
            $tahun_baru = intval($_POST['tahun_akreditasi'] ?? 0);

            if ($tahun_baru < 2020 || $tahun_baru > 2030) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Tahun harus antara 2020-2030'
                ]);
                exit;
            }

            // Cek apakah sudah ada data tahun untuk provinsi ini
            $check_query = "SELECT id_mapping_tahun FROM mapping_validasi_tahun_2025 WHERE provinsi_id = $provinsi_id";
            $check_result = $conn->query($check_query);

            if ($check_result && $check_result->num_rows > 0) {
                // Update tahun yang sudah ada
                $update_query = "UPDATE mapping_validasi_tahun_2025
                               SET nama_tahun = $tahun_baru
                               WHERE provinsi_id = $provinsi_id";

                if ($conn->query($update_query)) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Tahun akreditasi berhasil diupdate',
                        'tahun' => $tahun_baru
                    ]);
                } else {
                    echo json_encode([
                        'success' => false,
                        'message' => 'Gagal update tahun: ' . $conn->error
                    ]);
                }
            } else {
                // Insert tahun baru
                $insert_query = "INSERT INTO mapping_validasi_tahun_2025 (nama_tahun, provinsi_id)
                               VALUES ($tahun_baru, $provinsi_id)";

                if ($conn->query($insert_query)) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Tahun akreditasi berhasil ditambahkan',
                        'tahun' => $tahun_baru
                    ]);
                } else {
                    echo json_encode([
                        'success' => false,
                        'message' => 'Gagal menambah tahun: ' . $conn->error
                    ]);
                }
            }
            break;

        default:
            echo json_encode([
                'success' => false,
                'message' => 'Action tidak valid'
            ]);
            break;
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
