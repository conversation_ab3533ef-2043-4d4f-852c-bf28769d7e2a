// Pastikan jQuery tersedia sebelum menjalankan script
function initializeApp() {
    if (typeof jQuery !== 'undefined' && typeof $ !== 'undefined') {
        $(document).ready(function() {
            // Initialize event handlers
            initEventHandlers();
        });
    } else {
        console.error('jQuery is not loaded. Please make sure jQuery is included before this script.');
    }
}

// Try multiple initialization methods
if (typeof jQuery !== 'undefined') {
    initializeApp();
} else {
    // Fallback: wait for window load
    window.addEventListener('load', function() {
        setTimeout(initializeApp, 100); // Small delay to ensure jQuery is loaded
    });
}

/**
 * Initialize all event handlers
 */
function initEventHandlers() {
    // Check if jQuery is available
    if (typeof $ === 'undefined') {
        console.error('jQuery ($) is not available in initEventHandlers');
        return;
    }

    // File input change handler
    $('#file_penjelasan_hasil_akreditasi').on('change', function() {
        updateFileLabel(this);
    });

    // Form submit handlers
    $('#formUploadPHA').on('submit', function(e) {
        e.preventDefault();
        handleFileUpload();
    });

    $('#formCatatanKPA').on('submit', function(e) {
        e.preventDefault();
        simpanCatatanKPA();
    });

    $('#formCatatanVisitasiA').on('submit', function(e) {
        e.preventDefault();
        simpanCatatanVisitasiA();
    });

    $('#formCatatanVisitasiB').on('submit', function(e) {
        e.preventDefault();
        simpanCatatanVisitasiB();
    });

    $('#formCatatanValidator').on('submit', function(e) {
        e.preventDefault();
        simpanCatatanValidator();
    });

    // Modal reset handlers
    $('.modal').on('hidden.bs.modal', function() {
        resetModalState($(this));
    });
}

/**
 * Update file input label
 */
function updateFileLabel(input) {
    const fileName = input.files[0] ? input.files[0].name : 'Pilih file...';
    $(input).next('.custom-file-label').text(fileName);
}

/**
 * Reset modal state when closed
 */
function resetModalState(modal) {
    modal.find('form')[0].reset();
    modal.find('.custom-file-label').text('Pilih file...');
    modal.find('#uploadProgress').hide();
    modal.find('#progressBar').css('width', '0%');
    modal.find('#progressText').text('0%');
    modal.find('button[type="submit"]').prop('disabled', false);
}

/**
 * Show upload modal for PHA file
 */
function uploadFilePHA(idMapping) {
    $('#upload_id_mapping').val(idMapping);
    $('#modalUploadPHA').modal('show');
}

/**
 * Handle file upload with progress bar
 */
function handleFileUpload() {
    const formData = new FormData($('#formUploadPHA')[0]);
    const uploadBtn = $('#btnUploadPHA');
    const progressContainer = $('#uploadProgress');
    const progressBar = $('#progressBar');
    const progressText = $('#progressText');


    
    // Validate file
    const fileInput = $('#file_penjelasan_hasil_akreditasi')[0];
    if (!fileInput.files.length) {
        showAlert('error', 'Silahkan pilih file terlebih dahulu');
        return;
    }

    // Validate file type
    const file = fileInput.files[0];
    if (file.type !== 'application/pdf') {
        showAlert('error', 'File harus berformat PDF');
        return;
    }

    // Validate file size (10MB)
    if (file.size > 10 * 1024 * 1024) {
        showAlert('error', 'Ukuran file maksimal 10MB');
        return;
    }
    
    // Show progress bar
    progressContainer.show();
    progressBar.css('width', '0%');
    progressText.text('0%');
    
    // Disable upload button
    uploadBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Uploading...');
    
    // AJAX upload with progress
    $.ajax({
        url: 'ajax/upload_file_penjelasan_hasil_akreditasi.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            
            // Upload progress
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 100);
                    progressBar.css('width', percentComplete + '%');
                    progressText.text(percentComplete + '%');
                }
            }, false);
            
            return xhr;
        },
        success: function(response) {
            if (response.success) {
                // Progress bar complete
                progressBar.removeClass('progress-bar-striped progress-bar-animated')
                          .addClass('bg-success');
                progressText.text('Complete!');

                // Notifikasi success dihilangkan

                // KISS: Update UI sederhana
                updatePHABadgeStatus(response.data.id_mapping, true, response.data.filename);

                // Auto close modal after 1 second
                setTimeout(function() {
                    $('#modalUploadPHA').modal('hide');
                }, 1000);

            } else {
                // Error
                progressBar.removeClass('progress-bar-striped progress-bar-animated')
                          .addClass('bg-danger');
                progressText.text('Failed!');

                // Notifikasi error dihilangkan

                // Re-enable upload button
                uploadBtn.prop('disabled', false).html('<i class="fas fa-upload"></i> Upload File');
            }
        },
        error: function(xhr, status, error) {
            // AJAX Error
            progressBar.removeClass('progress-bar-striped progress-bar-animated')
                      .addClass('bg-danger');
            progressText.text('Error!');

            // Notifikasi error dihilangkan

            // Re-enable upload button
            uploadBtn.prop('disabled', false).html('<i class="fas fa-upload"></i> Upload File');
        },
        complete: function() {
            // Reset button state
            uploadBtn.prop('disabled', false).html('<i class="fas fa-upload"></i> Upload File');
            progressContainer.hide();
        }
    });
}

/**
 * Show form catatan KPA
 */
function formCatatanKPA(button) {
    const idMapping = $(button).data('id-mapping');
    // Coba ambil dari data() method dulu, kalau tidak ada baru dari attr()
    let namaAsesor = $(button).data('nama-asesor') || $(button).attr('data-nama-asesor') || '';
    let catatanPenilaian = $(button).data('catatan-penilaian') || $(button).attr('data-catatan-penilaian') || '';

    console.log('Opening KPA form with data:', {idMapping, namaAsesor, catatanPenilaian});

    $('#kpa_id_mapping').val(idMapping);
    $('#nama_asesor_kpa').val(namaAsesor);
    $('#catatan_penilaian_asesor_kpa').val(catatanPenilaian);
    $('#modalCatatanKPA').modal('show');
}

/**
 * Show form catatan visitasi A
 */
function formCatatanVisitasiA(button) {
    const idMapping = $(button).data('id-mapping');
    // Coba ambil dari data() method dulu, kalau tidak ada baru dari attr()
    let namaAsesor = $(button).data('nama-asesor') || $(button).attr('data-nama-asesor') || '';
    let catatanPenilaian = $(button).data('catatan-penilaian') || $(button).attr('data-catatan-penilaian') || '';

    console.log('Opening Visitasi A form with data:', {idMapping, namaAsesor, catatanPenilaian});

    $('#visitasi_a_id_mapping').val(idMapping);
    $('#nama_asesor_visitasi_a').val(namaAsesor);
    $('#catatan_penilaian_asesor_visitasi_a').val(catatanPenilaian);
    $('#modalCatatanVisitasiA').modal('show');
}

/**
 * Show form catatan visitasi B
 */
function formCatatanVisitasiB(button) {
    const idMapping = $(button).data('id-mapping');
    // Coba ambil dari data() method dulu, kalau tidak ada baru dari attr()
    let namaAsesor = $(button).data('nama-asesor') || $(button).attr('data-nama-asesor') || '';
    let catatanPenilaian = $(button).data('catatan-penilaian') || $(button).attr('data-catatan-penilaian') || '';

    console.log('Opening Visitasi B form with data:', {idMapping, namaAsesor, catatanPenilaian});

    $('#visitasi_b_id_mapping').val(idMapping);
    $('#nama_asesor_visitasi_b').val(namaAsesor);
    $('#catatan_penilaian_asesor_visitasi_b').val(catatanPenilaian);
    $('#modalCatatanVisitasiB').modal('show');
}

/**
 * Show form catatan validator
 */
function formCatatanValidator(button) {
    const idMapping = $(button).data('id-mapping');
    // Coba ambil dari data() method dulu, kalau tidak ada baru dari attr()
    let namaValidator = $(button).data('nama-validator') || $(button).attr('data-nama-validator') || '';
    let nilaiValidasi = $(button).data('nilai-validasi') || $(button).attr('data-nilai-validasi') || '';
    let catatanPenilaian = $(button).data('catatan-penilaian') || $(button).attr('data-catatan-penilaian') || '';

    console.log('Opening Validator form with data:', {idMapping, namaValidator, nilaiValidasi, catatanPenilaian});

    $('#validator_id_mapping').val(idMapping);
    $('#nama_validator').val(namaValidator);
    $('#nilai_validasi').val(nilaiValidasi);
    $('#catatan_penilaian_validator').val(catatanPenilaian);
    $('#modalCatatanValidator').modal('show');
}

/**
 * Preview file in new tab
 */
function previewFile(filename) {
    if (!filename || filename.trim() === '') {
        showAlert('error', 'Nama file tidak valid');
        return;
    }

    const filePath = '../../../../simak/files/upload_file_hasil_validasi_paud/' + filename;
    const newWindow = window.open(filePath, '_blank');

    if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
        showAlert('warning', 'Popup diblokir oleh browser. Silakan izinkan popup untuk membuka file.');
        return;
    }
}

/**
 * Save catatan KPA
 */
function simpanCatatanKPA() {
    const formData = $('#formCatatanKPA').serialize();
    const submitBtn = $('#btnSimpanKPA');
    
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
    
    $.ajax({
        url: 'ajax/simpan_catatan_kpa.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            console.log('Response:', response); // Debug
            if (response.success) {
                // Hanya tampilkan toast notification, tidak ada alert
                $('#modalCatatanKPA').modal('hide');
                // Update button data attributes dengan data baru
                updateButtonDataKPA();
                // Tampilkan feedback toast
                showUpdateFeedback('KPA');
            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', xhr.responseText);
            console.error('Status:', status);
            console.error('Error:', error);
            showAlert('error', 'Terjadi kesalahan saat menyimpan data: ' + error);
        },
        complete: function() {
            submitBtn.prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Catatan');
        }
    });
}

/**
 * Update button data KPA setelah save
 */
function updateButtonDataKPA() {
    const idMapping = $('#kpa_id_mapping').val();
    const namaAsesor = $('#nama_asesor_kpa').val();
    const catatanPenilaian = $('#catatan_penilaian_asesor_kpa').val();

    console.log('Updating KPA button data:', {idMapping, namaAsesor, catatanPenilaian});

    // Update data attributes pada button yang sesuai dengan selector yang lebih spesifik
    $(`button[data-id-mapping="${idMapping}"]`).each(function() {
        const buttonText = $(this).text();
        console.log('Checking button:', buttonText);

        if (buttonText.includes('Form Catatan Asesor KPA')) {
            // Update menggunakan data() method dan attr() method
            $(this).data('nama-asesor', namaAsesor);
            $(this).data('catatan-penilaian', catatanPenilaian);
            $(this).attr('data-nama-asesor', namaAsesor);
            $(this).attr('data-catatan-penilaian', catatanPenilaian);

            // Visual feedback - ubah warna button jika ada data
            if (namaAsesor.trim() !== '' || catatanPenilaian.trim() !== '') {
                $(this).html('<i class="fas fa-edit"></i> Form Catatan Asesor KPA <i class="fas fa-check-circle text-white ml-1"></i>');
            }

            console.log('Successfully updated KPA button data for mapping:', idMapping);
            console.log('New data attributes:', {
                'data-nama-asesor': $(this).attr('data-nama-asesor'),
                'data-catatan-penilaian': $(this).attr('data-catatan-penilaian')
            });

            // Flash animation untuk feedback visual
            $(this).addClass('btn-outline-success').removeClass('btn-success');
            setTimeout(() => {
                $(this).removeClass('btn-outline-success').addClass('btn-success');
            }, 1000);
        }
    });
}

/**
 * Save catatan visitasi A
 */
function simpanCatatanVisitasiA() {
    const formData = $('#formCatatanVisitasiA').serialize();
    const submitBtn = $('#btnSimpanVisitasiA');
    
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
    
    $.ajax({
        url: 'ajax/simpan_catatan_visitasi_a.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            console.log('Response Visitasi A:', response); // Debug
            if (response.success) {
                // Hanya tampilkan toast notification, tidak ada alert
                $('#modalCatatanVisitasiA').modal('hide');
                // Update button data attributes dengan data baru
                updateButtonDataVisitasiA();
                // Tampilkan feedback toast
                showUpdateFeedback('VisitasiA');
            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', xhr.responseText);
            console.error('Status:', status);
            console.error('Error:', error);
            showAlert('error', 'Terjadi kesalahan saat menyimpan data: ' + error);
        },
        complete: function() {
            submitBtn.prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Catatan');
        }
    });
}

/**
 * Save catatan visitasi B
 */
function simpanCatatanVisitasiB() {
    const formData = $('#formCatatanVisitasiB').serialize();
    const submitBtn = $('#btnSimpanVisitasiB');

    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    $.ajax({
        url: 'ajax/simpan_catatan_visitasi_b.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            console.log('Response Visitasi B:', response); // Debug
            if (response.success) {
                // Hanya tampilkan toast notification, tidak ada alert
                $('#modalCatatanVisitasiB').modal('hide');
                // Update button data attributes dengan data baru
                updateButtonDataVisitasiB();
                // Tampilkan feedback toast
                showUpdateFeedback('VisitasiB');
            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', xhr.responseText);
            console.error('Status:', status);
            console.error('Error:', error);
            showAlert('error', 'Terjadi kesalahan saat menyimpan data: ' + error);
        },
        complete: function() {
            submitBtn.prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Catatan');
        }
    });
}

/**
 * Save catatan validator
 */
function simpanCatatanValidator() {
    const formData = $('#formCatatanValidator').serialize();
    const submitBtn = $('#btnSimpanValidator');

    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    $.ajax({
        url: 'ajax/simpan_catatan_validator.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            console.log('Response Validator:', response); // Debug
            if (response.success) {
                // Hanya tampilkan toast notification, tidak ada alert
                $('#modalCatatanValidator').modal('hide');
                // Update button data attributes dengan data baru
                updateButtonDataValidator();
                // Tampilkan feedback toast
                showUpdateFeedback('Validator');
            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', xhr.responseText);
            console.error('Status:', status);
            console.error('Error:', error);
            showAlert('error', 'Terjadi kesalahan saat menyimpan data: ' + error);
        },
        complete: function() {
            submitBtn.prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Catatan');
        }
    });
}

/**
 * Show alert message
 */
function showAlert(type, message) {
    let alertClass = 'alert-info';
    let icon = 'fas fa-info-circle';

    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            icon = 'fas fa-check-circle';
            break;
        case 'error':
            alertClass = 'alert-danger';
            icon = 'fas fa-exclamation-circle';
            break;
        case 'warning':
            alertClass = 'alert-warning';
            icon = 'fas fa-exclamation-triangle';
            break;
    }

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="${icon}"></i> ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;

    // Remove existing alerts
    $('.alert').remove();

    // Add new alert at the top of content
    $('.content-wrapper .content').prepend(alertHtml);

    // Auto hide after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Scroll to top to show alert
    $('html, body').animate({
        scrollTop: $('.content-wrapper').offset().top
    }, 500);
}

/**
 * Update button data Visitasi A setelah save
 */
function updateButtonDataVisitasiA() {
    const idMapping = $('#visitasi_a_id_mapping').val();
    const namaAsesor = $('#nama_asesor_visitasi_a').val();
    const catatanPenilaian = $('#catatan_penilaian_asesor_visitasi_a').val();

    console.log('Updating Visitasi A button data:', {idMapping, namaAsesor, catatanPenilaian});

    // Update data attributes pada button yang sesuai
    $(`button[data-id-mapping="${idMapping}"]`).each(function() {
        if ($(this).text().includes('Form Catatan Visitasi A')) {
            // Update menggunakan data() method dan attr() method
            $(this).data('nama-asesor', namaAsesor);
            $(this).data('catatan-penilaian', catatanPenilaian);
            $(this).attr('data-nama-asesor', namaAsesor);
            $(this).attr('data-catatan-penilaian', catatanPenilaian);

            // Visual feedback - ubah warna button jika ada data
            if (namaAsesor.trim() !== '' || catatanPenilaian.trim() !== '') {
                $(this).html('<i class="fas fa-edit"></i> Form Catatan Visitasi A <i class="fas fa-check-circle text-white ml-1"></i>');
            }

            console.log('Successfully updated Visitasi A button data for mapping:', idMapping);

            // Flash animation untuk feedback visual
            $(this).addClass('btn-outline-info').removeClass('btn-info');
            setTimeout(() => {
                $(this).removeClass('btn-outline-info').addClass('btn-info');
            }, 1000);
        }
    });
}

/**
 * Update button data Visitasi B setelah save
 */
function updateButtonDataVisitasiB() {
    const idMapping = $('#visitasi_b_id_mapping').val();
    const namaAsesor = $('#nama_asesor_visitasi_b').val();
    const catatanPenilaian = $('#catatan_penilaian_asesor_visitasi_b').val();

    console.log('Updating Visitasi B button data:', {idMapping, namaAsesor, catatanPenilaian});

    // Update data attributes pada button yang sesuai
    $(`button[data-id-mapping="${idMapping}"]`).each(function() {
        if ($(this).text().includes('Form Catatan Visitasi B')) {
            // Update menggunakan data() method dan attr() method
            $(this).data('nama-asesor', namaAsesor);
            $(this).data('catatan-penilaian', catatanPenilaian);
            $(this).attr('data-nama-asesor', namaAsesor);
            $(this).attr('data-catatan-penilaian', catatanPenilaian);

            // Visual feedback - ubah warna button jika ada data
            if (namaAsesor.trim() !== '' || catatanPenilaian.trim() !== '') {
                $(this).html('<i class="fas fa-edit"></i> Form Catatan Visitasi B <i class="fas fa-check-circle text-white ml-1"></i>');
            }

            console.log('Successfully updated Visitasi B button data for mapping:', idMapping);

            // Flash animation untuk feedback visual
            $(this).addClass('btn-outline-warning').removeClass('btn-warning');
            setTimeout(() => {
                $(this).removeClass('btn-outline-warning').addClass('btn-warning');
            }, 1000);
        }
    });
}

/**
 * Update button data Validator setelah save
 */
function updateButtonDataValidator() {
    const idMapping = $('#validator_id_mapping').val();
    const namaValidator = $('#nama_validator').val();
    const nilaiValidasi = $('#nilai_validasi').val();
    const catatanPenilaian = $('#catatan_penilaian_validator').val();

    console.log('Updating Validator button data:', {idMapping, namaValidator, nilaiValidasi, catatanPenilaian});

    // Update data attributes pada button yang sesuai
    $(`button[data-id-mapping="${idMapping}"]`).each(function() {
        if ($(this).text().includes('Form Catatan Validator')) {
            // Update menggunakan data() method dan attr() method
            $(this).data('nama-validator', namaValidator);
            $(this).data('nilai-validasi', nilaiValidasi);
            $(this).data('catatan-penilaian', catatanPenilaian);
            $(this).attr('data-nama-validator', namaValidator);
            $(this).attr('data-nilai-validasi', nilaiValidasi);
            $(this).attr('data-catatan-penilaian', catatanPenilaian);

            // Visual feedback - ubah warna button jika ada data
            if (namaValidator.trim() !== '' || nilaiValidasi.trim() !== '' || catatanPenilaian.trim() !== '') {
                $(this).html('<i class="fas fa-edit"></i> Form Catatan Validator <i class="fas fa-check-circle text-white ml-1"></i>');
            }

            console.log('Successfully updated Validator button data for mapping:', idMapping);

            // Flash animation untuk feedback visual
            $(this).addClass('btn-outline-secondary').removeClass('btn-secondary');
            setTimeout(() => {
                $(this).removeClass('btn-outline-secondary').addClass('btn-secondary');
            }, 1000);
        }
    });
}

/**
 * KISS: Update PHA badge status setelah upload
 */
function updatePHABadgeStatus(idMapping, uploaded, filename) {
    console.log('Updating PHA badge for ID:', idMapping, 'Uploaded:', uploaded, 'Filename:', filename);

    // KISS: Cari badge di kolom terakhir untuk row yang mengandung button upload dengan ID mapping ini
    $('button[onclick="uploadFilePHA(\'' + idMapping + '\')"]').closest('tr').find('td:last-child .badge').each(function() {
        console.log('Found badge to update:', $(this));

        if (uploaded) {
            // KISS: Update ke status "Sudah Upload" dengan onclick event
            $(this).removeClass('badge-danger')
                   .addClass('badge-success')
                   .html('<i class="fas fa-eye"></i> Sudah Upload')
                   .css('cursor', 'pointer')
                   .attr('onclick', 'previewFile("' + filename + '")');

            console.log('Badge updated to success with onclick event for file:', filename);
        }
    });
}

/**
 * Show update feedback dengan toast notification
 */
function showUpdateFeedback(type) {
    const messages = {
        'KPA': 'Data Catatan Asesor KPA berhasil diperbarui!',
        'VisitasiA': 'Data Catatan Visitasi A berhasil diperbarui!',
        'VisitasiB': 'Data Catatan Visitasi B berhasil diperbarui!',
        'Validator': 'Data Catatan Validator berhasil diperbarui!',
        'PHA': 'File PHA berhasil diupload!'
    };

    // Create toast notification
    const toast = $(`
        <div class="toast-notification" style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 9999;
            font-size: 14px;
            max-width: 300px;
        ">
            <i class="fas fa-check-circle mr-2"></i>
            ${messages[type] || 'Data berhasil diperbarui!'}
        </div>
    `);

    // Add to body
    $('body').append(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
        toast.fadeOut(500, function() {
            $(this).remove();
        });
    }, 3000);
}
