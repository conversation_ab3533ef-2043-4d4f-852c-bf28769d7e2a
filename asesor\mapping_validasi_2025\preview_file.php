<?php
/**
 * File preview handler dengan security check - men<PERSON><PERSON><PERSON> pola KPA PAUD
 */

require_once '../../koneksi.php';
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');

// Get parameters
$filename = $_GET['file'] ?? '';

if (empty($filename)) {
    http_response_code(400);
    die('File parameter required');
}

// Security: Validate filename (no path traversal) - mengikuti pola KPA PAUD
if (strpos($filename, '..') !== false || strpos($filename, '/') !== false || strpos($filename, '\\') !== false) {
    http_response_code(403);
    die('Invalid filename');
}

// Get session variables
$kd_user = $_SESSION['kd_user'] ?? '';
$provinsi_id = $_SESSION['provinsi_id'] ?? '';

// Define possible file directories - mengi<PERSON><PERSON> pola KPA PAUD
$file_directories = array(
    '../../../simak/files/upload_file_format_5_1_berita_acara_hasil_validasi_1/',
    '../../../simak/files/upload_file_format_5_1_berita_acara_hasil_validasi_2/'
);

$file_found = false;
$file_path = '';

// Search for file in all possible directories
foreach ($file_directories as $dir) {
    $potential_path = $dir . $filename;
    if (file_exists($potential_path)) {
        $file_path = $potential_path;
        $file_found = true;
        break;
    }
}

if (!$file_found) {
    http_response_code(404);
    die('File not found on server');
}

// Verify file belongs to current asesor - mengikuti pola KPA PAUD
$check_query = "SELECT mv.file_format_5_1_berita_acara_hasil_validasi_1,
                       mv.file_format_5_1_berita_acara_hasil_validasi_2
                FROM mapping_validasi_2025 mv
                WHERE (mv.kd_asesor1 = '$kd_user' OR mv.kd_asesor2 = '$kd_user')
                  AND mv.provinsi_id = '$provinsi_id'
                  AND (mv.file_format_5_1_berita_acara_hasil_validasi_1 = '$filename'
                       OR mv.file_format_5_1_berita_acara_hasil_validasi_2 = '$filename')";

$check_result = $conn->query($check_query);

if (!$check_result || $check_result->num_rows === 0) {
    http_response_code(403);
    die('Access denied: File not found or you do not have permission to view this file');
}

// Verify file type - Alternative method tanpa finfo
$file_extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

if ($file_extension !== 'pdf') {
    die('File bukan format PDF');
}

// Additional validation: Check PDF magic bytes
$handle = fopen($file_path, 'r');
$first_bytes = fread($handle, 4);
fclose($handle);

if (substr($first_bytes, 0, 4) !== '%PDF') {
    die('File bukan format PDF yang valid');
}

// Set appropriate headers for PDF display - mengikuti pola KPA PAUD
header('Content-Type: application/pdf');
header('Content-Disposition: inline; filename="' . basename($filename) . '"');
header('Content-Length: ' . filesize($file_path));
header('Cache-Control: private, max-age=0, must-revalidate');
header('Pragma: public');

// Clear any previous output
if (ob_get_level()) {
    ob_end_clean();
}

// Output file
readfile($file_path);
exit;
?>
