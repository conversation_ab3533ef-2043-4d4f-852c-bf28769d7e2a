Berikut ini adalah struktur tabel "mapping_paud_validasi" :
id_mapping  int(11)
sekolah_id  int(11)
kd_asesor1  varchar(25)
kd_asesor2  varchar(25)
tahap int(11)
tahun_akreditasi  varchar(4)
file_penjelasan_hasil_akreditasi  varchar(50)
nama_asesor_kpa varchar(50)
catatan_penilaian_asesor_kpa  text 
nama_asesor_visitasi_a  varchar(50)
catatan_penilaian_asesor_visitasi_a text 
nama_asesor_visitasi_b  varchar(50)
catatan_penilaian_asesor_visitasi_b text 
nama_validator  varchar(50)
nilai_validasi  varchar(50)
catatan_penilaian_validator text
pha text
provinsi_id int(11)


Berikut ini struktur tabel "sekolah" :
sekolah_id int(11)
nama_sekolah varchar(100)
npsn varchar(30)
jenjang_id int(1)
rumpun varchar(20)
alamat text
tipe_sekolah_id int(1)
status_sekolah_id int(1)
provinsi_id int(2)
kota_id int(6)
desa_kel<PERSON>han varchar(200)
kecamatan varchar(100)
nama_kepsek varchar(100)
no_hp_kepsek varchar(50)
no_wa_kepsek varchar(50)
nama_operator varchar(50)
no_hp_operator varchar(20)
no_wa_operator varchar(20)
email varchar(100)
nama_yayasan varchar(200)
no_akte varchar(50)
tahun_berdiri varchar(4)
status_keaktifan_id varchar(1)
soft_delete rchar(1)


Berikut ini adalah struktur tabel "jenjang" :
id_jenjang int(11)
jenjang_id varchar(2)
nm_jenjang varchar(15)


Berikut ini struktur tabel "kab_kota" :
id_kota	int(11)	
kota_id	varchar(10)
nm_kota	varchar(50)
provinsi_id	int(11)
kd_user	varchar(25)


Berikut ini struktur tabel "asesor_1" :
id_asesor1 int(11)
kd_asesor1 varchar(25)
nia1 varchar(20) 
nm_asesor1  varchar(100) 
ktp varchar(20) 
unit_kerja  varchar(300) 
kota_id1 varchar(10)
provinsi_id int(11)
no_sertifikat varchar(30)
no_hp varchar(50)
no_wa varchar(50)
tempat_lahir varchar(30)
tgl_lahir date
jabatan varchar(50)
jabatan_struktural varchar(20)
pendidikan varchar(15)
jenjang_id int(11)
rumpun varchar(7)
grade varchar(1)
jk varchar(10)
alamat_kantor varchar(300)
alamat_rumah varchar(300)
email varchar(50)
thn_terbit_sertifikat date
kegiatan varchar(50)
status_keaktifan_id varchar(1)
sebab text
kd_user varchar(50)
soft_delete varchar(1)

Berikut ini tabel "asesor_2" :
id_asesor2 int(11)
kd_asesor2 varchar(25)
nia2 varchar(20)
nm_asesor2 varchar(100)
ktp varchar(20)
unit_kerja varchar(300)
kota_id2 varchar(10)
provinsi_id int(11)
no_sertifikat varchar(30)
no_hp varchar(50)
no_wa varchar(50)
tempat_lahir varchar(30)
tgl_lahir date
jabatan varchar(50)
jabatan_struktural  varchar(20)
pendidikan varchar(15)
jenjang_id int(11)
rumpun varchar(7)
grade varchar(1)
jk varchar(10)
alamat_kantor varchar(300)
alamat_rumah varchar(300)
email varchar(50)
thn_terbit_sertifikat date
kegiatan varchar(50)
status_keaktifan_id varchar(1)
sebab text
kd_user varchar(50)
soft_delete varchar(1)


Berikut ini struktur tabel "mapping_paud_validasi_tahun" :
id_mapping_tahun	int(11)
nama_tahun	int(4)
provinsi_id	int(11)

====================================================================================================================

buatlah modul "Mapping Validasi Paud" pada direktori "asesor/mapping_paud_validasi_2020/validasi_paud_2020.php", juga tersedia sub direktori ajax dan js, terdapat 5 kolom dengan tabel header yang digunakan adalah :

- NO autoincrement;

- SEKOLAH, pada tabel body berisi data sebagai berikut : 
  NPSN : sekolah.npsn, NAMA : sekolah.nama_sekolah,
  JENJANG : jenjang.jenjang_id=sekolah.jenjang_id (jenjang.nm_jenjang),
  KAB/KOTA : sekolah.kota_id = kab_kota.kota_id (kab_kota.nm_kota),
  NAMA KEPSEK : sekolah.nama_kepsek,
  NO HP KEPSEK : sekolah.no_hp_kepsek,
  TAHAP VISITASI : mapping_paud_validasi.tahap

- ASESOR, pada tabel body berisi 
  VALIDATOR (colspan =2), NIA : asesor_1.nia1, NAMA : asesor_1.nm_asesor1, KAB/KOTA : asesor_1.kota_id1 = kab_kota.kota_id (kab_kota.nm_kota),
  VERIFIKATOR (colspan =2), NIA : asesor_2.nia2, NAMA : asesor_2.nm_asesor2, KAB/KOTA : asesor_2.kota_id2 = kab_kota.kota_id (kab_kota.nm_kota);

- FORM UPLOAD DOKUMEN

  Tombol "Upload File Penjelasan Hasil Akreditasi (PHA)" hanya tampil jika session login $kd_user==$asesor1, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_hasil_validasi_paud/"

  Tombol "Form Catatan Peilaian Terhadap Asesor KPA" hanya tampil jika session login $kd_user==$asesor1, jika di-klik akan menampilkan modal "Catatan Penilaian Terhadap Asesor KPA" untuk mengisi field mapping_paud_validasi.nama_asesor_kpa dan field mapping_paud_validasi.catatan_penilaian_asesor_kpa

  Tombol "Form Catatan Penilaian Asesor Visitasi A" hanya tampil jika session login $kd_user==$asesor1, jika di-klik akan menampilkan modal "Catatan Penilaian Asesor Visitasi A" untuk mengisi field mapping_paud_validasi.nama_asesor_visitasi_a dan field mapping_paud_validasi.catatan_penilaian_asesor_visitasi_a

  Tombol "Form Catatan Penilaian Asesor Visitasi B" session login $kd_user==$asesor1, ika di-klik akan menampilkan modal "Catatan Penilaian Asesor Visitasi B" untuk mengisi field mapping_paud_validasi.nama_asesor_visitasi_b dan field mapping_paud_validasi.catatan_penilaian_asesor_visitasi_b

  Tombol "Form Catatan Penilaian Validator" hanya tampil jika session login $kd_user==$asesor2, jika di-klik akan menampilkan modal "Catatan Penilaian Terhadap Validator", untuk mengisi field mapping_paud_validasi.nama_validator dan mapping_paud_validasi.nilai_validasi dan mapping_paud_validasi.catatan_penilaian_validator

- DOKUMEN PENJELASAN HASIL AKREDITASI (PHA)
  label "File Penjelasan Hasil Akreditasi (PHA) :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_validasi.file_penjelasan_hasil_akreditasi ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

oh iya lupa, tambahkan
<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Asesor
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');
?>

// Include header
<?php include '../header.php'; ?>


<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

terakhir di paling bawah <!-- Include footer -->
<?php include '../footer.php'; ?>

berikut di bawah ini query sql yang bisa anda gunakan untuk menampilkan data:

$tahun = "SELECT mapping_paud_validasi_tahun.nama_tahun FROM mapping_paud_validasi_tahun WHERE mapping_paud_validasi_tahun.provinsi_id='$provinsi_id' ";
$result_tahun = $conn->query($tahun);
if($result_tahun->num_rows > 0) {
while($row_tahun = $result_tahun->fetch_assoc()) {
    $nama_tahun = $row_tahun['nama_tahun'];


$sql = "SELECT mapping_paud_validasi.id_mapping, sekolah.sekolah_id, sekolah.npsn, sekolah.nama_sekolah,
        sekolah.nama_kepsek, sekolah.no_hp_kepsek, sekolah.no_wa_kepsek, sekolah.jenjang_id, jenjang.nm_jenjang,
        sekolah.kota_id, kab_kota.nm_kota, mapping_paud_validasi.kd_asesor1, mapping_paud_validasi.kd_asesor2,
        mapping_paud_validasi.tahap, mapping_paud_validasi.file_penjelasan_hasil_akreditasi,
        mapping_paud_validasi.nama_asesor_kpa,
        mapping_paud_validasi.catatan_penilaian_asesor_kpa,
        mapping_paud_validasi.nama_asesor_visitasi_a,
        mapping_paud_validasi.catatan_penilaian_asesor_visitasi_a,
        mapping_paud_validasi.nama_asesor_visitasi_b,
        mapping_paud_validasi.catatan_penilaian_asesor_visitasi_b,
        mapping_paud_validasi.nama_validator,
        mapping_paud_validasi.nilai_validasi,
        mapping_paud_validasi.catatan_penilaian_validator,
        (SELECT asesor.nia from asesor WHERE mapping_paud_validasi.kd_asesor1=asesor.kd_asesor) as nia1,
        (SELECT asesor.nm_asesor from asesor WHERE mapping_paud_validasi.kd_asesor1=asesor.kd_asesor) as nama1,
        (SELECT asesor.no_hp from asesor WHERE mapping_paud_validasi.kd_asesor1=asesor.kd_asesor) as hp1,
        (SELECT kab_kota.nm_kota from asesor LEFT JOIN kab_kota ON asesor.kota_id=kab_kota.kota_id
            WHERE mapping_paud_validasi.kd_asesor1=asesor.kd_asesor) as kota1,
        (SELECT asesor.nia from asesor WHERE mapping_paud_validasi.kd_asesor2=asesor.kd_asesor) as nia2,
        (SELECT asesor.nm_asesor from asesor WHERE mapping_paud_validasi.kd_asesor2=asesor.kd_asesor) as nama2,
        (SELECT asesor.no_hp from asesor WHERE mapping_paud_validasi.kd_asesor2=asesor.kd_asesor) as hp2,
        (SELECT kab_kota.nm_kota from asesor LEFT JOIN kab_kota ON asesor.kota_id=kab_kota.kota_id
            WHERE mapping_paud_validasi.kd_asesor2=asesor.kd_asesor) as kota2, mapping_paud_validasi.tahun_akreditasi
            FROM mapping_paud_validasi
        LEFT JOIN sekolah ON mapping_paud_validasi.sekolah_id=sekolah.sekolah_id
        LEFT JOIN jenjang ON sekolah.jenjang_id=jenjang.jenjang_id
        LEFT JOIN kab_kota ON sekolah.kota_id=kab_kota.kota_id
        WHERE (kd_asesor1='$kd_user' OR kd_asesor2='$kd_user')
        AND mapping_paud_validasi.tahun_akreditasi='$nama_tahun'
        AND mapping_paud_validasi.provinsi_id='$provinsi_id'  ";
$result = $conn->query($sql);
if($result->num_rows > 0) {
while($row = $result->fetch_assoc()) {
$nomor++;
$asesor1                              = $row['kd_asesor1'];
$asesor2                              = $row['kd_asesor2'];
$file_penjelasan_hasil_akreditasi     = $row['file_penjelasan_hasil_akreditasi'];
$nama_asesor_kpa                      = $row['nama_asesor_kpa'];
$catatan_penilaian_asesor_kpa         = $row['catatan_penilaian_asesor_kpa'];
$nama_asesor_visitasi_a               = $row['nama_asesor_visitasi_a'];
$catatan_penilaian_asesor_visitasi_a  = $row['catatan_penilaian_asesor_visitasi_a'];
$nama_asesor_visitasi_b               = $row['nama_asesor_visitasi_b'];
$catatan_penilaian_asesor_visitasi_b  = $row['catatan_penilaian_asesor_visitasi_b'];
$nama_validator                       = $row['nama_validator'];
$nilai_validasi                       = $row['nilai_validasi'];
$catatan_penilaian_validator          = $row['catatan_penilaian_validator'];

sampai disini apakah anda mengerti dengan apa yang saya maksudkan?
jika ada pertanyaan yang ingin ditanyakan dipersilahkan

=================================================================================================================

mantap kawanku yang cerdas, its work, sekarang kita lanjut untuk menampilkan modal "Unggah File" yang dimana jika tombol tersebut di-klik akan menampilkan modal yang berisi form untuk input file "Hasil KPA" berupa file PDF, file yang diunggah akan tersimpan pada direktori "../../../../simak/files/upload_file_hasil_kpa/"

berikut contoh kode sumber php untuk tanggal dan jam
<?php 
  date_default_timezone_set('Asia/Singapore');
  $tanggal= mktime(date("m"),date("d"),date("Y"));
  $tglAwal = date("Y-m-d", $tanggal);
  $tglAkhir = date("Y-m-d", $tanggal);
  $tglsekarang = date("Y-m-d", $tanggal);
  $jam=date("H:i:s");
?>

berikut contoh form:
<input name="tgl_file_hasil_kpa" id="tgl_file_hasil_kpa" type="hidden" value="<?php echo $tglsekarang; ?>" />
<input name="jam_file_hasil_kpa" id="jam_file_hasil_kpa" type="hidden" value="<?php echo $jam; ?>" />
<label for="file_laporan_hasil_kpa">Upload File Laporan Hasil KPA</label><br>
<input type="file" id="file_laporan_hasil_kpa" name="file_laporan_hasil_kpa" >

berikut contoh kode sumber php yang bisa anda contoh 
<?php 
  $id_mapping             = $_POST['id_mapping'];
  $file_laporan_hasil_kpa = htmlspecialchars(addslashes($_FILES['file_laporan_hasil_kpa']['name']));
  $tgl_file_hasil_kpa  = $_POST['tgl_file_hasil_kpa'];
  $jam_file_hasil_kpa  = $_POST['jam_file_hasil_kpa'];


    $sqlfile = "SELECT * FROM mapping_paud_kpa WHERE id_mapping = '$id_mapping' ";
    $resultfile = mysqli_query($conn, $sqlfile);
    $row = mysqli_fetch_array($resultfile);
    
    //hapus gambar
    $folder="../files/upload_file_hasil_kpa/$row[file_laporan_hasil_kpa]";
    unlink($folder);
    
    //input file
    $temp = explode('.', $_FILES['file_laporan_hasil_kpa']['name']);
    $nama_baru = round(microtime(true)) . '.' . end($temp);
    move_uploaded_file($_FILES['file_laporan_hasil_kpa']['tmp_name'], '../../../../simak/files/upload_file_hasil_kpa/'.$nama_baru);

    $sqlupdate = "UPDATE mapping_paud_kpa SET file_laporan_hasil_kpa ='$nama_baru', tgl_file_hasil_kpa ='$tgl_file_hasil_kpa', jam_file_hasil_kpa ='$jam_file_hasil_kpa'
    WHERE id_mapping='$id_mapping' ";
?>

proses upload file "file_laporan_hasil_kpa" terjadi tanpa refresh browser (tanpa reload halaman) dan terjadi secara real-time pada perubahan tombol di kolom "PREVIEW" menjadi "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_validasi.file_laporan_hasil_kpa berhasil terunggah

sampai disini apakah anda mengerti dengan apa yang saya maksudkan?
jika ada pertanyaan yang ingin ditanyakan dipersilahkan

=================================================================================================================

mantap kawanku yang sangat smart dan brilian, kita ke perbaikan lainnya, yaitu  jika mapping_paud_validasi.tgl_mulai_visitasi=0000-00-00 pada tabel di database maka pada kolom "JADWAL DAN AKSI" pada "Tanggal Visitasi" tampilkan tulisan "Tanggal visitasi belum diisi oleh asesor" namun jika mapping_paud_validasi.tgl_mulai_visitasi != 0000-00-00 maka tampilkan mapping_paud_validasi.tgl_mulai_visitasi,

kemudian jika mapping_paud_validasi.tgl_mulai_visitasi = 0000-00-00 maka tampilkan tombol "Input Tanggal Visitasi" dan tombol "Download Surat Tugas" di-hidden namun jika mapping_paud_validasi.tgl_mulai_visitasi != 0000-00-00 maka tombol "Input Tanggal Visitasi" di-hidden dan tombol "Download Surat Tugas" ditampilkan,

terakhir tampilkan tombol "Input Tanggal Visitasi" dan tombol "Download Surat Tugas" di kedua asesor ($kd_user==$asesor1 dan atau $kd_user==$asesor2)


ketika saya login sebagai asesor 2 ($kd_user==$asesor2) lalu mengisi tanggal visitasi, tampil pesan "Error! Data mapping tidak ditemukan atau Anda tidak memiliki akses untuk input tanggal", seharusnya tidak tampil pesan tersebut karena $kd_user==$asesor1 dan atau $kd_user==$asesor2 boleh mengisi tanggal visitasi
