Berikut ini adalah struktur tabel "mapping_2024" :
id_mapping  int(11)
sekolah_id  int(11)
kd_asesor1  varchar(25)
kd_asesor2  varchar(25)
tgl_pra_visitasi  date
tgl_surat_tugas_pra_visitasi  date
no_surat_tugas_pra_visitasi varchar(30)
tgl_mulai_visitasi  date
tgl_akhir_visitasi  date
tgl_surat_tugas_visitasi  date
no_surat_tugas_visitasi varchar(30)
tahap int(11)
tahun_akreditasi  varchar(4)
file_format_3_1_hasil_penilaian_pra_visitasi_1  varchar(50)
file_format_3_1_hasil_penilaian_pra_visitasi_2  varchar(50)
file_format_3_2_lk_penggalian_data_pra_visitasi_1 varchar(50)
file_format_3_2_lk_penggalian_data_pra_visitasi_2 varchar(50)
file_format_4_1_surat_tugas_visitasi  varchar(50)
file_format_4_2_pakta_integritas_1  varchar(50)
file_format_4_2_pakta_integritas_2  varchar(50)
file_format_4_3_lembar_rekap_penggalian_data_penilaian_1  varchar(50)
file_format_4_3_lembar_rekap_penggalian_data_penilaian_2  varchar(50)
file_format_4_4_berita_acara_visitasi varchar(50)
file_format_4_5_laporan_individu_1  varchar(50)
file_format_4_5_laporan_individu_2  varchar(50)
file_format_4_5_laporan_kelompok  varchar(50)
file_format_4_5_catatan_dan_saran varchar(50)
file_foto_visitasi_2024 varchar(50)
provinsi_id int(11)

Berikut ini struktur tabel "sekolah" :
sekolah_id int(11)
nama_sekolah varchar(100)
npsn varchar(30)
jenjang_id int(1)
rumpun varchar(20)
alamat text
tipe_sekolah_id int(1)
status_sekolah_id int(1)
provinsi_id int(2)
kota_id int(6)
desa_kelurahan varchar(200)
kecamatan varchar(100)
nama_kepsek varchar(100)
no_hp_kepsek varchar(50)
no_wa_kepsek varchar(50)
nama_operator varchar(50)
no_hp_operator varchar(20)
no_wa_operator varchar(20)
email varchar(100)
nama_yayasan varchar(200)
no_akte varchar(50)
tahun_berdiri varchar(4)
status_keaktifan_id varchar(1)
soft_delete rchar(1)

Berikut ini adalah struktur tabel "jenjang" :
id_jenjang int(11)
jenjang_id varchar(2)
nm_jenjang varchar(15)

Berikut ini struktur tabel "kab_kota" :
id_kota int(11) 
kota_id varchar(10)
nm_kota varchar(50)
provinsi_id int(11)
kd_user varchar(25)

Berikut ini struktur tabel "asesor_1" :
id_asesor1 int(11)
kd_asesor1 varchar(25)
nia1 varchar(20) 
nm_asesor1  varchar(100) 
ktp varchar(20) 
unit_kerja  varchar(300) 
kota_id1 varchar(10)
provinsi_id int(11)
no_sertifikat varchar(30)
no_hp varchar(50)
no_wa varchar(50)
tempat_lahir varchar(30)
tgl_lahir date
jabatan varchar(50)
jabatan_struktural varchar(20)
pendidikan varchar(15)
jenjang_id int(11)
rumpun varchar(7)
grade varchar(1)
jk varchar(10)
alamat_kantor varchar(300)
alamat_rumah varchar(300)
email varchar(50)
thn_terbit_sertifikat date
kegiatan varchar(50)
status_keaktifan_id varchar(1)
sebab text
kd_user varchar(50)
soft_delete varchar(1)

Berikut ini tabel "asesor_2" :
id_asesor2 int(11)
kd_asesor2 varchar(25)
nia2 varchar(20)
nm_asesor2 varchar(100)
ktp varchar(20)
unit_kerja varchar(300)
kota_id2 varchar(10)
provinsi_id int(11)
no_sertifikat varchar(30)
no_hp varchar(50)
no_wa varchar(50)
tempat_lahir varchar(30)
tgl_lahir date
jabatan varchar(50)
jabatan_struktural  varchar(20)
pendidikan varchar(15)
jenjang_id int(11)
rumpun varchar(7)
grade varchar(1)
jk varchar(10)
alamat_kantor varchar(300)
alamat_rumah varchar(300)
email varchar(50)
thn_terbit_sertifikat date
kegiatan varchar(50)
status_keaktifan_id varchar(1)
sebab text
kd_user varchar(50)
soft_delete varchar(1)

Berikut ini struktur tabel "mapping_2024_tahun" :
id_mapping_tahun  int(11)
nama_tahun  int(4)
provinsi_id int(11)

=================================================================================================================

buatlah modul "Mapping Visitasi Dasmen IASP 2024" pada direktori asesor/mapping_dasmen_2024/mapping.php, juga tersedia sub direktori ajax dan js, terdapat 5 kolom dengan tabel header yang digunakan adalah :

- NO autoincrement;

- SEKOLAH, pada tabel body berisi data sebagai berikut : 
  NPSN : sekolah.npsn,
  NAMA : sekolah.nama_sekolah,
  JENJANG : jenjang.jenjang_id=sekolah.jenjang_id (jenjang.nm_jenjang),
  KAB/KOTA : sekolah.kota_id = kab_kota.kota_id (kab_kota.nm_kota),
  NAMA KEPSEK : sekolah.nama_kepsek,
  NO HP KEPSEK : sekolah.no_hp_kepsek,
  TAHAP VISITASI : mapping_2024.tahap

- ASESOR VISITASI, pada tabel body berisi 
  ASESOR 1 (colspan =2), NIA : asesor_1.nia1, NAMA : asesor_1.nm_asesor1, KAB/KOTA : asesor_1.kota_id1 = kab_kota.kota_id (kab_kota.nm_kota), asesor_1.no_hp
  ASESOR 2 (colspan =2), NIA : asesor_2.nia2, NAMA : asesor_2.nm_asesor2, KAB/KOTA : asesor_2.kota_id2 = kab_kota.kota_id (kab_kota.nm_kota), asesor_1.no_hp

- FORM UPLOAD DOKUMEN
  Tombol "Upload File Format 3.1 Hasil Penilaian Pra-Visitasi 1" hanya tampil jika session login $kd_user==$asesor1, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_format_3_1_hasil_penilaian_pra_visitasi_1/"

  Tombol "Upload File Format 3.1 Hasil Penilaian Pra-Visitasi 2" hanya tampil jika session login $kd_user==$asesor2, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_format_3_1_hasil_penilaian_pra_visitasi_2/"

  Tombol "Upload File Format 3.2 LK Penggalian Data Pra-Visitasi 1" hanya tampil jika session login $kd_user==$asesor1, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_format_3_2_lk_penggalian_data_pra_visitasi_1/"

  Tombol "Upload File Format 3.2 LK Penggalian Data Pra-Visitasi 2" hanya tampil jika session login $kd_user==$asesor2, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_format_3_2_lk_penggalian_data_pra_visitasi_2/"

  Tombol "Upload File Format 4.1 Surat Tugas Visitasi" hanya tampil jika session login $kd_user==$asesor1, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_format_4_1_surat_tugas_visitasi/"

  Tombol "Upload File File Format 4.2 Pakta Integritas 1" hanya tampil jika session login $kd_user==$asesor1, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_format_4_2_pakta_integritas_1/"

  Tombol "Upload File File Format 4.2 Pakta Integritas 2" hanya tampil jika session login $kd_user==$asesor2, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_format_4_2_pakta_integritas_2/"

  Tombol "Upload File Format 4.3 Rekap Penggalian Data Penilaian 1" hanya tampil jika session login $kd_user==$asesor1, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_format_4_3_lembar_rekap_penggalian_data_penilaian_1/"

  Tombol "Upload File Format 4.3 Rekap Penggalian Data Penilaian 2" hanya tampil jika session login $kd_user==$asesor2, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_format_4_3_lembar_rekap_penggalian_data_penilaian_2/"

  Tombol "Upload File Format 4.4 Berita Acara Visitasi" hanya tampil jika session login $kd_user==$asesor1, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_format_4_4_berita_acara_visitasi/"

  Tombol "Upload File Format 4.5 Laporan Individu 1" hanya tampil jika session login $kd_user==$asesor1, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_format_4_5_laporan_individu_1/"

  Tombol "Upload File Format 4.5 Laporan Individu 2" hanya tampil jika session login $kd_user==$asesor2, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_format_4_5_laporan_individu_2/"

  Tombol "Upload File Format 4.5 Laporan Kelompok" hanya tampil jika session login $kd_user==$asesor1, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_format_4_5_laporan_kelompok/"

  Tombol "Upload File Format 4.5 Catatan Dan Saran" hanya tampil jika session login $kd_user==$asesor1, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_format_4_5_catatan_dan_saran/"

  Tombol "Upload File Foto Visitasi" hanya tampil jika session login $kd_user==$asesor1, jika tombol di-klik akan menampilkan modal untuk upload file ke direktori "../../../../simak/files/upload_file_foto_visitasi_2024/"


- DOKUMEN UNGGAHAN pada tabel body berisi

  label "File Format 3.1 Hasil Penilaian Pra-Visitasi 1 :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_format_3_1_hasil_penilaian_pra_visitasi_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah,
  
  label "File Format 3.1 Hasil Penilaian Pra-Visitasi 2 :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_format_3_1_hasil_penilaian_pra_visitasi_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah,
  
  label "File Format 3.2 LK Penggalian Data Pra-Visitasi 1 :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_format_3_2_lk_penggalian_data_pra_visitasi_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah,
  
  label "File Format 3.2 LK Penggalian Data Pra-Visitasi 2 :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_format_3_2_lk_penggalian_data_pra_visitasi_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah,
  
  label "File Format 4.1 Surat Tugas Visitasi :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_format_4_1_surat_tugas_visitasi ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah,
  
  label "File Format 4.2 Pakta Integritas 1 :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_format_4_2_pakta_integritas_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah,
  
  label "File Format 4.2 Pakta Integritas 2 :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_format_4_2_pakta_integritas_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah,
  
  label "File Format 4.3 Rekap Penggalian Data Penilaian 1 :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_format_4_3_lembar_rekap_penggalian_data_penilaian_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah,
  
  label "File Format 4.3 Rekap Penggalian Data Penilaian 2 :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_format_4_3_lembar_rekap_penggalian_data_penilaian_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah,
  
  label "File Format 4.4 Berita Acara Visitasi :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_format_4_4_berita_acara_visitasi ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah,
  
  label "File Format 4.5 Laporan Individu 1 :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_format_4_5_laporan_individu_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah,
  
  label "File Format 4.5 Laporan Individu 2 :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_format_4_5_laporan_individu_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah,
  
  label "File Format 4.5 Laporan Kelompok :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_format_4_5_laporan_kelompok ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah,
  
  label "File Format 4.5 Catatan Dan Saran :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_format_4_5_catatan_dan_saran ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah,
  
  label "File Foto Visitasi :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_foto_visitasi_2024 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah,

  
- JADWAL DAN AKSI pada tabel body berisi 
  label "Tanggal Pra-Akreditasi : mapping_2024.tgl_pra_visitasi,
  label "Tanggal Visit Mulai : mapping_2024.tgl_mulai_visitasi,
  label "Tanggal Visit Akhir : mapping_2024.tgl_akhir_visitasi,
  tombol "Input Tanggal Visitasi",
  tombol "Download Surat Pra-Akreditasi",
  tombol "Download Surat Tugas Visitasi"


oh iya lupa, tambahkan
<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Asesor
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');
?>

// Include header
<?php include '../header.php'; ?>


<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

terakhir di paling bawah <!-- Include footer -->
<?php include '../footer.php'; ?>

Berikut ini contoh query yang bisa anda gunakan untuk modul ini:

$tahun = "SELECT mapping_2024_tahun.nama_tahun FROM mapping_2024_tahun WHERE mapping_2024_tahun.provinsi_id='$provinsi_id' ";
$result_tahun = $connect->query($tahun);
if($result_tahun->num_rows > 0) {
while($row_tahun = $result_tahun->fetch_assoc()) {
$nama_tahun = $row_tahun['nama_tahun'];


$sql = "SELECT mapping_2024.id_mapping, sekolah.sekolah_id, sekolah.npsn, sekolah.nama_sekolah,
        sekolah.rumpun, sekolah.nama_kepsek, sekolah.no_hp_kepsek, sekolah.no_wa_kepsek,
        sekolah.jenjang_id, jenjang.nm_jenjang, sekolah.kota_id, kab_kota.nm_kota,
        mapping_2024.kd_asesor1, mapping_2024.kd_asesor2, mapping_2024.tahap, mapping_2024.tahun_akreditasi,
        mapping_2024.file_format_3_1_hasil_penilaian_pra_visitasi_1,
        mapping_2024.file_format_3_1_hasil_penilaian_pra_visitasi_2,
        mapping_2024.file_format_3_2_lk_penggalian_data_pra_visitasi_1,
        mapping_2024.file_format_3_2_lk_penggalian_data_pra_visitasi_2,
        mapping_2024.file_format_4_1_surat_tugas_visitasi,
        mapping_2024.file_format_4_2_pakta_integritas_1,
        mapping_2024.file_format_4_2_pakta_integritas_2,
        mapping_2024.file_format_4_3_lembar_rekap_penggalian_data_penilaian_1,
        mapping_2024.file_format_4_3_lembar_rekap_penggalian_data_penilaian_2,
        mapping_2024.file_format_4_4_berita_acara_visitasi,
        mapping_2024.file_format_4_5_laporan_individu_1,
        mapping_2024.file_format_4_5_laporan_individu_2,
        mapping_2024.file_format_4_5_laporan_kelompok,
        mapping_2024.file_format_4_5_catatan_dan_saran,
        mapping_2024.file_foto_visitasi_2024,
        (SELECT asesor.nia from asesor WHERE mapping_2024.kd_asesor1=asesor.kd_asesor) as nia1,
        (SELECT asesor.nm_asesor from asesor WHERE mapping_2024.kd_asesor1=asesor.kd_asesor) as nama1,
        (SELECT asesor.no_hp from asesor WHERE mapping_2024.kd_asesor1=asesor.kd_asesor) as hp1,
        (SELECT kab_kota.nm_kota from asesor LEFT JOIN kab_kota ON asesor.kota_id=kab_kota.kota_id
            WHERE mapping_2024.kd_asesor1=asesor.kd_asesor) as kota1,
        (SELECT asesor.nia from asesor WHERE mapping_2024.kd_asesor2=asesor.kd_asesor) as nia2,
        (SELECT asesor.nm_asesor from asesor WHERE mapping_2024.kd_asesor2=asesor.kd_asesor) as nama2,
        (SELECT asesor.no_hp from asesor WHERE mapping_2024.kd_asesor2=asesor.kd_asesor) as hp2,
        (SELECT kab_kota.nm_kota from asesor LEFT JOIN kab_kota ON asesor.kota_id=kab_kota.kota_id
            WHERE mapping_2024.kd_asesor2=asesor.kd_asesor) as kota2, mapping_2024.tahun_akreditasi
            FROM mapping_2024
        LEFT JOIN sekolah ON mapping_2024.sekolah_id=sekolah.sekolah_id
        LEFT JOIN jenjang ON sekolah.jenjang_id=jenjang.jenjang_id
        LEFT JOIN kab_kota ON sekolah.kota_id=kab_kota.kota_id
        WHERE (kd_asesor1='$kd_user' OR kd_asesor2='$kd_user')
        AND mapping_2024.tahun_akreditasi='$nama_tahun'
        AND mapping_2024.provinsi_id='$provinsi_id'  ";
$result = $connect->query($sql);
if($result->num_rows > 0) {
while($row = $result->fetch_assoc()) {
$nomor++;
$asesor1 = $row['kd_asesor1'];
$asesor2 = $row['kd_asesor2'];
$file_format_3_1_hasil_penilaian_pra_visitasi_1 = $row['file_format_3_1_hasil_penilaian_pra_visitasi_1'];
$file_format_3_1_hasil_penilaian_pra_visitasi_2 = $row['file_format_3_1_hasil_penilaian_pra_visitasi_2'];
$file_format_3_2_lk_penggalian_data_pra_visitasi_1 = $row['file_format_3_2_lk_penggalian_data_pra_visitasi_1'];
$file_format_3_2_lk_penggalian_data_pra_visitasi_2 = $row['file_format_3_2_lk_penggalian_data_pra_visitasi_2'];
$file_format_4_1_surat_tugas_visitasi = $row['file_format_4_1_surat_tugas_visitasi'];
$file_format_4_2_pakta_integritas_1 = $row['file_format_4_2_pakta_integritas_1'];
$file_format_4_2_pakta_integritas_2 = $row['file_format_4_2_pakta_integritas_2'];
$file_format_4_3_lembar_rekap_penggalian_data_penilaian_1 = $row['file_format_4_3_lembar_rekap_penggalian_data_penilaian_1'];
$file_format_4_3_lembar_rekap_penggalian_data_penilaian_2 = $row['file_format_4_3_lembar_rekap_penggalian_data_penilaian_2'];
$file_format_4_4_berita_acara_visitasi = $row['file_format_4_4_berita_acara_visitasi'];
$file_format_4_5_laporan_individu_1 = $row['file_format_4_5_laporan_individu_1'];
$file_format_4_5_laporan_individu_2 = $row['file_format_4_5_laporan_individu_2'];
$file_format_4_5_laporan_kelompok = $row['file_format_4_5_laporan_kelompok'];
$file_format_4_5_catatan_dan_saran = $row['file_format_4_5_catatan_dan_saran'];
$file_foto_visitasi_2024 = $row['file_foto_visitasi_2024'];

jika menurut anda query sql tersebut belum maksimal, silahkan diperbaiki agar maksimal kinerjanya

sampai disini apakah anda mengerti dengan apa yang saya maksudkan?
jika ada pertanyaan yang ingin ditanyakan dipersilahkan

=================================================================================================================

mantap kawanku yang sangat smart dan brilian, kita ke perbaikan lainnya, yaitu  jika mapping_2024.tgl_mulai_visitasi=0000-00-00 pada tabel di database maka pada kolom "JADWAL DAN AKSI" pada "Tanggal Visitasi" tampilkan tulisan "Tanggal visitasi belum diisi oleh asesor" namun jika mapping_2024.tgl_mulai_visitasi != 0000-00-00 maka tampilkan mapping_2024.tgl_mulai_visitasi,

kemudian jika mapping_2024.tgl_mulai_visitasi = 0000-00-00 maka tampilkan tombol "Input Tanggal Visitasi" dan tombol "Download Surat Tugas" di-hidden namun jika mapping_2024.tgl_mulai_visitasi != 0000-00-00 maka tombol "Input Tanggal Visitasi" di-hidden dan tombol "Download Surat Tugas" ditampilkan,

terakhir tampilkan tombol "Input Tanggal Visitasi" dan tombol "Download Surat Tugas" di kedua asesor ($kd_user==$asesor1 dan atau $kd_user==$asesor2)


ketika saya login sebagai asesor 2 ($kd_user==$asesor2) lalu mengisi tanggal visitasi, tampil pesan "Error! Data mapping tidak ditemukan atau Anda tidak memiliki akses untuk input tanggal", seharusnya tidak tampil pesan tersebut karena $kd_user==$asesor1 dan atau $kd_user==$asesor2 boleh mengisi tanggal visitasi
===================================================================================================================

1. Query Optimization: Query yang saya berikan menggunakan subquery untuk data asesor. anda bisa optimasi dengan JOIN untuk performa yang lebih baik.
2. Tabel Asesor: anda lihat di query saya menggunakan tabel asesor, tapi di struktur yang diberikan ada asesor_1 dan asesor_2. jika memungkinkan gunakan tabel asesor_1 dan asesor_2 (separated)
3. File Preview: Untuk badge "Sudah Upload", perlu clickable untuk preview file seperti modul PAUD visitasi
4. Upload Directories: semua 15 direktori upload sudah ada di server
5. Tanggal Input: Untuk "Input Tanggal Visitasi", field yang di-update: tgl_mulai_visitasi dan tgl_akhir_visitasi, untuk tgl_pra_visitasi tidak perlu diupdate. untuk update tgl_mulai_visitasi dan tgl_akhir_visitasi dibuat dalam satu modal saja

====================================================================================================================

kemudian pada kolom "JADWAL DAN AKSI", pada "Tanggal Visit Mulai:" jika pada tabel di datatase  pada field "mapping_2024.tgl_mulai_visitasi = 000-00-00" maka tampilkan tulisan "Tanggal visitasi belum diisi oleh Asesor", begitu juga dengan "Tanggal Visit Akhir:" jika pada tabel di datatase  pada field "mapping_2024.tgl_akhir_visitasi = 000-00-00" maka tampilkan tulisan "Tanggal visitasi belum diisi oleh Asesor",
kemudian yang tidak kalah pentingnya juga adalah tombol "Input Tanggal Visitasi" akan terhidden jika field "mapping_2024.tgl_mulai_visitasi != 000-00-00" dan akan tampil jika field "mapping_2024.tgl_mulai_visitasi = 000-00-00"
serta tombol "Download Surat Tugas Visitasi" akan ter-hidden jika field "mapping_2024.tgl_mulai_visitasi = 000-00-00"

====================================================================================================================