<?php
/**
 * AJAX handler untuk update validator (asesor)
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi parameter
    if (!isset($_POST['id_mapping']) || empty($_POST['id_mapping'])) {
        throw new Exception('ID mapping tidak valid');
    }
    
    if (!isset($_POST['nia1']) || !isset($_POST['nia2'])) {
        throw new Exception('Data NIA tidak lengkap');
    }
    
    $id_mapping = intval($_POST['id_mapping']);
    $nia1 = trim($_POST['nia1']);
    $nia2 = trim($_POST['nia2']);
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Lookup kd_asesor1 dari NIA1
    $kd_asesor1 = null;
    if (!empty($nia1)) {
        $lookup1_sql = "SELECT kd_asesor1 FROM asesor_1 WHERE nia1 = ?";
        $lookup1_stmt = $conn->prepare($lookup1_sql);
        $lookup1_stmt->bind_param("s", $nia1);
        $lookup1_stmt->execute();
        $lookup1_result = $lookup1_stmt->get_result();
        
        if ($lookup1_result->num_rows > 0) {
            $kd_asesor1 = $lookup1_result->fetch_assoc()['kd_asesor1'];
        }
    }
    
    // Lookup kd_asesor2 dari NIA2
    $kd_asesor2 = null;
    if (!empty($nia2)) {
        $lookup2_sql = "SELECT kd_asesor2 FROM asesor_2 WHERE nia2 = ?";
        $lookup2_stmt = $conn->prepare($lookup2_sql);
        $lookup2_stmt->bind_param("s", $nia2);
        $lookup2_stmt->execute();
        $lookup2_result = $lookup2_stmt->get_result();
        
        if ($lookup2_result->num_rows > 0) {
            $kd_asesor2 = $lookup2_result->fetch_assoc()['kd_asesor2'];
        }
    }
    
    // Cek apakah mapping exists dan milik provinsi user
    $check_sql = "SELECT mv.id_mapping 
                  FROM mapping_validasi_2025 mv
                  LEFT JOIN sekolah s ON mv.sekolah_id = s.sekolah_id
                  WHERE mv.id_mapping = ? 
                  AND mv.provinsi_id = ?
                  AND s.rumpun = 'kesetaraan'";
    
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("ii", $id_mapping, $provinsi_id_session);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows == 0) {
        throw new Exception('Data mapping tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    // Update kd_asesor1 dan kd_asesor2
    $update_sql = "UPDATE mapping_validasi_2025 
                   SET kd_asesor1 = ?, 
                       kd_asesor2 = ?
                   WHERE id_mapping = ? 
                   AND provinsi_id = ?";
    
    $update_stmt = $conn->prepare($update_sql);
    $update_stmt->bind_param("ssii", $kd_asesor1, $kd_asesor2, $id_mapping, $provinsi_id_session);
    
    if ($update_stmt->execute()) {
        // Ambil data asesor lengkap untuk response
        $asesor_data = [
            'nia1' => $nia1 ?: '-',
            'nama_asesor1' => '-',
            'hp_asesor1' => '-',
            'kota_asesor1' => '-',
            'nia2' => $nia2 ?: '-',
            'nama_asesor2' => '-',
            'hp_asesor2' => '-',
            'kota_asesor2' => '-'
        ];
        
        // Get data asesor 1
        if (!empty($kd_asesor1)) {
            $asesor1_sql = "SELECT a1.nia1, a1.nm_asesor1, a1.no_hp, k.nm_kota
                           FROM asesor_1 a1
                           LEFT JOIN kab_kota k ON a1.kota_id1 = k.kota_id
                           WHERE a1.kd_asesor1 = ?";
            $asesor1_stmt = $conn->prepare($asesor1_sql);
            $asesor1_stmt->bind_param("s", $kd_asesor1);
            $asesor1_stmt->execute();
            $asesor1_result = $asesor1_stmt->get_result();
            
            if ($asesor1_result->num_rows > 0) {
                $asesor1_data = $asesor1_result->fetch_assoc();
                $asesor_data['nia1'] = $asesor1_data['nia1'] ?? '-';
                $asesor_data['nama_asesor1'] = $asesor1_data['nm_asesor1'] ?? '-';
                $asesor_data['hp_asesor1'] = $asesor1_data['no_hp'] ?? '-';
                $asesor_data['kota_asesor1'] = $asesor1_data['nm_kota'] ?? '-';
            }
        }
        
        // Get data asesor 2
        if (!empty($kd_asesor2)) {
            $asesor2_sql = "SELECT a2.nia2, a2.nm_asesor2, a2.no_hp, k.nm_kota
                           FROM asesor_2 a2
                           LEFT JOIN kab_kota k ON a2.kota_id2 = k.kota_id
                           WHERE a2.kd_asesor2 = ?";
            $asesor2_stmt = $conn->prepare($asesor2_sql);
            $asesor2_stmt->bind_param("s", $kd_asesor2);
            $asesor2_stmt->execute();
            $asesor2_result = $asesor2_stmt->get_result();
            
            if ($asesor2_result->num_rows > 0) {
                $asesor2_data = $asesor2_result->fetch_assoc();
                $asesor_data['nia2'] = $asesor2_data['nia2'] ?? '-';
                $asesor_data['nama_asesor2'] = $asesor2_data['nm_asesor2'] ?? '-';
                $asesor_data['hp_asesor2'] = $asesor2_data['no_hp'] ?? '-';
                $asesor_data['kota_asesor2'] = $asesor2_data['nm_kota'] ?? '-';
            }
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Validator berhasil diupdate',
            'data' => $asesor_data
        ]);
    } else {
        throw new Exception('Gagal update validator: ' . $conn->error);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
    error_log("Error in update_validator.php: " . $e->getMessage());
}
?>
