$(document).ready(function() {
    // Initialize DataTable
    initDataTable();
    
    // Event handlers
    initEventHandlers();
});

function initDataTable() {
    $('#table-mapping-kpa').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'ajax/get_mapping_kpa.php',
            type: 'POST'
        },
        columns: [
            { 
                data: null,
                orderable: false,
                searchable: false,
                render: function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { data: 'npsn' },
            {
                data: 'nama_sekolah',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { data: 'nm_jenjang' },
            { data: 'nm_kota' },
            { data: 'nia' },
            {
                data: 'nm_asesor',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { 
                data: 'tahun_akreditasi',
                className: 'text-center'
            },
            { 
                data: 'tahap',
                className: 'text-center',
                render: function(data, type, row) {
                    if (data) {
                        var badgeClass = '';
                        switch(data) {
                            case '1':
                                badgeClass = 'badge-primary';
                                break;
                            case '2':
                                badgeClass = 'badge-success';
                                break;
                            case '3':
                                badgeClass = 'badge-warning';
                                break;
                            default:
                                badgeClass = 'badge-secondary';
                        }
                        return '<span class="badge ' + badgeClass + ' badge-tahap">' + data + '</span>';
                    }
                    return '-';
                }
            },
            { 
                data: null,
                orderable: false,
                searchable: false,
                className: 'text-center',
                render: function(data, type, row) {
                    return `
                        <button type="button" class="btn btn-info btn-sm btn-action" 
                                onclick="detailMappingKPA(${row.id_mapping})" 
                                title="Detail Mapping KPA">
                            <i class="fas fa-eye"></i>
                        </button>
                    `;
                }
            }
        ],
        order: [[7, 'desc']], // Order by tahun_akreditasi descending
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: "Memuat data...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            paginate: {
                first: "Pertama",
                last: "Terakhir",
                next: "Selanjutnya",
                previous: "Sebelumnya"
            },
            emptyTable: "Tidak ada data mapping KPA PAUD"
        },
        responsive: true,
        autoWidth: true,
        scrollX: true
    });
}

function initEventHandlers() {
    // Button click handlers
    $('#btn-input-mapping-kpa').on('click', function() {
        inputMappingKPA();
    });

    $('#btn-export-excel').on('click', function() {
        exportExcel();
    });

    $('#btn-import-excel').on('click', function() {
        showImportModal();
    });

    $('#btn-tahun-akreditasi').on('click', function() {
        filterTahunAkreditasi();
    });

    // Import modal event handlers
    $('#btn-download-template-modal').on('click', function() {
        downloadTemplate();
    });

    $('#btn-start-import').on('click', function() {
        startImport();
    });

    // Reset modal when closed
    $('#modal-import-excel').on('hidden.bs.modal', function() {
        resetImportModal();
    });

    // Form submit handler
    $(document).on('submit', '#form-input-mapping-kpa', function(e) {
        e.preventDefault();
        submitInputMappingKPA();
    });

    // Auto lookup handlers
    $(document).on('blur', '#npsn_sekolah', function() {
        const npsn = $(this).val().trim();
        if (npsn) {
            lookupNPSN(npsn);
        } else {
            $('#info-sekolah').hide();
            $('#sekolah_id').val('');
        }
    });

    $(document).on('blur', '#nia_asesor', function() {
        const nia = $(this).val().trim();
        if (nia) {
            lookupNIA(nia);
        } else {
            $('#info-asesor').hide();
            $('#kd_asesor').val('');
        }
    });

    // Modal reset handler
    $('#modal-input-mapping-kpa').on('hidden.bs.modal', function() {
        resetFormInputMapping();
    });

    // Form submit handler for tahun akreditasi
    $(document).on('submit', '#form-tahun-akreditasi', function(e) {
        e.preventDefault();
        updateTahunAkreditasi();
    });

    // Modal reset handler for tahun akreditasi
    $('#modal-tahun-akreditasi').on('hidden.bs.modal', function() {
        $('#form-tahun-akreditasi')[0].reset();
    });

    // Form submit handler for edit tanggal KPA
    $(document).on('submit', '#form-edit-tanggal-kpa', function(e) {
        e.preventDefault();
        submitEditTanggalKPA();
    });

    // Modal reset handler for edit tanggal KPA
    $('#modal-edit-tanggal-kpa').on('hidden.bs.modal', function() {
        $('#form-edit-tanggal-kpa')[0].reset();
    });

    // Event handler untuk preview file KPA
    $(document).on('click', '.file-preview-kpa', function(e) {
        e.preventDefault();
        const filename = $(this).data('filename');
        previewFileKPA(filename);
    });

    // Event handlers untuk tombol aksi di modal detail
    $(document).on('click', '#btn-edit-tanggal-kpa', function(e) {
        e.preventDefault();
        console.log('Edit Tanggal KPA clicked');
        openEditTanggalKPAModal();
    });

    $(document).on('click', '#btn-hapus-mapping-kpa', function(e) {
        e.preventDefault();
        console.log('Hapus Mapping KPA clicked');
        openKonfirmasiHapusMappingKPAModal();
    });

    // Event handler untuk konfirmasi hapus mapping KPA
    $(document).on('click', '#btn-konfirmasi-hapus-kpa', function(e) {
        e.preventDefault();
        submitHapusMappingKPA();
    });
}

function detailMappingKPA(mappingId) {
    console.log('Detail mapping KPA ID:', mappingId);

    // Load detail data
    $.ajax({
        url: 'ajax/get_detail_mapping_kpa.php',
        type: 'POST',
        data: { id_mapping: mappingId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                populateDetailModalKPA(response.data);
                $('#modal-detail-mapping-kpa').modal('show');
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat memuat detail mapping KPA');
        }
    });
}

function populateDetailModalKPA(data) {
    console.log('Populating detail modal KPA with data:', data);

    // Data Sekolah
    $('#detail-npsn').text(data.npsn || '-');
    $('#detail-nama-sekolah').text(data.nama_sekolah || '-');
    $('#detail-jenjang').text(data.nm_jenjang || '-');
    $('#detail-kab-kota').text(data.nm_kota || '-');
    $('#detail-nama-kepsek').text(data.nama_kepsek || '-');
    $('#detail-hp-kepsek').text(data.no_hp_kepsek || '-');
    $('#detail-wa-kepsek').text(data.no_wa_kepsek || '-');

    // Data Asesor
    $('#detail-nia-asesor').text(data.nia || '-');
    $('#detail-nama-asesor').text(data.nm_asesor || '-');
    $('#detail-hp-asesor').text(data.no_hp || '-');
    $('#detail-kota-asesor').text(data.kota_asesor || '-');

    // Dokumen Unggahan - File Status
    $('#detail-file-laporan-kpa').html(getFileStatusKPA(data.file_laporan_hasil_kpa));

    // Pelaksanaan Kegiatan
    $('#detail-tgl-penetapan-kpa').text(formatDate(data.tgl_penetapan_kpa) || '-');

    // Store data untuk edit/hapus
    $('#modal-detail-mapping-kpa').data('mapping-data', data);
}

function getFileStatusKPA(filename) {
    if (filename && filename.trim() !== '' && filename !== '0000-00-00') {
        // Clickable badge untuk file yang sudah ada
        return '<span class="badge badge-success text-white file-preview-kpa" ' +
               'style="cursor: pointer;" ' +
               'data-filename="' + filename + '" ' +
               'title="Klik untuk preview file">Sudah Upload</span>';
    } else {
        // Static badge untuk file yang belum ada
        return '<span class="badge badge-danger text-white">Belum Upload</span>';
    }
}

function previewFileKPA(filename) {
    console.log('Preview file KPA:', filename);

    if (!filename || filename.trim() === '' || filename === '0000-00-00') {
        showAlert('error', 'Nama file tidak valid');
        return;
    }

    // Construct file path
    const filePath = '../../../simak/files/upload_file_hasil_kpa/' + filename;

    console.log('Opening file path:', filePath);

    // Method 1: Direct window.open (primary method)
    try {
        // Buka file di tab baru
        const newWindow = window.open(filePath, '_blank');

        // Check if popup was blocked
        if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
            showAlert('warning', 'Popup diblokir oleh browser. Silakan izinkan popup untuk melihat file.');
        } else {
            console.log('File opened successfully in new tab');
        }
    } catch (error) {
        console.error('Error opening file:', error);

        // Method 2: Fallback dengan AJAX check (jika diperlukan)
        checkAndOpenFileKPA(filename);
    }
}

function checkAndOpenFileKPA(filename) {
    console.log('Checking file existence:', filename);

    // Alternative method: Check file via AJAX then open
    $.ajax({
        url: 'ajax/check_file_kpa.php',
        type: 'POST',
        data: { filename: filename },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.file_exists) {
                // File exists, try to open again
                const filePath = '../../../simak/files/upload_file_hasil_kpa/' + filename;
                window.open(filePath, '_blank');
            } else {
                showAlert('error', 'File tidak ditemukan atau tidak dapat diakses');
            }
        },
        error: function() {
            // If AJAX fails, still try to open the file
            const filePath = '../../../simak/files/upload_file_hasil_kpa/' + filename;
            window.open(filePath, '_blank');
        }
    });
}

function openEditTanggalKPAModal() {
    console.log('Opening edit tanggal KPA modal');

    // Ambil data mapping dari modal detail yang sedang terbuka
    const mappingData = $('#modal-detail-mapping-kpa').data('mapping-data');

    if (!mappingData) {
        showAlert('error', 'Data mapping tidak ditemukan. Silakan tutup dan buka kembali modal detail.');
        return;
    }

    // Populate form dengan data current
    $('#edit_id_mapping_kpa').val(mappingData.id_mapping);

    // Set tanggal current (handle '0000-00-00' sebagai empty)
    let currentDate = mappingData.tgl_penetapan_kpa;
    if (currentDate === '0000-00-00' || !currentDate) {
        currentDate = '';
    }
    $('#edit_tgl_penetapan_kpa').val(currentDate);

    // Show modal dengan z-index yang lebih tinggi untuk nested modal
    $('#modal-edit-tanggal-kpa').css('z-index', 1070).modal('show');

    console.log('Edit tanggal modal opened with ID:', mappingData.id_mapping, 'Current date:', currentDate);
}

function inputMappingKPA() {
    console.log('Input mapping KPA clicked');

    // Reset form dan show modal
    resetFormInputMapping();
    $('#modal-input-mapping-kpa').modal('show');
}

function resetFormInputMapping() {
    $('#form-input-mapping-kpa')[0].reset();
    $('#info-sekolah, #info-asesor').hide();
    $('#sekolah_id, #kd_asesor').val('');

    // Set default tahun akreditasi (tahun sekarang)
    const currentYear = new Date().getFullYear();
    $('#tahun_akreditasi').val(currentYear);
}

function lookupNPSN(npsn) {
    console.log('Looking up NPSN:', npsn);

    $.ajax({
        url: 'ajax/lookup_npsn.php',
        type: 'GET',
        data: { npsn: npsn },
        dataType: 'json',
        beforeSend: function() {
            $('#info-sekolah').hide();
        },
        success: function(response) {
            if (response.success) {
                console.log('NPSN lookup success:', response.data);

                // Set hidden field sekolah_id
                $('#sekolah_id').val(response.data.sekolah_id);

                // Show info sekolah
                $('#info-nama-sekolah').text(response.data.nama_sekolah);
                $('#info-jenjang-sekolah').text(response.data.nm_jenjang || '-');
                $('#info-kota-sekolah').text(response.data.nm_kota || '-');
                $('#info-sekolah').show();

            } else {
                showAlert('error', response.message || 'NPSN tidak ditemukan');
                $('#npsn_sekolah').val('').focus();
                $('#sekolah_id').val('');
                $('#info-sekolah').hide();
            }
        },
        error: function(xhr, status, error) {
            console.error('Error looking up NPSN:', error);
            showAlert('error', 'Terjadi kesalahan saat mencari NPSN');
            $('#sekolah_id').val('');
            $('#info-sekolah').hide();
        }
    });
}

function lookupNIA(nia) {
    console.log('Looking up NIA:', nia);

    $.ajax({
        url: 'ajax/lookup_nia.php',
        type: 'GET',
        data: { nia: nia },
        dataType: 'json',
        beforeSend: function() {
            $('#info-asesor').hide();
        },
        success: function(response) {
            if (response.success) {
                console.log('NIA lookup success:', response.data);

                // Set hidden field kd_asesor
                $('#kd_asesor').val(response.data.kd_asesor);

                // Show info asesor
                $('#info-nama-asesor').text(response.data.nm_asesor);
                $('#info-kota-asesor').text((response.data.nm_kota || '-') + ' (' + (response.data.rumpun || '-').toUpperCase() + ')');
                $('#info-asesor').show();

            } else {
                showAlert('error', response.message || 'NIA tidak ditemukan');
                $('#nia_asesor').val('').focus();
                $('#kd_asesor').val('');
                $('#info-asesor').hide();
            }
        },
        error: function(xhr, status, error) {
            console.error('Error looking up NIA:', error);
            showAlert('error', 'Terjadi kesalahan saat mencari NIA');
            $('#kd_asesor').val('');
            $('#info-asesor').hide();
        }
    });
}

function submitInputMappingKPA() {
    console.log('Submitting input mapping KPA');

    // Validasi form
    if (!validateFormInputMapping()) {
        return;
    }

    const formData = new FormData($('#form-input-mapping-kpa')[0]);

    $.ajax({
        url: 'ajax/insert_mapping_kpa.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        beforeSend: function() {
            // Disable form dan show loading
            $('#form-input-mapping-kpa button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
        },
        success: function(response) {
            if (response.success) {
                console.log('Mapping KPA berhasil disimpan:', response.data);

                // Tutup modal
                $('#modal-input-mapping-kpa').modal('hide');

                // Reload DataTable tanpa refresh halaman
                $('#table-mapping-kpa').DataTable().ajax.reload(null, false);

                // Silent success - no notification to avoid clutter
                console.log('Data mapping KPA berhasil disimpan');

            } else {
                showAlert('error', response.message || 'Gagal menyimpan data mapping KPA');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error submitting input mapping KPA:', error);
            showAlert('error', 'Terjadi kesalahan saat menyimpan data mapping KPA');
        },
        complete: function() {
            // Enable form kembali
            $('#form-input-mapping-kpa button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Data');
        }
    });
}

function validateFormInputMapping() {
    // Validasi manual untuk field required
    const npsn = $('#npsn_sekolah').val().trim();
    const nia = $('#nia_asesor').val().trim();
    const sekolah_id = $('#sekolah_id').val();
    const kd_asesor = $('#kd_asesor').val();
    const tahun = $('#tahun_akreditasi').val().trim();
    const tahap = $('#tahap').val();

    if (!npsn) {
        showAlert('error', 'NPSN Sekolah harus diisi');
        $('#npsn_sekolah').focus();
        return false;
    }

    if (!sekolah_id) {
        showAlert('error', 'Data sekolah tidak valid. Silakan input ulang NPSN');
        $('#npsn_sekolah').focus();
        return false;
    }

    if (!nia) {
        showAlert('error', 'NIA Asesor harus diisi');
        $('#nia_asesor').focus();
        return false;
    }

    if (!kd_asesor) {
        showAlert('error', 'Data asesor tidak valid. Silakan input ulang NIA');
        $('#nia_asesor').focus();
        return false;
    }

    // Tanggal Penetapan KPA adalah opsional, tidak perlu validasi

    if (!tahun || !/^\d{4}$/.test(tahun)) {
        showAlert('error', 'Tahun Akreditasi harus diisi dengan format 4 digit (contoh: 2024)');
        $('#tahun_akreditasi').focus();
        return false;
    }

    if (!tahap) {
        showAlert('error', 'Tahap harus dipilih');
        $('#tahap').focus();
        return false;
    }

    return true;
}

function submitEditTanggalKPA() {
    console.log('Submitting edit tanggal KPA');

    const formData = new FormData($('#form-edit-tanggal-kpa')[0]);

    $.ajax({
        url: 'ajax/update_tanggal_kpa.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        beforeSend: function() {
            // Disable submit button
            $('#form-edit-tanggal-kpa button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
        },
        success: function(response) {
            console.log('Update tanggal KPA response:', response);
            if (response.success) {
                // Tutup modal edit
                $('#modal-edit-tanggal-kpa').modal('hide');

                // Update field tanggal di modal detail secara real-time
                const newDate = response.data.tgl_penetapan_kpa;
                const formattedDate = formatDate(newDate);
                $('#detail-tgl-penetapan-kpa').text(formattedDate);

                // Update data yang tersimpan di modal detail
                const mappingData = $('#modal-detail-mapping-kpa').data('mapping-data');
                if (mappingData) {
                    mappingData.tgl_penetapan_kpa = newDate;
                    $('#modal-detail-mapping-kpa').data('mapping-data', mappingData);
                }

                // Silent success - no notification
                console.log('Tanggal penetapan KPA berhasil diupdate:', response.data);

            } else {
                showAlert('error', response.message || 'Gagal mengupdate tanggal penetapan KPA');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error updating tanggal KPA:', error);
            showAlert('error', 'Terjadi kesalahan saat mengupdate tanggal penetapan KPA');
        },
        complete: function() {
            // Re-enable submit button
            $('#form-edit-tanggal-kpa button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> Update Perubahan');
        }
    });
}

function openKonfirmasiHapusMappingKPAModal() {
    console.log('Opening konfirmasi hapus mapping KPA modal');

    // Ambil data mapping dari modal detail yang sedang terbuka
    const mappingData = $('#modal-detail-mapping-kpa').data('mapping-data');

    if (!mappingData) {
        showAlert('error', 'Data mapping tidak ditemukan. Silakan tutup dan buka kembali modal detail.');
        return;
    }

    // Populate nama sekolah di modal konfirmasi
    $('#nama-sekolah-hapus').text(mappingData.nama_sekolah || '-');

    // Simpan ID mapping yang akan dihapus
    $('#id-mapping-kpa-akan-dihapus').val(mappingData.id_mapping);

    // Show modal dengan z-index yang lebih tinggi untuk nested modal
    $('#modal-konfirmasi-hapus-kpa').css('z-index', 1080).modal('show');

    console.log('Konfirmasi hapus modal opened with ID:', mappingData.id_mapping, 'Sekolah:', mappingData.nama_sekolah);
}

function submitHapusMappingKPA() {
    console.log('Submitting hapus mapping KPA');

    const idMapping = $('#id-mapping-kpa-akan-dihapus').val();

    if (!idMapping) {
        showAlert('error', 'ID mapping tidak valid');
        return;
    }

    $.ajax({
        url: 'ajax/hapus_mapping_kpa.php',
        type: 'POST',
        data: { id_mapping: idMapping },
        dataType: 'json',
        beforeSend: function() {
            // Disable tombol konfirmasi dan show loading
            $('#btn-konfirmasi-hapus-kpa').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menghapus...');
        },
        success: function(response) {
            if (response.success) {
                console.log('Mapping KPA berhasil dihapus:', response.data);

                // Tutup modal konfirmasi hapus
                $('#modal-konfirmasi-hapus-kpa').modal('hide');

                // Tutup modal detail
                $('#modal-detail-mapping-kpa').modal('hide');

                // Reload DataTable tanpa refresh halaman
                $('#table-mapping-kpa').DataTable().ajax.reload(null, false);

                // Silent success - no notification to avoid clutter
                console.log('Data mapping KPA berhasil dihapus:', response.data);

            } else {
                showAlert('error', response.message || 'Gagal menghapus data mapping KPA');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error submitting hapus mapping KPA:', error);
            showAlert('error', 'Terjadi kesalahan saat menghapus data mapping KPA');
        },
        complete: function() {
            // Enable tombol konfirmasi kembali
            $('#btn-konfirmasi-hapus-kpa').prop('disabled', false).html('<i class="fas fa-trash"></i> Ya, Hapus');
        }
    });
}

function exportExcel() {
    console.log('Export Excel clicked');

    // Show loading
    showAlert('info', 'Sedang memproses export Excel...');

    // Get all data from server for export
    $.ajax({
        url: 'ajax/get_export_mapping_kpa.php',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Prepare data for Excel
                var excelData = [];

                // Add header row
                excelData.push([
                    'NO',
                    'NPSN',
                    'NAMA SEKOLAH',
                    'JENJANG',
                    'KAB/KOTA SEKOLAH',
                    'NIA ASESOR',
                    'NAMA ASESOR',
                    'KAB/KOTA ASESOR',
                    'TAHUN AKREDITASI',
                    'TAHAP KPA',
                    'TANGGAL PENETAPAN KPA',
                    'FILE LAPORAN HASIL KPA'
                ]);

                // Add data rows
                response.data.forEach(function(row, index) {
                    // Format tanggal penetapan KPA
                    let tanggalPenetapan = '';
                    if (row.tgl_penetapan_kpa && row.tgl_penetapan_kpa !== '0000-00-00') {
                        const date = new Date(row.tgl_penetapan_kpa);
                        tanggalPenetapan = date.toLocaleDateString('id-ID');
                    }

                    // Status file laporan - sesuai dengan script PHP
                    let statusFile = '';
                    if (row.file_laporan_hasil_kpa &&
                        row.file_laporan_hasil_kpa.trim() !== '' &&
                        row.file_laporan_hasil_kpa !== '0000-00-00' &&
                        row.file_laporan_hasil_kpa !== null) {
                        statusFile = 'Sudah diunggah'; // Sesuai script PHP
                    } else {
                        statusFile = 'Belum diunggah'; // Sesuai script PHP
                    }

                    excelData.push([
                        index + 1,
                        row.npsn || '',
                        row.nama_sekolah || '',
                        row.nm_jenjang || '',
                        row.nm_kota_sekolah || '',
                        row.nia || '',
                        row.nm_asesor || '',
                        row.nm_kota_asesor || '',
                        row.tahun_akreditasi || '',
                        row.tahap || '',
                        tanggalPenetapan,
                        statusFile
                    ]);
                });

                // Create workbook and worksheet
                var wb = XLSX.utils.book_new();
                var ws = XLSX.utils.aoa_to_sheet(excelData);

                // Set column widths
                ws['!cols'] = [
                    {wch: 5},   // NO
                    {wch: 12},  // NPSN
                    {wch: 30},  // NAMA SEKOLAH
                    {wch: 10},  // JENJANG
                    {wch: 15},  // KAB/KOTA SEKOLAH
                    {wch: 12},  // NIA ASESOR
                    {wch: 25},  // NAMA ASESOR
                    {wch: 15},  // KAB/KOTA ASESOR
                    {wch: 12},  // TAHUN AKREDITASI
                    {wch: 12},  // TAHAP KPA
                    {wch: 18},  // TANGGAL PENETAPAN KPA
                    {wch: 20}   // FILE LAPORAN HASIL KPA
                ];

                // Apply conditional formatting for file status
                applyConditionalFormattingKPA(ws, excelData, response.data);

                // Add worksheet to workbook
                XLSX.utils.book_append_sheet(wb, ws, 'export kpa');

                // Generate filename with current date
                var now = new Date();
                var dateStr = now.getFullYear() +
                             String(now.getMonth() + 1).padStart(2, '0') +
                             String(now.getDate()).padStart(2, '0');
                var filename = 'Export_Mapping_KPA_PAUD_' + dateStr + '.xlsx';

                // Save file
                XLSX.writeFile(wb, filename);

                // Show success message
                showAlert('success', 'File Excel berhasil didownload: ' + filename);

            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Export Excel error:', error);
            showAlert('error', 'Terjadi kesalahan saat export data');
        }
    });
}

function importExcel() {
    console.log('Import Excel clicked');
    
    // TODO: Implement Excel import functionality
    showAlert('info', 'Fitur import Excel akan segera tersedia');
}

function filterTahunAkreditasi() {
    console.log('Filter tahun akreditasi clicked');

    // Load current tahun akreditasi value
    $.ajax({
        url: 'ajax/get_tahun_kpa.php',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#nama_tahun').val(response.data.nama_tahun);
                $('#modal-tahun-akreditasi').modal('show');
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat memuat data tahun akreditasi');
        }
    });
}

function updateTahunAkreditasi() {
    console.log('Update tahun akreditasi');

    const formData = new FormData($('#form-tahun-akreditasi')[0]);

    $.ajax({
        url: 'ajax/update_tahun_kpa.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        beforeSend: function() {
            // Disable submit button
            $('#form-tahun-akreditasi button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
        },
        success: function(response) {
            console.log('Update tahun response:', response);
            if (response.success) {
                // Tutup modal
                $('#modal-tahun-akreditasi').modal('hide');

                // Reload DataTable untuk menerapkan filter tahun baru
                $('#table-mapping-kpa').DataTable().ajax.reload(null, false);

                // Silent success - no notification
                console.log('Tahun akreditasi berhasil diupdate:', response.data);
            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Update tahun error:', xhr.responseText);
            showAlert('error', 'Terjadi kesalahan saat menyimpan tahun akreditasi: ' + error);
        },
        complete: function() {
            // Re-enable submit button
            $('#form-tahun-akreditasi button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan');
        }
    });
}

function applyConditionalFormattingKPA(ws, excelData, originalData) {
    console.log('Applying conditional formatting for KPA file status');

    // Apply header styling (row 1)
    const headerRange = XLSX.utils.encode_range({s: {c: 0, r: 0}, e: {c: 11, r: 0}});

    for (let col = 0; col <= 11; col++) {
        const cellAddress = XLSX.utils.encode_cell({c: col, r: 0});
        if (!ws[cellAddress]) ws[cellAddress] = {};

        ws[cellAddress].s = {
            fill: {
                fgColor: {rgb: "D3D3D3"} // Abu-abu untuk header
            },
            font: {
                bold: true,
                color: {rgb: "000000"} // Hitam
            },
            alignment: {
                horizontal: "center",
                vertical: "center"
            },
            border: {
                top: {style: "thin", color: {rgb: "000000"}},
                bottom: {style: "thin", color: {rgb: "000000"}},
                left: {style: "thin", color: {rgb: "000000"}},
                right: {style: "thin", color: {rgb: "000000"}}
            }
        };
    }

    // Apply conditional formatting for file status column (column 11)
    originalData.forEach(function(row, index) {
        const rowIndex = index + 1; // +1 because header is row 0
        const cellAddress = XLSX.utils.encode_cell({c: 11, r: rowIndex}); // Column L (FILE LAPORAN HASIL KPA)

        if (!ws[cellAddress]) ws[cellAddress] = {};

        // Check file status - lebih akurat untuk deteksi file kosong
        const hasFile = row.file_laporan_hasil_kpa &&
                       row.file_laporan_hasil_kpa.trim() !== '' &&
                       row.file_laporan_hasil_kpa !== '0000-00-00' &&
                       row.file_laporan_hasil_kpa !== null;

        // Debug log untuk conditional formatting (sesuai script PHP)
        const statusText = hasFile ? 'Sudah diunggah' : 'Belum diunggah';
        const bgColor = hasFile ? '#98FB98' : '#FF6347';
        const textColor = hasFile ? '#000' : '#FFFFFF';
        console.log(`Row ${index + 1}: File = "${row.file_laporan_hasil_kpa}", Status = "${statusText}", BG = ${bgColor}, Text = ${textColor}`);

        if (hasFile) {
            // Sudah Unggah - Sesuai script PHP: #98FB98 (PaleGreen) dengan text #000 (hitam)
            ws[cellAddress].s = {
                fill: {
                    fgColor: {rgb: "98FB98"} // #98FB98 - PaleGreen (sama dengan script PHP)
                },
                font: {
                    color: {rgb: "000000"}, // #000 - Hitam (sama dengan script PHP)
                    bold: true
                },
                alignment: {
                    horizontal: "center",
                    vertical: "center"
                },
                border: {
                    top: {style: "thin", color: {rgb: "000000"}},
                    bottom: {style: "thin", color: {rgb: "000000"}},
                    left: {style: "thin", color: {rgb: "000000"}},
                    right: {style: "thin", color: {rgb: "000000"}}
                }
            };
        } else {
            // Belum Unggah - Sesuai script PHP: #FF6347 (Tomato) dengan text #FFFFFF (putih)
            ws[cellAddress].s = {
                fill: {
                    fgColor: {rgb: "FF6347"} // #FF6347 - Tomato (sama dengan script PHP)
                },
                font: {
                    color: {rgb: "FFFFFF"}, // #FFFFFF - Putih (sama dengan script PHP)
                    bold: true
                },
                alignment: {
                    horizontal: "center",
                    vertical: "center"
                },
                border: {
                    top: {style: "thin", color: {rgb: "000000"}},
                    bottom: {style: "thin", color: {rgb: "000000"}},
                    left: {style: "thin", color: {rgb: "000000"}},
                    right: {style: "thin", color: {rgb: "000000"}}
                }
            };
        }
    });

    console.log('Conditional formatting applied successfully');
}

function formatDate(dateString) {
    // Handle empty date atau '0000-00-00'
    if (!dateString || dateString === '0000-00-00' || dateString === '') {
        return '-';
    }

    try {
        const date = new Date(dateString);
        const options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        };
        return date.toLocaleDateString('id-ID', options);
    } catch (e) {
        return '-';
    }
}

function showAlert(type, message) {
    var alertClass = '';
    var iconClass = '';

    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            iconClass = 'fa-check-circle';
            break;
        case 'error':
            alertClass = 'alert-danger';
            iconClass = 'fa-exclamation-triangle';
            break;
        case 'warning':
            alertClass = 'alert-warning';
            iconClass = 'fa-exclamation-triangle';
            break;
        case 'info':
            alertClass = 'alert-info';
            iconClass = 'fa-info-circle';
            break;
        default:
            alertClass = 'alert-info';
            iconClass = 'fa-info-circle';
    }

    var alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${iconClass}"></i> ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;

    // Remove existing alerts
    $('.alert').remove();

    // Add new alert at the top of content
    $('.content-wrapper .content').prepend(alertHtml);

    // Auto hide after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

// Function untuk download template Excel menggunakan SheetJS
function downloadTemplate() {
    // Check if SheetJS is available
    if (typeof XLSX === 'undefined') {
        showAlert('error', 'Library Excel tidak tersedia. Silakan refresh halaman.');
        return;
    }

    try {
        // Create workbook
        const wb = XLSX.utils.book_new();

        // Create worksheet data
        const wsData = [
            // Headers
            ['NPSN', 'NIA', 'Tgl_Penetapan_KPA', 'Tahap', 'Tahun_Akreditasi', '', 'PETUNJUK PENGISIAN:'],
            // Sample data
            ['12345678', 'A123456', '2024-01-15', '1', '2024', '', '1. NPSN: 8 digit nomor sekolah'],
            ['87654321', 'B789012', '2024-02-20', '2', '2024', '', '2. NIA: Nomor Induk Asesor'],
            ['11223344', 'C456789', '2024-03-10', '3', '2024', '', '3. Tgl_Penetapan_KPA: Format yyyy-mm-dd'],
            ['', '', '', '', '', '', '4. Tahap: Angka 1, 2, atau 3'],
            ['', '', '', '', '', '', '5. Tahun_Akreditasi: 4 digit tahun'],
            ['', '', '', '', '', '', '6. Hapus baris contoh sebelum import']
        ];

        // Create worksheet
        const ws = XLSX.utils.aoa_to_sheet(wsData);

        // Set column widths
        ws['!cols'] = [
            { width: 12 }, // NPSN
            { width: 12 }, // NIA
            { width: 18 }, // Tgl_Penetapan_KPA
            { width: 8 },  // Tahap
            { width: 16 }, // Tahun_Akreditasi
            { width: 3 },  // Empty column
            { width: 35 }  // Instructions
        ];

        // Set row heights
        ws['!rows'] = [
            { hpt: 25 }, // Header row height
            { hpt: 20 }, // Sample data rows
            { hpt: 20 },
            { hpt: 20 },
            { hpt: 20 },
            { hpt: 20 },
            { hpt: 20 }
        ];

        // Add cell comments/notes for better guidance
        if (!ws['!comments']) ws['!comments'] = [];

        // Add comment to header row
        ws['A1'].c = [{
            a: 'System',
            t: 'Masukkan 8 digit NPSN sekolah PAUD yang valid'
        }];

        ws['B1'].c = [{
            a: 'System',
            t: 'Masukkan NIA asesor yang terdaftar dan aktif'
        }];

        ws['C1'].c = [{
            a: 'System',
            t: 'Format tanggal: YYYY-MM-DD (contoh: 2024-01-15). Boleh kosong.'
        }];

        ws['D1'].c = [{
            a: 'System',
            t: 'Pilih tahap: 1, 2, atau 3'
        }];

        ws['E1'].c = [{
            a: 'System',
            t: 'Tahun akreditasi 4 digit (contoh: 2024)'
        }];

        // Add data validation (if supported by Excel)
        ws['!dataValidation'] = {
            'D2:D1000': {
                type: 'list',
                allowBlank: false,
                showInputMessage: true,
                showErrorMessage: true,
                errorTitle: 'Invalid Input',
                error: 'Pilih tahap 1, 2, atau 3',
                promptTitle: 'Tahap',
                prompt: 'Pilih tahap visitasi',
                formula1: '"1,2,3"'
            }
        };

        // Freeze header row
        ws['!freeze'] = { xSplit: 0, ySplit: 1 };

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Template');

        // Generate filename
        const filename = 'Template_Import_Mapping_KPA_PAUD_' + new Date().toISOString().split('T')[0] + '.xlsx';

        // Download file
        XLSX.writeFile(wb, filename);

    } catch (error) {
        console.error('Error generating template:', error);
        showAlert('error', 'Gagal membuat template Excel');
    }
}

// Function untuk show import modal
function showImportModal() {
    resetImportModal();
    $('#modal-import-excel').modal('show');
}

// Function untuk reset import modal
function resetImportModal() {
    // Reset file input
    $('#excel-file').val('');

    // Show upload section, hide others
    $('#upload-section').show();
    $('#progress-section').hide();
    $('#results-section').hide();

    // Always show petunjuk import
    $('#petunjuk-import').show();

    // Reset progress
    updateProgress(0, 'Memulai proses import...');

    // Enable buttons
    $('#btn-start-import').prop('disabled', false);
    $('#btn-cancel-import').prop('disabled', false);
}

// Function untuk update progress
function updateProgress(percentage, message) {
    $('#import-progress-bar')
        .css('width', percentage + '%')
        .attr('aria-valuenow', percentage)
        .text(percentage + '%');

    $('#progress-message').text(message);

    // Update progress title based on percentage
    if (percentage === 25) {
        $('#progress-title').text('Uploading file...');
    } else if (percentage === 50) {
        $('#progress-title').text('Parsing Excel...');
    } else if (percentage === 75) {
        $('#progress-title').text('Inserting data...');
    } else if (percentage === 100) {
        $('#progress-title').text('Import complete!');
    }
}

// Function untuk start import menggunakan SheetJS
function startImport() {
    // Check if SheetJS is available
    if (typeof XLSX === 'undefined') {
        showAlert('error', 'Library Excel tidak tersedia. Silakan refresh halaman.');
        return;
    }

    // Validate file selection
    const fileInput = $('#excel-file')[0];
    if (!fileInput.files || fileInput.files.length === 0) {
        showAlert('error', 'Silakan pilih file Excel terlebih dahulu');
        return;
    }

    const file = fileInput.files[0];

    // Validate file type
    const allowedTypes = ['.xlsx', '.xls'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    if (!allowedTypes.includes(fileExtension)) {
        showAlert('error', 'Format file tidak didukung. Gunakan .xlsx atau .xls');
        return;
    }

    // Hide upload section, show progress
    $('#upload-section').hide();
    $('#progress-section').show();
    $('#results-section').hide();

    // Disable buttons during import
    $('#btn-start-import').prop('disabled', true);
    $('#btn-cancel-import').prop('disabled', true);

    // Phase 1: Upload (25%)
    updateProgress(25, 'Membaca file Excel...');

    // Read Excel file using SheetJS
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            // Phase 2: Parse (50%)
            updateProgress(50, 'Memproses data Excel...');

            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });

            // Get first worksheet
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];

            // Convert to array of arrays
            const rows = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

            // Remove header row
            rows.shift();

            // Filter out empty rows
            const filteredRows = rows.filter(row => row.some(cell => cell !== undefined && cell !== ''));

            if (filteredRows.length === 0) {
                throw new Error('File tidak memiliki data untuk diimport');
            }

            if (filteredRows.length > 100) {
                throw new Error('Maksimal 100 baris data. File Anda memiliki ' + filteredRows.length + ' baris');
            }

            // Phase 3: Send to server (75%)
            updateProgress(75, 'Menyimpan data ke database...');

            // Send parsed data to server
            $.ajax({
                url: 'ajax/import_excel_data.php',
                type: 'POST',
                data: {
                    excel_data: JSON.stringify(filteredRows)
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // Phase 4: Complete (100%)
                        updateProgress(100, 'Import berhasil diselesaikan');

                        // Show results
                        showImportResults(response.data);

                        // Refresh DataTable
                        $('#table-mapping-kpa').DataTable().ajax.reload(null, false);

                    } else {
                        showImportError(response.message);
                    }
                },
                error: function(xhr, status, error) {
                    let errorMessage = 'Terjadi kesalahan saat menyimpan data';
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        errorMessage = errorResponse.message || errorMessage;
                    } catch (e) {
                        // Use default error message
                    }
                    showImportError(errorMessage);
                },
                complete: function() {
                    // Re-enable buttons
                    $('#btn-start-import').prop('disabled', false);
                    $('#btn-cancel-import').prop('disabled', false);
                }
            });

        } catch (error) {
            showImportError('Gagal membaca file Excel: ' + error.message);
            $('#btn-start-import').prop('disabled', false);
            $('#btn-cancel-import').prop('disabled', false);
        }
    };

    reader.onerror = function() {
        showImportError('Gagal membaca file');
        $('#btn-start-import').prop('disabled', false);
        $('#btn-cancel-import').prop('disabled', false);
    };

    // Read file as array buffer
    reader.readAsArrayBuffer(file);
}

// Function untuk show import results
function showImportResults(data) {
    $('#progress-section').hide();
    $('#results-section').show();

    let resultHtml = '';

    if (data.success_count > 0 || data.error_count > 0) {
        resultHtml += '<div class="alert alert-success">';
        resultHtml += '<h6><i class="fas fa-check-circle"></i> Import Selesai</h6>';
        resultHtml += '<p class="mb-1"><strong>Berhasil:</strong> ' + data.success_count + ' data</p>';
        if (data.error_count > 0) {
            resultHtml += '<p class="mb-1"><strong>Gagal:</strong> ' + data.error_count + ' data</p>';
        }
        resultHtml += '<p class="mb-0"><strong>Total diproses:</strong> ' + data.total_processed + ' baris</p>';
        resultHtml += '</div>';

        // Show errors if any
        if (data.errors && data.errors.length > 0) {
            resultHtml += '<div class="alert alert-warning">';
            resultHtml += '<h6><i class="fas fa-exclamation-triangle"></i> Detail Error:</h6>';
            resultHtml += '<ul class="mb-0">';
            data.errors.slice(0, 10).forEach(function(error) { // Show max 10 errors
                resultHtml += '<li>' + error + '</li>';
            });
            if (data.errors.length > 10) {
                resultHtml += '<li><em>... dan ' + (data.errors.length - 10) + ' error lainnya</em></li>';
            }
            resultHtml += '</ul>';
            resultHtml += '</div>';
        }
    } else {
        resultHtml += '<div class="alert alert-info">';
        resultHtml += '<h6><i class="fas fa-info-circle"></i> Tidak Ada Data</h6>';
        resultHtml += '<p class="mb-0">Tidak ada data yang berhasil diimport. Semua data mungkin sudah ada atau tidak valid.</p>';
        resultHtml += '</div>';
    }

    $('#import-results').html(resultHtml);
}

// Function untuk show import error
function showImportError(message) {
    $('#progress-section').hide();
    $('#results-section').show();

    const errorHtml = `
        <div class="alert alert-danger">
            <h6><i class="fas fa-times-circle"></i> Import Gagal</h6>
            <p class="mb-0">${message}</p>
        </div>
    `;

    $('#import-results').html(errorHtml);
}
