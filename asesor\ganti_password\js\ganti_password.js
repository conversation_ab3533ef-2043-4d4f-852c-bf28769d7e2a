/**
 * JavaScript untuk Modul Ganti Password
 * Additional features only - main functions defined in modal file
 */

$(document).ready(function() {
    // Event handler untuk toggle password visibility
    $('#toggle-password').click(function() {
        var passwordField = $('#new_password');
        var toggleIcon = $('#toggle-icon');

        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            toggleIcon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            toggleIcon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
});

// Main functions are defined in modal_ganti_password.php for immediate availability
// This file only contains additional features


