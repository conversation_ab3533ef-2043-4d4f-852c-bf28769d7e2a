<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Get tahun aktif dari mapping_validasi_tahun_2025
    $tahun_query = "SELECT nama_tahun FROM mapping_validasi_tahun_2025 WHERE provinsi_id = ?";
    $tahun_stmt = $conn->prepare($tahun_query);
    $tahun_stmt->bind_param("i", $provinsi_id);
    $tahun_stmt->execute();
    $tahun_result = $tahun_stmt->get_result();
    
    $tahun_aktif = '';
    if ($tahun_result && $tahun_result->num_rows > 0) {
        $tahun_row = $tahun_result->fetch_assoc();
        $tahun_aktif = $tahun_row['nama_tahun'];
    }
    
    // Query comprehensive untuk export sesuai spesifikasi
    $export_query = "SELECT 
                        mv.id_mapping,
                        s.npsn,
                        s.nama_sekolah,
                        j.nm_jenjang,
                        s.rumpun,
                        kk_sekolah.nm_kota as nm_kota_sekolah,
                        a1.nia1,
                        a1.nm_asesor1,
                        kk_asesor1.nm_kota as nm_kota_asesor1,
                        a2.nia2,
                        a2.nm_asesor2,
                        kk_asesor2.nm_kota as nm_kota_asesor2,
                        mv.tahun_akreditasi,
                        mv.tahap,
                        mv.file_format_5_1_berita_acara_hasil_validasi_1,
                        mv.file_format_5_1_berita_acara_hasil_validasi_2
                     FROM mapping_validasi_2025 mv
                     LEFT JOIN sekolah s ON mv.sekolah_id = s.sekolah_id
                     LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                     LEFT JOIN kab_kota kk_sekolah ON s.kota_id = kk_sekolah.kota_id
                     LEFT JOIN asesor_1 a1 ON mv.kd_asesor1 = a1.kd_asesor1
                     LEFT JOIN kab_kota kk_asesor1 ON a1.kota_id1 = kk_asesor1.kota_id
                     LEFT JOIN asesor_2 a2 ON mv.kd_asesor2 = a2.kd_asesor2
                     LEFT JOIN kab_kota kk_asesor2 ON a2.kota_id2 = kk_asesor2.kota_id
                     LEFT JOIN mapping_validasi_tahun_2025 mvt ON mv.tahun_akreditasi = mvt.nama_tahun
                     WHERE mv.tahun_akreditasi = mvt.nama_tahun
                     AND mv.provinsi_id = ?
                     AND mvt.provinsi_id = ?
                     AND s.rumpun = 'kesetaraan'";
    
    // Add filter berdasarkan tahun aktif jika ada
    if (!empty($tahun_aktif)) {
        $export_query .= " AND mv.tahun_akreditasi = ?";
    }
    
    $export_query .= " ORDER BY s.nama_sekolah ASC";
    
    $export_stmt = $conn->prepare($export_query);
    
    if (!empty($tahun_aktif)) {
        $export_stmt->bind_param("iis", $provinsi_id, $provinsi_id, $tahun_aktif);
    } else {
        $export_stmt->bind_param("ii", $provinsi_id, $provinsi_id);
    }
    
    $export_stmt->execute();
    $export_result = $export_stmt->get_result();
    
    $data = [];
    if ($export_result && $export_result->num_rows > 0) {
        while ($row = $export_result->fetch_assoc()) {
            $data[] = $row;
        }
    }
    
    // Log export activity
    error_log("Export Mapping Validasi SM - Provinsi: $provinsi_id, Tahun: $tahun_aktif, Records: " . count($data) . ", User: " . $_SESSION['nm_user']);
    
    // Response
    echo json_encode([
        'success' => true,
        'data' => $data,
        'tahun_aktif' => $tahun_aktif,
        'total_records' => count($data),
        'message' => 'Data export berhasil dimuat'
    ]);
    
} catch (Exception $e) {
    error_log("Export Mapping Validasi Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan saat mengambil data export: ' . $e->getMessage(),
        'data' => [],
        'total_records' => 0
    ]);
}

$conn->close();
?>
