<?php
require_once '../../koneksi.php';

// Include session checker dan require level As<PERSON><PERSON>
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');

// Get session variables
$kd_user = mysqli_real_escape_string($conn, $_SESSION['kd_user'] ?? '');
$provinsi_id = mysqli_real_escape_string($conn, $_SESSION['provinsi_id'] ?? '');

// Get tahun akreditasi
$tahun_query = "SELECT nama_tahun FROM mapping_2025_tahun WHERE provinsi_id = '$provinsi_id'";
$tahun_result = $conn->query($tahun_query);
$nama_tahun = '';
if ($tahun_result && $tahun_result->num_rows > 0) {
    $tahun_row = $tahun_result->fetch_assoc();
    $nama_tahun = $tahun_row['nama_tahun'];
}

// Optimized main query dengan JOIN untuk performa yang lebih baik
$sql = "SELECT
            m.id_mapping, m.tahap, m.tahun_akreditasi,
            m.tgl_pra_visitasi, m.tgl_mulai_visitasi, m.tgl_akhir_visitasi,
            m.tgl_surat_tugas_pra_visitasi, m.no_surat_tugas_pra_visitasi,
            m.tgl_surat_tugas_visitasi, m.no_surat_tugas_visitasi,
            s.sekolah_id, s.npsn, s.nama_sekolah, s.nama_kepsek, s.no_hp_kepsek, s.rumpun,
            j.nm_jenjang, kk.nm_kota,
            m.kd_asesor1, m.kd_asesor2,
            a1.nia1, a1.nm_asesor1, a1.no_hp as hp1, kk1.nm_kota as kota1,
            a2.nia2, a2.nm_asesor2, a2.no_hp as hp2, kk2.nm_kota as kota2,
            m.file_format_3_1_hasil_penilaian_pra_visitasi_1,
            m.file_format_3_1_hasil_penilaian_pra_visitasi_2,
            m.file_format_3_2_lk_penggalian_data_pra_visitasi_1,
            m.file_format_3_2_lk_penggalian_data_pra_visitasi_2,
            m.file_format_4_1_surat_tugas_visitasi,
            m.file_format_4_2_pakta_integritas_1,
            m.file_format_4_2_pakta_integritas_2,
            m.file_lk_penilaian_1,
            m.file_lk_penilaian_2,
            m.file_format_4_4_berita_acara_visitasi,
            m.file_format_4_5_laporan_individu_1,
            m.file_format_4_5_laporan_individu_2,
            m.file_format_4_5_laporan_kelompok,
            m.file_format_4_5_catatan_dan_saran,
            m.file_foto_visitasi_2025
        FROM mapping_2025 m
        LEFT JOIN sekolah s ON m.sekolah_id = s.sekolah_id AND s.soft_delete = '1'
        LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
        LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
        LEFT JOIN asesor_1 a1 ON m.kd_asesor1 = a1.kd_asesor1 AND a1.soft_delete = '1'
        LEFT JOIN kab_kota kk1 ON a1.kota_id1 = kk1.kota_id
        LEFT JOIN asesor_2 a2 ON m.kd_asesor2 = a2.kd_asesor2 AND a2.soft_delete = '1'
        LEFT JOIN kab_kota kk2 ON a2.kota_id2 = kk2.kota_id
        WHERE (m.kd_asesor1 = '$kd_user' OR m.kd_asesor2 = '$kd_user')
            AND m.tahun_akreditasi = '$nama_tahun'
            AND m.provinsi_id = '$provinsi_id'
            AND s.rumpun IN ('dasmen', 'kesetaraan')
        ORDER BY m.id_mapping DESC";

$result = $conn->query($sql);
?>

<!-- Include header -->
<?php include '../header.php'; ?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Mapping Visitasi Dasmen & Kesetaraan IASP 2025</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../dashboard/dashboard.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="#">Data Mapping Dasmen</a></li>
                        <li class="breadcrumb-item active">Mapping Visitasi</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
    <!-- /.content-header -->

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            
            <!-- Alert Messages -->
            <div id="alert-container"></div>
            
            <!-- Main Card -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-clipboard-check mr-2"></i>
                        Data Mapping Visitasi Dasmen & Kesetaraan IASP - Tahun <?php echo $nama_tahun; ?>
                    </h3>
                    <div class="card-tools">
                        <span class="badge badge-info">
                            Total: <?php echo $result ? $result->num_rows : 0; ?> data
                        </span>
                    </div>
                </div>
                <!-- /.card-header -->
                
                <div class="card-body">
                    <?php if ($result && $result->num_rows > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped table-hover" style="font-size: 13px;">
                                <thead style="background-color: #007bff; color: white;">
                                    <tr>
                                        <th width="3%" class="text-center">NO</th>
                                        <th width="20%" class="text-center">SEKOLAH</th>
                                        <th width="25%" class="text-center">ASESOR VISITASI</th>
                                        <th width="25%" class="text-center">FORM UPLOAD DOKUMEN</th>
                                        <th width="20%" class="text-center">DOKUMEN UNGGAHAN</th>
                                        <th width="7%" class="text-center">JADWAL DAN AKSI</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $nomor = 0;
                                    while($row = $result->fetch_assoc()): 
                                        $nomor++;
                                        $id_mapping = $row['id_mapping'];
                                        $asesor1 = $row['kd_asesor1'];
                                        $asesor2 = $row['kd_asesor2'];
                                    ?>
                                    <tr>
                                        <!-- NO -->
                                        <td class="text-center align-top" style="vertical-align: top; padding-top: 8px;"><?php echo $nomor; ?></td>
                                        
                                        <!-- SEKOLAH -->
                                        <td class="align-top text-left">
                                            <div style="font-size: 13px; line-height: 1.4;">
                                                <strong>NPSN:</strong> <?php echo $row['npsn']; ?><br>
                                                <strong>NAMA:</strong> <?php echo $row['nama_sekolah']; ?><br>
                                                <strong>JENJANG:</strong> <?php echo $row['nm_jenjang']; ?><br>
                                                <strong>KAB/KOTA:</strong> <?php echo $row['nm_kota']; ?><br>
                                                <strong>NAMA KEPSEK:</strong> <?php echo $row['nama_kepsek']; ?><br>
                                                <strong>NO HP KEPSEK:</strong> <?php echo $row['no_hp_kepsek']; ?><br>
                                                <strong>TAHAP VISITASI:</strong> <?php echo $row['tahap']; ?>
                                            </div>
                                        </td>

                                        <!-- ASESOR VISITASI -->
                                        <td class="align-top text-left">
                                            <div style="font-size: 13px; line-height: 1.4;">
                                                <div class="mb-2">
                                                    <span class="badge badge-primary" style="font-size: 11px; margin-bottom: 5px;">ASESOR 1</span><br>
                                                    <strong>NIA:</strong> <?php echo $row['nia1'] ?? '-'; ?><br>
                                                    <strong>NAMA:</strong> <?php echo $row['nm_asesor1'] ?? '-'; ?><br>
                                                    <strong>KAB/KOTA:</strong> <?php echo $row['kota1'] ?? '-'; ?><br>
                                                    <strong>NO HP:</strong> <?php echo $row['hp1'] ?? '-'; ?>
                                                </div>
                                                <div>
                                                    <span class="badge badge-success" style="font-size: 11px; margin-bottom: 5px;">ASESOR 2</span><br>
                                                    <strong>NIA:</strong> <?php echo $row['nia2'] ?? '-'; ?><br>
                                                    <strong>NAMA:</strong> <?php echo $row['nm_asesor2'] ?? '-'; ?><br>
                                                    <strong>KAB/KOTA:</strong> <?php echo $row['kota2'] ?? '-'; ?><br>
                                                    <strong>NO HP:</strong> <?php echo $row['hp2'] ?? '-'; ?>
                                                </div>
                                            </div>
                                        </td>

                                        <!-- FORM UPLOAD DOKUMEN -->
                                        <td class="align-top text-left">
                                            <div style="font-size: 11px; line-height: 1.3;">
                                                <?php if ($kd_user == $asesor1): ?>
                                                    <!-- Asesor 1 Upload Buttons -->
                                                    <button type="button" class="btn btn-primary btn-xs mb-1" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="uploadFile(<?php echo $id_mapping; ?>, 'format_3_1_pra_visitasi_1')">
                                                        Upload Format 3.1 Pra-Visitasi 1
                                                    </button><br>

                                                    <button type="button" class="btn btn-success btn-xs mb-1" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="uploadFile(<?php echo $id_mapping; ?>, 'format_3_2_lk_pra_visitasi_1')">
                                                        Upload Format 3.2 LK Pra-Visitasi 1
                                                    </button><br>

                                                    <button type="button" class="btn btn-info btn-xs mb-1" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="uploadFile(<?php echo $id_mapping; ?>, 'format_4_1_surat_tugas')">
                                                        Upload Format 4.1 Surat Tugas
                                                    </button><br>

                                                    <button type="button" class="btn btn-warning btn-xs mb-1" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="uploadFile(<?php echo $id_mapping; ?>, 'format_4_2_pakta_1')">
                                                        Upload Format 4.2 Pakta 1
                                                    </button><br>

                                                    <button type="button" class="btn btn-secondary btn-xs mb-1" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="uploadFile(<?php echo $id_mapping; ?>, 'format_4_3_rekap_1')">
                                                        Upload LK Penilaian 1
                                                    </button><br>

                                                    <button type="button" class="btn btn-dark btn-xs mb-1" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="uploadFile(<?php echo $id_mapping; ?>, 'format_4_4_berita_acara')">
                                                        Upload Format 4.4 Berita Acara
                                                    </button><br>

                                                    <button type="button" class="btn btn-primary btn-xs mb-1" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="uploadFile(<?php echo $id_mapping; ?>, 'format_4_5_laporan_1')">
                                                        Upload Format 4.5 Laporan 1
                                                    </button><br>

                                                    <button type="button" class="btn btn-success btn-xs mb-1" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="uploadFile(<?php echo $id_mapping; ?>, 'format_4_5_laporan_kelompok')">
                                                        Upload Format 4.5 Laporan Kelompok
                                                    </button><br>

                                                    <button type="button" class="btn btn-info btn-xs mb-1" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="uploadFile(<?php echo $id_mapping; ?>, 'format_4_5_catatan_saran')">
                                                        Upload Format 4.5 Catatan Saran
                                                    </button><br>

                                                    <button type="button" class="btn btn-warning btn-xs mb-1" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="uploadFile(<?php echo $id_mapping; ?>, 'foto_visitasi')">
                                                        Upload Foto Visitasi
                                                    </button>
                                                <?php endif; ?>

                                                <?php if ($kd_user == $asesor2): ?>
                                                    <!-- Asesor 2 Upload Buttons -->
                                                    <button type="button" class="btn btn-primary btn-xs mb-1" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="uploadFile(<?php echo $id_mapping; ?>, 'format_3_1_pra_visitasi_2')">
                                                        Upload Format 3.1 Pra-Visitasi 2
                                                    </button><br>

                                                    <button type="button" class="btn btn-success btn-xs mb-1" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="uploadFile(<?php echo $id_mapping; ?>, 'format_3_2_lk_pra_visitasi_2')">
                                                        Upload Format 3.2 LK Pra-Visitasi 2
                                                    </button><br>

                                                    <button type="button" class="btn btn-warning btn-xs mb-1" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="uploadFile(<?php echo $id_mapping; ?>, 'format_4_2_pakta_2')">
                                                        Upload Format 4.2 Pakta 2
                                                    </button><br>

                                                    <button type="button" class="btn btn-secondary btn-xs mb-1" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="uploadFile(<?php echo $id_mapping; ?>, 'format_4_3_rekap_2')">
                                                        Upload LK Penilaian 2
                                                    </button><br>

                                                    <button type="button" class="btn btn-primary btn-xs mb-1" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="uploadFile(<?php echo $id_mapping; ?>, 'format_4_5_laporan_2')">
                                                        Upload Format 4.5 Laporan 2
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>

                                        <!-- DOKUMEN UNGGAHAN -->
                                        <td class="align-top text-left">
                                            <div style="font-size: 12px; line-height: 1.3;">
                                                <!-- File Format 3.1 Pra-Visitasi 1 -->
                                                <div class="mb-1">
                                                    <span style="font-weight: bold;">Format 3.1 Pra-Visitasi 1:</span>
                                                    <?php if (!empty($row['file_format_3_1_hasil_penilaian_pra_visitasi_1'])): ?>
                                                        <span class="badge badge-success" id="badge-<?php echo $id_mapping; ?>-format_3_1_pra_visitasi_1" style="cursor: pointer;"
                                                              onclick="previewFile('<?php echo $row['file_format_3_1_hasil_penilaian_pra_visitasi_1']; ?>')">
                                                            <i class="fas fa-eye"></i> Sudah Upload
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger" id="badge-<?php echo $id_mapping; ?>-format_3_1_pra_visitasi_1">Belum Upload</span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- File Format 3.1 Pra-Visitasi 2 -->
                                                <div class="mb-1">
                                                    <span style="font-weight: bold;">Format 3.1 Pra-Visitasi 2:</span>
                                                    <?php if (!empty($row['file_format_3_1_hasil_penilaian_pra_visitasi_2'])): ?>
                                                        <span class="badge badge-success" id="badge-<?php echo $id_mapping; ?>-format_3_1_pra_visitasi_2" style="cursor: pointer;"
                                                              onclick="previewFile('<?php echo $row['file_format_3_1_hasil_penilaian_pra_visitasi_2']; ?>')">
                                                            <i class="fas fa-eye"></i> Sudah Upload
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger" id="badge-<?php echo $id_mapping; ?>-format_3_1_pra_visitasi_2">Belum Upload</span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- File Format 3.2 LK Pra-Visitasi 1 -->
                                                <div class="mb-1">
                                                    <span style="font-weight: bold;">Format 3.2 LK Pra-Visitasi 1:</span>
                                                    <?php if (!empty($row['file_format_3_2_lk_penggalian_data_pra_visitasi_1'])): ?>
                                                        <span class="badge badge-success" id="badge-<?php echo $id_mapping; ?>-format_3_2_lk_pra_visitasi_1" style="cursor: pointer;"
                                                              onclick="previewFile('<?php echo $row['file_format_3_2_lk_penggalian_data_pra_visitasi_1']; ?>')">
                                                            <i class="fas fa-eye"></i> Sudah Upload
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger" id="badge-<?php echo $id_mapping; ?>-format_3_2_lk_pra_visitasi_1">Belum Upload</span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- File Format 3.2 LK Pra-Visitasi 2 -->
                                                <div class="mb-1">
                                                    <span style="font-weight: bold;">Format 3.2 LK Pra-Visitasi 2:</span>
                                                    <?php if (!empty($row['file_format_3_2_lk_penggalian_data_pra_visitasi_2'])): ?>
                                                        <span class="badge badge-success" id="badge-<?php echo $id_mapping; ?>-format_3_2_lk_pra_visitasi_2" style="cursor: pointer;"
                                                              onclick="previewFile('<?php echo $row['file_format_3_2_lk_penggalian_data_pra_visitasi_2']; ?>')">
                                                            <i class="fas fa-eye"></i> Sudah Upload
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger" id="badge-<?php echo $id_mapping; ?>-format_3_2_lk_pra_visitasi_2">Belum Upload</span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- File Format 4.1 Surat Tugas -->
                                                <div class="mb-1">
                                                    <span style="font-weight: bold;">Format 4.1 Surat Tugas:</span>
                                                    <?php if (!empty($row['file_format_4_1_surat_tugas_visitasi'])): ?>
                                                        <span class="badge badge-success" id="badge-<?php echo $id_mapping; ?>-format_4_1_surat_tugas" style="cursor: pointer;"
                                                              onclick="previewFile('<?php echo $row['file_format_4_1_surat_tugas_visitasi']; ?>')">
                                                            <i class="fas fa-eye"></i> Sudah Upload
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger" id="badge-<?php echo $id_mapping; ?>-format_4_1_surat_tugas">Belum Upload</span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- File Format 4.2 Pakta Integritas 1 -->
                                                <div class="mb-1">
                                                    <span style="font-weight: bold;">Format 4.2 Pakta Integritas 1:</span>
                                                    <?php if (!empty($row['file_format_4_2_pakta_integritas_1'])): ?>
                                                        <span class="badge badge-success" id="badge-<?php echo $id_mapping; ?>-format_4_2_pakta_1" style="cursor: pointer;"
                                                              onclick="previewFile('<?php echo $row['file_format_4_2_pakta_integritas_1']; ?>')">
                                                            <i class="fas fa-eye"></i> Sudah Upload
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger" id="badge-<?php echo $id_mapping; ?>-format_4_2_pakta_1">Belum Upload</span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- File Format 4.2 Pakta Integritas 2 -->
                                                <div class="mb-1">
                                                    <span style="font-weight: bold;">Format 4.2 Pakta Integritas 2:</span>
                                                    <?php if (!empty($row['file_format_4_2_pakta_integritas_2'])): ?>
                                                        <span class="badge badge-success" id="badge-<?php echo $id_mapping; ?>-format_4_2_pakta_2" style="cursor: pointer;"
                                                              onclick="previewFile('<?php echo $row['file_format_4_2_pakta_integritas_2']; ?>')">
                                                            <i class="fas fa-eye"></i> Sudah Upload
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger" id="badge-<?php echo $id_mapping; ?>-format_4_2_pakta_2">Belum Upload</span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- File Format 4.3 Rekap 1 -->
                                                <div class="mb-1">
                                                    <span style="font-weight: bold;">Lembar Kerja Penilaian 1:</span>
                                                    <?php if (!empty($row['file_lk_penilaian_1'])): ?>
                                                        <span class="badge badge-success" id="badge-<?php echo $id_mapping; ?>-format_4_3_rekap_1" style="cursor: pointer;"
                                                              onclick="previewFile('<?php echo $row['file_lk_penilaian_1']; ?>')">
                                                            <i class="fas fa-eye"></i> Sudah Upload
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger" id="badge-<?php echo $id_mapping; ?>-format_4_3_rekap_1">Belum Upload</span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- File Format 4.3 Rekap 2 -->
                                                <div class="mb-1">
                                                    <span style="font-weight: bold;">Lembar Kerja Penilaian 2:</span>
                                                    <?php if (!empty($row['file_lk_penilaian_2'])): ?>
                                                        <span class="badge badge-success" id="badge-<?php echo $id_mapping; ?>-format_4_3_rekap_2" style="cursor: pointer;"
                                                              onclick="previewFile('<?php echo $row['file_lk_penilaian_2']; ?>')">
                                                            <i class="fas fa-eye"></i> Sudah Upload
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger" id="badge-<?php echo $id_mapping; ?>-format_4_3_rekap_2">Belum Upload</span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- File Format 4.4 Berita Acara -->
                                                <div class="mb-1">
                                                    <span style="font-weight: bold;">Format 4.4 Berita Acara:</span>
                                                    <?php if (!empty($row['file_format_4_4_berita_acara_visitasi'])): ?>
                                                        <span class="badge badge-success" id="badge-<?php echo $id_mapping; ?>-format_4_4_berita_acara" style="cursor: pointer;"
                                                              onclick="previewFile('<?php echo $row['file_format_4_4_berita_acara_visitasi']; ?>')">
                                                            <i class="fas fa-eye"></i> Sudah Upload
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger" id="badge-<?php echo $id_mapping; ?>-format_4_4_berita_acara">Belum Upload</span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- File Format 4.5 Laporan Individu 1 -->
                                                <div class="mb-1">
                                                    <span style="font-weight: bold;">Format 4.5 Laporan Individu 1:</span>
                                                    <?php if (!empty($row['file_format_4_5_laporan_individu_1'])): ?>
                                                        <span class="badge badge-success" id="badge-<?php echo $id_mapping; ?>-format_4_5_laporan_1" style="cursor: pointer;"
                                                              onclick="previewFile('<?php echo $row['file_format_4_5_laporan_individu_1']; ?>')">
                                                            <i class="fas fa-eye"></i> Sudah Upload
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger" id="badge-<?php echo $id_mapping; ?>-format_4_5_laporan_1">Belum Upload</span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- File Format 4.5 Laporan Individu 2 -->
                                                <div class="mb-1">
                                                    <span style="font-weight: bold;">Format 4.5 Laporan Individu 2:</span>
                                                    <?php if (!empty($row['file_format_4_5_laporan_individu_2'])): ?>
                                                        <span class="badge badge-success" id="badge-<?php echo $id_mapping; ?>-format_4_5_laporan_2" style="cursor: pointer;"
                                                              onclick="previewFile('<?php echo $row['file_format_4_5_laporan_individu_2']; ?>')">
                                                            <i class="fas fa-eye"></i> Sudah Upload
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger" id="badge-<?php echo $id_mapping; ?>-format_4_5_laporan_2">Belum Upload</span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- File Format 4.5 Laporan Kelompok -->
                                                <div class="mb-1">
                                                    <span style="font-weight: bold;">Format 4.5 Laporan Kelompok:</span>
                                                    <?php if (!empty($row['file_format_4_5_laporan_kelompok'])): ?>
                                                        <span class="badge badge-success" id="badge-<?php echo $id_mapping; ?>-format_4_5_laporan_kelompok" style="cursor: pointer;"
                                                              onclick="previewFile('<?php echo $row['file_format_4_5_laporan_kelompok']; ?>')">
                                                            <i class="fas fa-eye"></i> Sudah Upload
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger" id="badge-<?php echo $id_mapping; ?>-format_4_5_laporan_kelompok">Belum Upload</span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- File Format 4.5 Catatan Dan Saran -->
                                                <div class="mb-1">
                                                    <span style="font-weight: bold;">Format 4.5 Catatan Dan Saran:</span>
                                                    <?php if (!empty($row['file_format_4_5_catatan_dan_saran'])): ?>
                                                        <span class="badge badge-success" id="badge-<?php echo $id_mapping; ?>-format_4_5_catatan_saran" style="cursor: pointer;"
                                                              onclick="previewFile('<?php echo $row['file_format_4_5_catatan_dan_saran']; ?>')">
                                                            <i class="fas fa-eye"></i> Sudah Upload
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger" id="badge-<?php echo $id_mapping; ?>-format_4_5_catatan_saran">Belum Upload</span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- File Foto Visitasi -->
                                                <div class="mb-1">
                                                    <span style="font-weight: bold;">Foto Visitasi:</span>
                                                    <?php if (!empty($row['file_foto_visitasi_2025'])): ?>
                                                        <span class="badge badge-success" id="badge-<?php echo $id_mapping; ?>-foto_visitasi" style="cursor: pointer;"
                                                              onclick="previewFile('<?php echo $row['file_foto_visitasi_2025']; ?>')">
                                                            <i class="fas fa-eye"></i> Sudah Upload
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger" id="badge-<?php echo $id_mapping; ?>-foto_visitasi">Belum Upload</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>

                                        <!-- JADWAL DAN AKSI -->
                                        <td class="align-top text-left">
                                            <div style="font-size: 12px; line-height: 1.3;">
                                                <?php
                                                // Check apakah tanggal visitasi sudah diisi atau masih default
                                                $tgl_mulai_empty = empty($row['tgl_mulai_visitasi']) || $row['tgl_mulai_visitasi'] == '0000-00-00';
                                                $tgl_akhir_empty = empty($row['tgl_akhir_visitasi']) || $row['tgl_akhir_visitasi'] == '0000-00-00';

                                                // Production ready - debug info removed
                                                ?>

                                                <div class="mb-2">
                                                    <strong>Tanggal Pra-Akreditasi:</strong><br>
                                                    <?php
                                                    if (!empty($row['tgl_pra_visitasi']) && $row['tgl_pra_visitasi'] != '0000-00-00') {
                                                        echo date('d-m-Y', strtotime($row['tgl_pra_visitasi']));
                                                    } else {
                                                        echo '-';
                                                    }
                                                    ?>
                                                </div>

                                                <div class="mb-2">
                                                    <strong>Tanggal Visit Mulai:</strong><br>
                                                    <?php
                                                    if ($tgl_mulai_empty) {
                                                        echo '<span style="color: #dc3545; font-style: italic;">Tanggal visitasi belum diisi oleh Asesor</span>';
                                                    } else {
                                                        echo date('d-m-Y', strtotime($row['tgl_mulai_visitasi']));
                                                    }
                                                    ?>
                                                </div>

                                                <div class="mb-2">
                                                    <strong>Tanggal Visit Akhir:</strong><br>
                                                    <?php
                                                    if ($tgl_akhir_empty) {
                                                        echo '<span style="color: #dc3545; font-style: italic;">Tanggal visitasi belum diisi oleh Asesor</span>';
                                                    } else {
                                                        echo date('d-m-Y', strtotime($row['tgl_akhir_visitasi']));
                                                    }
                                                    ?>
                                                </div>

                                                <!-- Tombol Input Tanggal - Hanya tampil jika tanggal belum diisi dan user adalah asesor1 -->
                                                <?php if ($kd_user == $asesor1 && $tgl_mulai_empty): ?>
                                                <div class="mb-2">
                                                    <button type="button" class="btn btn-primary btn-xs" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="inputTanggalVisitasi(<?php echo $id_mapping; ?>)">
                                                        <i class="fas fa-calendar"></i> Input Tanggal Visitasi
                                                    </button>
                                                </div>
                                                <?php endif; ?>

                                                <div class="mb-1">
                                                    <button type="button" class="btn btn-success btn-xs" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="downloadSuratPraAkreditasi(<?php echo $id_mapping; ?>)">
                                                        <i class="fas fa-download"></i> Download Surat Pra-Akreditasi
                                                    </button>
                                                </div>

                                                <!-- Tombol Download Surat Tugas - Hanya tampil jika tanggal sudah diisi -->
                                                <?php if (!$tgl_mulai_empty): ?>
                                                <div>
                                                    <button type="button" class="btn btn-info btn-xs" style="font-size: 10px; padding: 3px 6px;"
                                                            onclick="downloadSuratTugasVisitasi(<?php echo $id_mapping; ?>)">
                                                        <i class="fas fa-download"></i> Download Surat Tugas Visitasi
                                                    </button>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i>
                            Tidak ada data mapping visitasi Dasmen & Kesetaraan untuk tahun <?php echo $nama_tahun; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <!-- /.card-body -->
            </div>
            <!-- /.card -->

        </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
</div>
<!-- /.content-wrapper -->

<!-- Modal Upload File -->
<div class="modal fade" id="modal-upload" tabindex="-1" role="dialog" aria-labelledby="modal-upload-label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-upload-label">Upload File</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="form-upload" enctype="multipart/form-data">
                    <input type="hidden" id="upload-id-mapping" name="id_mapping">
                    <input type="hidden" id="upload-file-type" name="file_type">

                    <div class="form-group">
                        <label for="file-upload">Pilih File (PDF Only):</label>
                        <input type="file" class="form-control-file" id="file-upload" name="file_visitasi"  required>
                        <small class="form-text text-muted">Format file yang diizinkan: PDF (maksimal 10MB)</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="btn-upload">Upload</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Input Tanggal Visitasi -->
<div class="modal fade" id="modal-tanggal" tabindex="-1" role="dialog" aria-labelledby="modal-tanggal-label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-tanggal-label">Input Tanggal Visitasi</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="form-tanggal">
                    <input type="hidden" id="tanggal-id-mapping" name="id_mapping">

                    <div class="form-group">
                        <label for="tgl-mulai-visitasi">Tanggal Mulai Visitasi:</label>
                        <input type="date" class="form-control" id="tgl-mulai-visitasi" name="tgl_mulai_visitasi" required>
                    </div>

                    <div class="form-group">
                        <label for="tgl-akhir-visitasi">Tanggal Akhir Visitasi:</label>
                        <input type="date" class="form-control" id="tgl-akhir-visitasi" name="tgl_akhir_visitasi" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="btn-save-tanggal">Simpan</button>
            </div>
        </div>
    </div>
</div>

<!-- Include footer -->
<?php include '../footer.php'; ?>

<!-- Page specific script -->
<script src="js/mapping_visitasi.js"></script>

<script>
// File type mapping untuk upload
const fileTypeMapping = {
    'format_3_1_pra_visitasi_1': {
        title: 'Upload File Format 3.1 Hasil Penilaian Pra-Visitasi 1',
        field: 'file_format_3_1_hasil_penilaian_pra_visitasi_1'
    },
    'format_3_1_pra_visitasi_2': {
        title: 'Upload File Format 3.1 Hasil Penilaian Pra-Visitasi 2',
        field: 'file_format_3_1_hasil_penilaian_pra_visitasi_2'
    },
    'format_3_2_lk_pra_visitasi_1': {
        title: 'Upload File Format 3.2 LK Penggalian Data Pra-Visitasi 1',
        field: 'file_format_3_2_lk_penggalian_data_pra_visitasi_1'
    },
    'format_3_2_lk_pra_visitasi_2': {
        title: 'Upload File Format 3.2 LK Penggalian Data Pra-Visitasi 2',
        field: 'file_format_3_2_lk_penggalian_data_pra_visitasi_2'
    },
    'format_4_1_surat_tugas': {
        title: 'Upload File Format 4.1 Surat Tugas Visitasi',
        field: 'file_format_4_1_surat_tugas_visitasi'
    },
    'format_4_2_pakta_1': {
        title: 'Upload File Format 4.2 Pakta Integritas 1',
        field: 'file_format_4_2_pakta_integritas_1'
    },
    'format_4_2_pakta_2': {
        title: 'Upload File Format 4.2 Pakta Integritas 2',
        field: 'file_format_4_2_pakta_integritas_2'
    },
    'format_4_3_rekap_1': {
        title: 'Upload File File Lembar Kerja Penilaian 1',
        field: 'file_lk_penilaian_1'
    },
    'format_4_3_rekap_2': {
        title: 'Upload File File Lembar Kerja Penilaian 2',
        field: 'file_lk_penilaian_2'
    },
    'format_4_4_berita_acara': {
        title: 'Upload File Format 4.4 Berita Acara Visitasi',
        field: 'file_format_4_4_berita_acara_visitasi'
    },
    'format_4_5_laporan_1': {
        title: 'Upload File Format 4.5 Laporan Individu 1',
        field: 'file_format_4_5_laporan_individu_1'
    },
    'format_4_5_laporan_2': {
        title: 'Upload File Format 4.5 Laporan Individu 2',
        field: 'file_format_4_5_laporan_individu_2'
    },
    'format_4_5_laporan_kelompok': {
        title: 'Upload File Format 4.5 Laporan Kelompok',
        field: 'file_format_4_5_laporan_kelompok'
    },
    'format_4_5_catatan_saran': {
        title: 'Upload File Format 4.5 Catatan Dan Saran',
        field: 'file_format_4_5_catatan_dan_saran'
    },
    'foto_visitasi': {
        title: 'Upload File Foto Visitasi',
        field: 'file_foto_visitasi_2025'
    }
};

// Global functions
function uploadFile(idMapping, fileType) {
    const mapping = fileTypeMapping[fileType];
    if (!mapping) {
        showError('Tipe file tidak valid');
        return;
    }

    $('#upload-id-mapping').val(idMapping);
    $('#upload-file-type').val(fileType);
    $('#modal-upload-label').text(mapping.title);
    $('#modal-upload').modal('show');
}

function previewFile(filename) {
    if (!filename) {
        showError('File tidak ditemukan');
        return;
    }

    const url = 'preview_file.php?file=' + encodeURIComponent(filename);
    window.open(url, '_blank');
}

function inputTanggalVisitasi(idMapping) {
    $('#tanggal-id-mapping').val(idMapping);
    $('#modal-tanggal').modal('show');
}



// Minimal error notification function (hanya untuk error)
function showError(message) {
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;
    $('#alert-container').html(alertHtml);
    setTimeout(() => $('.alert').fadeOut(), 5000);
}
</script>
