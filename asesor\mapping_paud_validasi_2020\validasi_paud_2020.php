<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level As<PERSON>or
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');

// Get session variables
$kd_user = $_SESSION['kd_user'] ?? '';
$provinsi_id = $_SESSION['provinsi_id'] ?? '';

// Get tahun akreditasi
$tahun_query = "SELECT nama_tahun FROM mapping_paud_validasi_tahun WHERE provinsi_id = '$provinsi_id' ORDER BY id_mapping_tahun DESC LIMIT 1";
$tahun_result = $conn->query($tahun_query);
$nama_tahun = '';
if ($tahun_result && $tahun_result->num_rows > 0) {
    $tahun_row = $tahun_result->fetch_assoc();
    $nama_tahun = $tahun_row['nama_tahun'];
}



// Main query dengan struktur yang sama seperti modul visitasi
$sql = "SELECT
            mpv.id_mapping, mpv.tahap, mpv.tahun_akreditasi,
            s.sekolah_id, s.npsn, s.nama_sekolah, s.nama_kepsek, s.no_hp_kepsek,
            j.nm_jenjang, kk.nm_kota,
            mpv.kd_asesor1, mpv.kd_asesor2,
            a1.nia1, a1.nm_asesor1, a1.no_hp as hp1, kk1.nm_kota as kota1,
            a2.nia2, a2.nm_asesor2, a2.no_hp as hp2, kk2.nm_kota as kota2,
            mpv.file_penjelasan_hasil_akreditasi,
            mpv.nama_asesor_kpa, mpv.catatan_penilaian_asesor_kpa,
            mpv.nama_asesor_visitasi_a, mpv.catatan_penilaian_asesor_visitasi_a,
            mpv.nama_asesor_visitasi_b, mpv.catatan_penilaian_asesor_visitasi_b,
            mpv.nama_validator, mpv.nilai_validasi, mpv.catatan_penilaian_validator
        FROM mapping_paud_validasi mpv
        LEFT JOIN sekolah s ON mpv.sekolah_id = s.sekolah_id
        LEFT JOIN jenjang j ON s.jenjang_id = j.id_jenjang
        LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
        LEFT JOIN asesor_1 a1 ON mpv.kd_asesor1 = a1.kd_asesor1
        LEFT JOIN kab_kota kk1 ON a1.kota_id1 = kk1.kota_id
        LEFT JOIN asesor_2 a2 ON mpv.kd_asesor2 = a2.kd_asesor2
        LEFT JOIN kab_kota kk2 ON a2.kota_id2 = kk2.kota_id
        WHERE (mpv.kd_asesor1 = '$kd_user' OR mpv.kd_asesor2 = '$kd_user')
            AND mpv.tahun_akreditasi = '$nama_tahun'
            AND mpv.provinsi_id = '$provinsi_id'
        ORDER BY mpv.id_mapping DESC";

$result = $conn->query($sql);


?>

<!-- Include header -->
<?php include '../header.php'; ?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Mapping Validasi PAUD</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../dashboard/dashboard.php">Home</a></li>
                        <li class="breadcrumb-item active">Mapping Validasi PAUD</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-clipboard-check mr-2"></i>
                                Data Mapping Validasi PAUD - Tahun <?php echo $nama_tahun; ?>
                            </h3>
                            <div class="card-tools">
                                <span class="badge badge-info">
                                    Total: <?php echo $result ? $result->num_rows : 0; ?> data
                                </span>
                            </div>
                        </div>
                        
                        <div class="card-body">


                            <?php if ($result && $result->num_rows > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped table-hover" style="font-size: 13px;">
                                        <thead class="bg-primary text-white">
                                            <tr>
                                                <th style="width: 3%;" class="text-center">NO <br> &nbsp; </th>
                                                <th style="width: 25%;" class="text-center">SEKOLAH <br> &nbsp; </th>
                                                <th style="width: 20%;" class="text-center">ASESOR <br> &nbsp; </th>
                                                <th style="width: 25%;" class="text-center">FORM UPLOAD DOKUMEN <br> &nbsp; </th>
                                                <th style="width: 27%;" class="text-center">DOKUMEN PENJELASAN HASIL <br> AKREDITASI (PHA)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $nomor = 0;
                                            while($row = $result->fetch_assoc()): 
                                                $nomor++;
                                                $id_mapping = $row['id_mapping'];
                                                $asesor1 = $row['kd_asesor1'];
                                                $asesor2 = $row['kd_asesor2'];
                                                $file_penjelasan_hasil_akreditasi = $row['file_penjelasan_hasil_akreditasi'];
                                                $nama_asesor_kpa = $row['nama_asesor_kpa'] ?? '';
                                                $catatan_penilaian_asesor_kpa = $row['catatan_penilaian_asesor_kpa'] ?? '';
                                                $nama_asesor_visitasi_a = $row['nama_asesor_visitasi_a'] ?? '';
                                                $catatan_penilaian_asesor_visitasi_a = $row['catatan_penilaian_asesor_visitasi_a'] ?? '';
                                                $nama_asesor_visitasi_b = $row['nama_asesor_visitasi_b'] ?? '';
                                                $catatan_penilaian_asesor_visitasi_b = $row['catatan_penilaian_asesor_visitasi_b'] ?? '';
                                                $nama_validator = $row['nama_validator'] ?? '';
                                                $nilai_validasi = $row['nilai_validasi'] ?? '';
                                                $catatan_penilaian_validator = $row['catatan_penilaian_validator'] ?? '';
                                            ?>
                                            <tr>
                                                <!-- NO -->
                                                <td class="text-center align-middle"><?php echo $nomor; ?></td>
                                                
                                                <!-- SEKOLAH -->
                                                <td class="align-middle">
                                                    <div style="line-height: 1.4;">
                                                        <strong>NPSN:</strong> <?php echo $row['npsn']; ?><br>
                                                        <strong>NAMA:</strong> <?php echo $row['nama_sekolah']; ?><br>
                                                        <strong>JENJANG:</strong> <?php echo $row['nm_jenjang']; ?><br>
                                                        <strong>KAB/KOTA:</strong> <?php echo $row['nm_kota']; ?><br>
                                                        <strong>NAMA KEPSEK:</strong> <?php echo $row['nama_kepsek']; ?><br>
                                                        <strong>NO HP KEPSEK:</strong> <?php echo $row['no_hp_kepsek']; ?><br>
                                                        <strong>TAHAP VALIDASI:</strong> <?php echo $row['tahap']; ?>
                                                    </div>
                                                </td>
                                                
                                                <!-- ASESOR -->
                                                <td class="align-middle">
                                                    <div style="line-height: 1.4;">
                                                        <span class="badge badge-primary" style="font-size: 11px; margin-bottom: 5px;">VALIDATOR</span><br>
                                                        <strong>NIA:</strong> <?php echo $row['nia1']; ?><br>
                                                        <strong>NAMA:</strong> <?php echo $row['nm_asesor1']; ?><br>
                                                        <strong>KAB/KOTA:</strong> <?php echo $row['kota1']; ?><br>
                                                        <strong>NO HP:</strong> <?php echo $row['hp1'] ?? '-'; ?><br><br>

                                                        <span class="badge badge-success" style="font-size: 11px; margin-bottom: 5px;">VERIFIKATOR</span><br>
                                                        <strong>NIA:</strong> <?php echo $row['nia2']; ?><br>
                                                        <strong>NAMA:</strong> <?php echo $row['nm_asesor2']; ?><br>
                                                        <strong>KAB/KOTA:</strong> <?php echo $row['kota2']; ?><br>
                                                        <strong>NO HP:</strong> <?php echo $row['hp2'] ?? '-'; ?>
                                                    </div>
                                                </td>

                                                <!-- FORM UPLOAD DOKUMEN -->
                                                <td class="align-middle">
                                                    <div style="line-height: 1.6;">
                                                        <?php if ($kd_user == $asesor1): ?>
                                                            <button type="button" class="btn btn-primary btn-sm btn-block mb-2"
                                                                    onclick="uploadFilePHA('<?php echo $id_mapping; ?>')">
                                                                <i class="fas fa-upload"></i> Upload File PHA
                                                            </button>

                                                            <button type="button" class="btn btn-success btn-sm btn-block mb-2"
                                                                    data-id-mapping="<?php echo $id_mapping; ?>"
                                                                    data-nama-asesor="<?php echo htmlspecialchars($nama_asesor_kpa, ENT_QUOTES, 'UTF-8'); ?>"
                                                                    data-catatan-penilaian="<?php echo htmlspecialchars($catatan_penilaian_asesor_kpa, ENT_QUOTES, 'UTF-8'); ?>"
                                                                    onclick="formCatatanKPA(this)">
                                                                <i class="fas fa-edit"></i> Form Catatan Asesor KPA
                                                            </button>

                                                            <button type="button" class="btn btn-info btn-sm btn-block mb-2"
                                                                    data-id-mapping="<?php echo $id_mapping; ?>"
                                                                    data-nama-asesor="<?php echo htmlspecialchars($nama_asesor_visitasi_a, ENT_QUOTES, 'UTF-8'); ?>"
                                                                    data-catatan-penilaian="<?php echo htmlspecialchars($catatan_penilaian_asesor_visitasi_a, ENT_QUOTES, 'UTF-8'); ?>"
                                                                    onclick="formCatatanVisitasiA(this)">
                                                                <i class="fas fa-edit"></i> Form Catatan Visitasi A
                                                            </button>

                                                            <button type="button" class="btn btn-warning btn-sm btn-block mb-2"
                                                                    data-id-mapping="<?php echo $id_mapping; ?>"
                                                                    data-nama-asesor="<?php echo htmlspecialchars($nama_asesor_visitasi_b, ENT_QUOTES, 'UTF-8'); ?>"
                                                                    data-catatan-penilaian="<?php echo htmlspecialchars($catatan_penilaian_asesor_visitasi_b, ENT_QUOTES, 'UTF-8'); ?>"
                                                                    onclick="formCatatanVisitasiB(this)">
                                                                <i class="fas fa-edit"></i> Form Catatan Visitasi B
                                                            </button>
                                                        <?php endif; ?>

                                                        <?php if ($kd_user == $asesor2): ?>
                                                            <button type="button" class="btn btn-secondary btn-sm btn-block mb-2"
                                                                    data-id-mapping="<?php echo $id_mapping; ?>"
                                                                    data-nama-validator="<?php echo htmlspecialchars($nama_validator, ENT_QUOTES, 'UTF-8'); ?>"
                                                                    data-nilai-validasi="<?php echo htmlspecialchars($nilai_validasi, ENT_QUOTES, 'UTF-8'); ?>"
                                                                    data-catatan-penilaian="<?php echo htmlspecialchars($catatan_penilaian_validator, ENT_QUOTES, 'UTF-8'); ?>"
                                                                    onclick="formCatatanValidator(this)">
                                                                <i class="fas fa-edit"></i> Form Catatan Validator
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>

                                                <!-- DOKUMEN PHA -->
                                                <td class="align-middle text-center">
                                                    <div style="line-height: 1.4;">
                                                        <strong>File Penjelasan Hasil Akreditasi (PHA):</strong><br><br>
                                                        <?php if (!empty($file_penjelasan_hasil_akreditasi)): ?>
                                                            <span class="badge badge-success" style="cursor: pointer; padding: 8px 12px; font-size: 12px;"
                                                                  onclick="previewFile('<?php echo $file_penjelasan_hasil_akreditasi; ?>')">
                                                                <i class="fas fa-eye"></i> Sudah Upload
                                                            </span>
                                                        <?php else: ?>
                                                            <span class="badge badge-danger" style="padding: 8px 12px; font-size: 12px;">
                                                                <i class="fas fa-times"></i> Belum Upload
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning text-center">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <?php if (empty($nama_tahun)): ?>
                                        Belum ada tahun akreditasi yang diset untuk provinsi ini. Silakan hubungi Tim IT untuk mengatur tahun akreditasi.
                                    <?php else: ?>
                                        Tidak ada data mapping validasi PAUD untuk tahun <?php echo $nama_tahun; ?> yang melibatkan Anda sebagai asesor.
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Modal Upload File PHA -->
<div class="modal fade" id="modalUploadPHA" tabindex="-1" role="dialog" aria-labelledby="modalUploadPHALabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="modalUploadPHALabel">
                    <i class="fas fa-upload"></i> Upload File Penjelasan Hasil Akreditasi (PHA)
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="formUploadPHA" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="upload_id_mapping" name="id_mapping">

                    <div class="form-group">
                        <label for="file_penjelasan_hasil_akreditasi">Pilih File PDF:</label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" id="file_penjelasan_hasil_akreditasi" name="file_penjelasan_hasil_akreditasi" accept=".pdf" required>
                            <label class="custom-file-label" for="file_penjelasan_hasil_akreditasi">Pilih file...</label>
                        </div>
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle"></i> Format file harus PDF, maksimal 10MB
                        </small>
                    </div>

                    <!-- Progress Bar -->
                    <div class="progress mb-3" id="uploadProgress" style="display: none;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             id="progressBar" role="progressbar" style="width: 0%">
                            <span id="progressText">0%</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-primary" id="btnUploadPHA">
                        <i class="fas fa-upload"></i> Upload File
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Form Catatan Asesor KPA -->
<div class="modal fade" id="modalCatatanKPA" tabindex="-1" role="dialog" aria-labelledby="modalCatatanKPALabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="modalCatatanKPALabel">
                    <i class="fas fa-edit"></i> Catatan Penilaian Terhadap Asesor KPA
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="formCatatanKPA">
                <div class="modal-body">
                    <input type="hidden" id="kpa_id_mapping" name="id_mapping">

                    <div class="form-group">
                        <label for="nama_asesor_kpa">Nama Asesor KPA:</label>
                        <input type="text" class="form-control" id="nama_asesor_kpa" name="nama_asesor_kpa"
                               placeholder="Masukkan nama asesor KPA">
                    </div>

                    <div class="form-group">
                        <label for="catatan_penilaian_asesor_kpa">Catatan Penilaian Asesor KPA:</label>
                        <textarea class="form-control" id="catatan_penilaian_asesor_kpa" name="catatan_penilaian_asesor_kpa"
                                  rows="5" placeholder="Masukkan catatan penilaian terhadap asesor KPA"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-success" id="btnSimpanKPA">
                        <i class="fas fa-save"></i> Simpan Catatan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Form Catatan Asesor Visitasi A -->
<div class="modal fade" id="modalCatatanVisitasiA" tabindex="-1" role="dialog" aria-labelledby="modalCatatanVisitasiALabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="modalCatatanVisitasiALabel">
                    <i class="fas fa-edit"></i> Catatan Penilaian Asesor Visitasi A
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="formCatatanVisitasiA">
                <div class="modal-body">
                    <input type="hidden" id="visitasi_a_id_mapping" name="id_mapping">

                    <div class="form-group">
                        <label for="nama_asesor_visitasi_a">Nama Asesor Visitasi A:</label>
                        <input type="text" class="form-control" id="nama_asesor_visitasi_a" name="nama_asesor_visitasi_a"
                               placeholder="Masukkan nama asesor visitasi A">
                    </div>

                    <div class="form-group">
                        <label for="catatan_penilaian_asesor_visitasi_a">Catatan Penilaian Asesor Visitasi A:</label>
                        <textarea class="form-control" id="catatan_penilaian_asesor_visitasi_a" name="catatan_penilaian_asesor_visitasi_a"
                                  rows="5" placeholder="Masukkan catatan penilaian terhadap asesor visitasi A"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-info" id="btnSimpanVisitasiA">
                        <i class="fas fa-save"></i> Simpan Catatan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Form Catatan Asesor Visitasi B -->
<div class="modal fade" id="modalCatatanVisitasiB" tabindex="-1" role="dialog" aria-labelledby="modalCatatanVisitasiBLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="modalCatatanVisitasiBLabel">
                    <i class="fas fa-edit"></i> Catatan Penilaian Asesor Visitasi B
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="formCatatanVisitasiB">
                <div class="modal-body">
                    <input type="hidden" id="visitasi_b_id_mapping" name="id_mapping">

                    <div class="form-group">
                        <label for="nama_asesor_visitasi_b">Nama Asesor Visitasi B:</label>
                        <input type="text" class="form-control" id="nama_asesor_visitasi_b" name="nama_asesor_visitasi_b"
                               placeholder="Masukkan nama asesor visitasi B">
                    </div>

                    <div class="form-group">
                        <label for="catatan_penilaian_asesor_visitasi_b">Catatan Penilaian Asesor Visitasi B:</label>
                        <textarea class="form-control" id="catatan_penilaian_asesor_visitasi_b" name="catatan_penilaian_asesor_visitasi_b"
                                  rows="5" placeholder="Masukkan catatan penilaian terhadap asesor visitasi B"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-warning" id="btnSimpanVisitasiB">
                        <i class="fas fa-save"></i> Simpan Catatan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Form Catatan Validator -->
<div class="modal fade" id="modalCatatanValidator" tabindex="-1" role="dialog" aria-labelledby="modalCatatanValidatorLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-secondary text-white">
                <h5 class="modal-title" id="modalCatatanValidatorLabel">
                    <i class="fas fa-edit"></i> Catatan Penilaian Terhadap Validator
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="formCatatanValidator">
                <div class="modal-body">
                    <input type="hidden" id="validator_id_mapping" name="id_mapping">

                    <div class="form-group">
                        <label for="nama_validator">Nama Validator:</label>
                        <input type="text" class="form-control" id="nama_validator" name="nama_validator"
                               placeholder="Masukkan nama validator">
                    </div>

                    <div class="form-group">
                        <label for="nilai_validasi">Nilai Validasi:</label>
                        <input type="text" class="form-control" id="nilai_validasi" name="nilai_validasi"
                               placeholder="Masukkan nilai validasi">
                    </div>

                    <div class="form-group">
                        <label for="catatan_penilaian_validator">Catatan Penilaian Validator:</label>
                        <textarea class="form-control" id="catatan_penilaian_validator" name="catatan_penilaian_validator"
                                  rows="5" placeholder="Masukkan catatan penilaian validator"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-secondary" id="btnSimpanValidator">
                        <i class="fas fa-save"></i> Simpan Catatan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Include footer -->
<?php include '../footer.php'; ?>

<script src="js/validasi.js" defer></script>
