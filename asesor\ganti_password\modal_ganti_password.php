<!-- Modal Ganti Password -->
<div class="modal fade" id="modal-ganti-password" tabindex="-1" role="dialog" aria-labelledby="modal-ganti-password-label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="modal-ganti-password-label">
                    <i class="fas fa-key mr-2"></i>Ganti Password Baru
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-ganti-password" novalidate onsubmit="return false;">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="new_password">
                            <i class="fas fa-lock mr-1"></i>Password Baru <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="new_password" name="new_password"
                                   placeholder="Masukkan password baru" required>
                            <div class="input-group-append">
                                <button type="button" class="btn btn-outline-secondary" id="toggle-password">
                                    <i class="fas fa-eye" id="toggle-icon"></i>
                                </button>
                            </div>
                        </div>
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle mr-1"></i>
                            <!-- Password akan disimpan untuk keperluan administrasi -->
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times mr-1"></i>Batal
                    </button>
                    <button type="button" class="btn btn-primary" id="btn-update-password" onclick="updatePassword()">
                        <i class="fas fa-save mr-1"></i>Simpan Perubahan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Notifikasi Sukses -->
<div class="modal fade" id="modal-notifikasi-sukses" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                <h5 class="text-success">Berhasil!</h5>
                <p id="notifikasi-sukses-message"></p>
                <button type="button" class="btn btn-success" data-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Notifikasi Error -->
<div class="modal fade" id="modal-notifikasi-error" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <i class="fas fa-times-circle fa-3x text-danger mb-3"></i>
                <h5 class="text-danger">Error!</h5>
                <p id="notifikasi-error-message"></p>
                <button type="button" class="btn btn-danger" data-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>

<!-- Simple function definitions -->
<script>
function showGantiPasswordModal() {
    $('#form-ganti-password')[0].reset();
    $('#modal-ganti-password').modal('show');
}

function updatePassword() {
    var formData = $('#form-ganti-password').serialize();

    // Disable submit button
    $('#btn-update-password').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>Menyimpan...');

    $.ajax({
        url: '../ganti_password/ajax/update_password.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Hide modal
                $('#modal-ganti-password').modal('hide');

                // Show success notification
                showNotification('success', response.message);
            } else {
                // Show error notification
                showNotification('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            var errorMessage = 'Terjadi kesalahan saat mengubah password';

            try {
                var errorResponse = JSON.parse(xhr.responseText);
                errorMessage = errorResponse.message || errorMessage;
            } catch (e) {
                // Use default error message
            }

            showNotification('error', errorMessage);
        },
        complete: function() {
            // Enable submit button
            $('#btn-update-password').prop('disabled', false).html('<i class="fas fa-save mr-1"></i>Simpan Perubahan');
        }
    });
}

function showNotification(type, message) {
    if (type === 'success') {
        $('#notifikasi-sukses-message').text(message);
        $('#modal-notifikasi-sukses').modal('show');

        // Auto close after 3 seconds
        setTimeout(function() {
            $('#modal-notifikasi-sukses').modal('hide');
        }, 3000);
    } else if (type === 'error') {
        $('#notifikasi-error-message').text(message);
        $('#modal-notifikasi-error').modal('show');
    }
}
</script>

<!-- Include JavaScript for additional features -->
<script src="ganti_password/js/ganti_password.js"></script>
