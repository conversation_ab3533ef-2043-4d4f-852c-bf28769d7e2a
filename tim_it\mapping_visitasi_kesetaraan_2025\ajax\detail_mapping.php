<?php
/**
 * AJAX handler untuk detail mapping visitasi SM
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

header('Content-Type: application/json');

try {
    $id_mapping = intval($_GET['id_mapping'] ?? 0);
    
    if ($id_mapping <= 0) {
        echo json_encode([
            'success' => false,
            'message' => 'ID mapping tidak valid'
        ]);
        exit;
    }

    // Query detail dengan JOIN semua tabel yang diperlukan
    $query = "SELECT 
                m.*,
                s.npsn, s.nama_sekolah, s.nama_kepsek, s.no_hp_kepsek, s.no_wa_kepsek,
                j.nm_jenjang,
                k1.nm_kota as kota_sekolah,
                a1.nia1, a1.nm_asesor1, a1.no_hp as hp_asesor1,
                k2.nm_kota as kota_asesor1,
                a2.nia2, a2.nm_asesor2, a2.no_hp as hp_asesor2,
                k3.nm_kota as kota_asesor2
              FROM mapping_2025 m
              LEFT JOIN sekolah s ON m.sekolah_id = s.sekolah_id
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              LEFT JOIN kab_kota k1 ON s.kota_id = k1.kota_id
              LEFT JOIN asesor_1 a1 ON m.kd_asesor1 = a1.kd_asesor1
              LEFT JOIN kab_kota k2 ON a1.kota_id1 = k2.kota_id
              LEFT JOIN asesor_2 a2 ON m.kd_asesor2 = a2.kd_asesor2
              LEFT JOIN kab_kota k3 ON a2.kota_id2 = k3.kota_id
              WHERE m.id_mapping = $id_mapping";

    $result = $conn->query($query);

    if (!$result) {
        echo json_encode([
            'success' => false,
            'message' => 'Query error: ' . $conn->error
        ]);
        exit;
    }

    if ($result->num_rows == 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Data mapping tidak ditemukan'
        ]);
        exit;
    }

    $data = $result->fetch_assoc();

    // Format tanggal untuk tampilan
    $formatTanggal = function($tanggal) {
        if (empty($tanggal) || $tanggal == '0000-00-00') {
            return '-';
        }
        return date('d/m/Y', strtotime($tanggal));
    };

    // Prepare response data
    $response = [
        'success' => true,
        'data' => [
            // Data Sekolah
            'sekolah' => [
                'npsn' => $data['npsn'] ?? '-',
                'nama_sekolah' => $data['nama_sekolah'] ?? '-',
                'jenjang' => $data['nm_jenjang'] ?? '-',
                'kota_sekolah' => $data['kota_sekolah'] ?? '-',
                'nama_kepsek' => $data['nama_kepsek'] ?? '-',
                'hp_kepsek' => $data['no_hp_kepsek'] ?? '-',
                'wa_kepsek' => $data['no_wa_kepsek'] ?? '-'
            ],
            
            // Data Asesor
            'asesor' => [
                'asesor1' => [
                    'nia' => $data['nia1'] ?? '-',
                    'nama' => $data['nm_asesor1'] ?? '-',
                    'hp' => $data['hp_asesor1'] ?? '-',
                    'kota' => $data['kota_asesor1'] ?? '-'
                ],
                'asesor2' => [
                    'nia' => $data['nia2'] ?? '-',
                    'nama' => $data['nm_asesor2'] ?? '-',
                    'hp' => $data['hp_asesor2'] ?? '-',
                    'kota' => $data['kota_asesor2'] ?? '-'
                ]
            ],
            
            // Status File Upload dengan nama file
            'files' => [
                'file_format_3_1_hasil_penilaian_pra_visitasi_1' => [
                    'uploaded' => !empty($data['file_format_3_1_hasil_penilaian_pra_visitasi_1']),
                    'filename' => $data['file_format_3_1_hasil_penilaian_pra_visitasi_1'] ?? '',
                    'directory' => 'upload_file_format_3_1_hasil_penilaian_pra_visitasi_1'
                ],
                'file_format_3_1_hasil_penilaian_pra_visitasi_2' => [
                    'uploaded' => !empty($data['file_format_3_1_hasil_penilaian_pra_visitasi_2']),
                    'filename' => $data['file_format_3_1_hasil_penilaian_pra_visitasi_2'] ?? '',
                    'directory' => 'upload_file_format_3_1_hasil_penilaian_pra_visitasi_2'
                ],
                'file_format_3_2_lk_penggalian_data_pra_visitasi_1' => [
                    'uploaded' => !empty($data['file_format_3_2_lk_penggalian_data_pra_visitasi_1']),
                    'filename' => $data['file_format_3_2_lk_penggalian_data_pra_visitasi_1'] ?? '',
                    'directory' => 'upload_file_format_3_2_lk_penggalian_data_pra_visitasi_1'
                ],
                'file_format_3_2_lk_penggalian_data_pra_visitasi_2' => [
                    'uploaded' => !empty($data['file_format_3_2_lk_penggalian_data_pra_visitasi_2']),
                    'filename' => $data['file_format_3_2_lk_penggalian_data_pra_visitasi_2'] ?? '',
                    'directory' => 'upload_file_format_3_2_lk_penggalian_data_pra_visitasi_2'
                ],
                'file_format_4_1_surat_tugas_visitasi' => [
                    'uploaded' => !empty($data['file_format_4_1_surat_tugas_visitasi']),
                    'filename' => $data['file_format_4_1_surat_tugas_visitasi'] ?? '',
                    'directory' => 'upload_file_format_4_1_surat_tugas_visitasi'
                ],
                'file_format_4_2_pakta_integritas_1' => [
                    'uploaded' => !empty($data['file_format_4_2_pakta_integritas_1']),
                    'filename' => $data['file_format_4_2_pakta_integritas_1'] ?? '',
                    'directory' => 'upload_file_format_4_2_pakta_integritas_1'
                ],
                'file_format_4_2_pakta_integritas_2' => [
                    'uploaded' => !empty($data['file_format_4_2_pakta_integritas_2']),
                    'filename' => $data['file_format_4_2_pakta_integritas_2'] ?? '',
                    'directory' => 'upload_file_format_4_2_pakta_integritas_2'
                ],
                'file_format_4_3_lembar_rekap_penggalian_data_penilaian_1' => [
                    'uploaded' => !empty($data['file_format_4_3_lembar_rekap_penggalian_data_penilaian_1']),
                    'filename' => $data['file_format_4_3_lembar_rekap_penggalian_data_penilaian_1'] ?? '',
                    'directory' => 'upload_file_format_4_3_lembar_rekap_penggalian_data_penilaian_1'
                ],
                'file_format_4_3_lembar_rekap_penggalian_data_penilaian_2' => [
                    'uploaded' => !empty($data['file_format_4_3_lembar_rekap_penggalian_data_penilaian_2']),
                    'filename' => $data['file_format_4_3_lembar_rekap_penggalian_data_penilaian_2'] ?? '',
                    'directory' => 'upload_file_format_4_3_lembar_rekap_penggalian_data_penilaian_2'
                ],
                'file_format_4_4_berita_acara_visitasi' => [
                    'uploaded' => !empty($data['file_format_4_4_berita_acara_visitasi']),
                    'filename' => $data['file_format_4_4_berita_acara_visitasi'] ?? '',
                    'directory' => 'upload_file_format_4_4_berita_acara_visitasi'
                ],
                'file_format_4_5_laporan_individu_1' => [
                    'uploaded' => !empty($data['file_format_4_5_laporan_individu_1']),
                    'filename' => $data['file_format_4_5_laporan_individu_1'] ?? '',
                    'directory' => 'upload_file_format_4_5_laporan_individu_1'
                ],
                'file_format_4_5_laporan_individu_2' => [
                    'uploaded' => !empty($data['file_format_4_5_laporan_individu_2']),
                    'filename' => $data['file_format_4_5_laporan_individu_2'] ?? '',
                    'directory' => 'upload_file_format_4_5_laporan_individu_2'
                ],
                'file_format_4_5_laporan_kelompok' => [
                    'uploaded' => !empty($data['file_format_4_5_laporan_kelompok']),
                    'filename' => $data['file_format_4_5_laporan_kelompok'] ?? '',
                    'directory' => 'upload_file_format_4_5_laporan_kelompok'
                ],
                'file_format_4_5_catatan_dan_saran' => [
                    'uploaded' => !empty($data['file_format_4_5_catatan_dan_saran']),
                    'filename' => $data['file_format_4_5_catatan_dan_saran'] ?? '',
                    'directory' => 'upload_file_format_4_5_catatan_dan_saran'
                ],
                'file_foto_visitasi_2025' => [
                    'uploaded' => !empty($data['file_foto_visitasi_2025']),
                    'filename' => $data['file_foto_visitasi_2025'] ?? '',
                    'directory' => 'upload_file_foto_visitasi_2025'
                ]
            ],
            
            // Pelaksanaan Kegiatan
            'kegiatan' => [
                'tgl_pra_visitasi' => $formatTanggal($data['tgl_pra_visitasi']),
                'no_st_pra_visitasi' => $data['no_surat_tugas_pra_visitasi'] ?? '-',
                'tgl_st_pra_visitasi' => $formatTanggal($data['tgl_surat_tugas_pra_visitasi']),
                'tgl_mulai_visitasi' => $formatTanggal($data['tgl_mulai_visitasi']),
                'tgl_akhir_visitasi' => $formatTanggal($data['tgl_akhir_visitasi']),
                'no_st_visitasi' => $data['no_surat_tugas_visitasi'] ?? '-',
                'tgl_st_visitasi' => $formatTanggal($data['tgl_surat_tugas_visitasi']),
                'tahap' => $data['tahap'] ?? '-'
            ],
            
            // ID untuk aksi
            'id_mapping' => $data['id_mapping']
        ]
    ];

    echo json_encode($response);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
